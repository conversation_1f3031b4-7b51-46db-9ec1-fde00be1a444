{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^4.9.0", "@nestjs-modules/mailer": "^1.9.1", "@nestjs/apollo": "^12.0.7", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/devtools-integration": "^0.1.6", "@nestjs/graphql": "^12.0.8", "@nestjs/jwt": "^10.1.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.2.7", "@nestjs/platform-ws": "^10.2.7", "@nestjs/schedule": "^3.0.3", "@nestjs/serve-static": "^4.0.0", "@nestjs/throttler": "^5.0.1", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.2.7", "@types/nodemailer": "^6.4.9", "apollo-server-core": "^3.12.0", "apollo-server-express": "^3.12.1", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "decimal.js": "^10.4.3", "graphql": "^16.7.1", "graphql-tools": "^9.0.0", "graphql-upload": "14.0.0", "handlebars": "^4.7.8", "hyperformula": "^2.5.0", "mathjs": "^12.2.1", "mysql2": "^3.5.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.4", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "typeorm-transactional": "^0.4.1", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/graphql-upload": "8.0.12", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}