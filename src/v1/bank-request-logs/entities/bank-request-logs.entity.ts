import { Field, ObjectType } from "@nestjs/graphql";
import { IsUUID } from "class-validator";
import { AuditableEntity } from "src/config/auditable-entity.config";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

export enum EnumBankName {
    BNB = 'BNB',
}

export enum EnumTypeLog {
    REQUEST = 'REQUEST',
    RESPONSE = 'RESPONSE',
    ERROR = 'ERROR',
}

@ObjectType()
@Entity()
export class BankRequestLog extends AuditableEntity{

    @IsUUID('4', { message: 'Invalid UUID format' })
    @PrimaryGeneratedColumn('uuid')
    @Field(() => String, { nullable: true })
    id: string;

    @Field({nullable: true})
    @Column( {type: 'enum', enum: EnumBankName , nullable:false} )
    bank_name:  EnumBankName;

    @Field({nullable: true})
    @Column()
    api_endpoint: string;

    @Field({nullable: true})
    @Column( {type: 'enum', enum: EnumTypeLog , nullable:false})
    type_log: EnumTypeLog;

    @Field({nullable: true})
    @Column({type: 'text', default: null})
    show_message: string;

    @Field({nullable: true})
    @Column({type: 'text', default: null})
    data: string;

}