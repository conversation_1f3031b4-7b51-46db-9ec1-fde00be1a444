import { Module } from '@nestjs/common';
import { BankRequestLogsService } from './bank-request-logs.service';
import { BankRequestLogsResolver } from './bank-request-logs.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BankRequestLog } from './entities/bank-request-logs.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BankRequestLog
    ])
  ],
  providers: [BankRequestLogsService, BankRequestLogsResolver],
  exports:[BankRequestLogsService]
})
export class BankRequestLogsModule {}
