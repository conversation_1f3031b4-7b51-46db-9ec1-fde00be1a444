import { Test, TestingModule } from '@nestjs/testing';
import { BankRequestLogsResolver } from './bank-request-logs.resolver';

describe('BankRequestLogsResolver', () => {
  let resolver: BankRequestLogsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BankRequestLogsResolver],
    }).compile();

    resolver = module.get<BankRequestLogsResolver>(BankRequestLogsResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
