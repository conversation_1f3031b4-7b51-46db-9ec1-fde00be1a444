import { Injectable } from '@nestjs/common';
import { BankRequestLog } from './entities/bank-request-logs.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertBankRequestLogInput } from './dto/bank-request-log.input';

@Injectable()
export class BankRequestLogsService {

    constructor(
        @InjectRepository(BankRequestLog)
        private readonly bankRequestLogRepository: Repository<BankRequestLog>,
    ) { }

    async insertBankRequestLog(bankRequestLog: InsertBankRequestLogInput) {
        const bankRequesE= this.bankRequestLogRepository.create(bankRequestLog);
        return await this.bankRequestLogRepository.save(bankRequesE);
    }

}
