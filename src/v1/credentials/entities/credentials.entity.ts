import { ObjectType, Field, Int } from '@nestjs/graphql';
import { IsUUID, IsNotEmpty } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { People } from './../../peoples/entities/people.entity';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  Unique,
} from 'typeorm';
import * as bcrypt from 'bcrypt';

export interface Payload {
  sub: string;
  name: string;
  lastName: string;
  roles: string[];
}

@Entity()
@Unique(['people'])
@ObjectType()
export class Credentials extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsNotEmpty({ message: 'Password cannot be empty' })
  password?: string;

  @OneToOne(() => People, (people) => people.credential)
  @JoinColumn()
  people: People;

  @RelationId((credential: Credentials) => credential.people)
  peopleId: string;

  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }
}
