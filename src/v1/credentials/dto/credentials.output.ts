import { IsUUID, IsEmail, IsBoolean } from 'class-validator';
import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UserOutput {
  @Field()
  @IsUUID('4', { message: 'Invalid UUID format for id' })
  id: string;

  @Field()
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @Field(() => Boolean)
  @IsBoolean({ message: 'EmailVerified must be a boolean value' })
  emailVerified: boolean;

  @Field()
  @IsUUID('4', { message: 'Invalid UUID format for peopleId' })
  peopleId: string;
}
