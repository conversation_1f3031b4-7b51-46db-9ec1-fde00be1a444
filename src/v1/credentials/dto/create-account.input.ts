import { Field, InputType } from '@nestjs/graphql';
import { Matches, MinLength, IsEmail, IsOptional } from 'class-validator';

@InputType()
export class PeopleInputCreate {
  @Field()
  name: string;

  @Field()
  lastName: string;

  @Field()
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  birthDate?: Date;

  @Field({ nullable: true })
  photoProfile?: string;

  @Field({ nullable: true })
  cellPhone?: string;

  @Field(() => String, { nullable: true })
  regionCode?: string;

  @Field({ nullable: true })
  academicLevel?: string;

  @Field({ nullable: true })
  country?: string;

  @Field({ nullable: true })
  city?: string;

  @Field({ nullable: true })
  companyInstitution?: string;

  @Field({ nullable: true })
  studentProfessional?: string;
}

@InputType()
export class CreateUserInputAccount {
  @Field({ nullable: true })
  @IsOptional()
  @MinLength(6, { message: 'Password must have at least 6 characters' })
  @Matches(/^(?=.*\d)(?=.*[A-Z])(?=.*[\W_]).*$/, {
    message:
      'Password must have at least 6 characters, 1 number, 1 uppercase letter, and 1 special character.',
  })
  password?: string;

  @Field()
  people: PeopleInputCreate;
}
