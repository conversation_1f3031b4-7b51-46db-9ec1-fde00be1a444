import { Modu<PERSON> } from '@nestjs/common';
import { CredentialsService } from './credentials.service';
import { CredentialsResolver } from './credentials.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Credentials } from './entities/credentials.entity';

import { AuthModule } from './../../jwt/auth.module';

import { MailModule } from './../../mail/mail.module';
import { PeoplesModule } from './../peoples/peoples.module';

import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { People } from '../peoples/entities/people.entity';
import { Admin } from '../admins/entities/admin.entity';
import { AdminsModule } from '../admins/admins.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Credentials, People, Admin]),
    AuthModule,
    MailModule,
    PeoplesModule,
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET_KEY,
      signOptions: { expiresIn: '10m' },
    }),
    ScheduleModule.forRoot(),
    AdminsModule,
  ],
  providers: [CredentialsResolver, CredentialsService],
  exports: [CredentialsService],
})
export class CredentialsModule {}
