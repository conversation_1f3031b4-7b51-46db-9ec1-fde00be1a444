import { Injectable, UnauthorizedException } from '@nestjs/common';
import { CreateCredentialsInput } from './dto/create-credentials.input';
import { UserOutput } from './dto/credentials.output';
import { Credentials, Payload } from './entities/credentials.entity';
import { Connection, IsNull, Repository, getConnection } from 'typeorm';
import { InjectConnection, InjectRepository } from '@nestjs/typeorm';

import { v4 as uuidv4 } from 'uuid';
import { People } from '../peoples/entities/people.entity';
import { CreateUserInputAccount } from './dto/create-account.input';

import { MailService } from './../../mail/mail.service';
import { CodeVerificationInput } from './dto/verify-code.input';
import { JwtService } from '@nestjs/jwt';

import { AuthServiceJwt } from './../../jwt/auth.service';
import * as bcrypt from 'bcrypt';

import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { Admin } from '../admins/entities/admin.entity';
import { PeoplesService } from '../peoples/peoples.service';
import { AdminsService } from '../admins/admins.service';

@Injectable()
export class CredentialsService {
  constructor(
    @InjectRepository(Credentials)
    private readonly credentialRepository: Repository<Credentials>,
    // @InjectRepository(Admin)
    // private readonly adminRepository: Repository<Admin>,
    private adminsService: AdminsService,
    // @InjectRepository(People)
    // private readonly peopleRepository: Repository<People>,
    private readonly peoplesService:PeoplesService,
    private readonly mailService: MailService,
    private jwtService: JwtService,
    private authServiceJwt: AuthServiceJwt,
    @InjectConnection() private readonly connection: Connection,
  ) {}

  generateCode(): string {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789#_%$&/()?¡';
    return Array.from({ length: 6 }, () =>
      characters.charAt(Math.floor(Math.random() * characters.length)),
    ).join('');
  }

  generatePassword(): string {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789#_%$&/()?¡';
    return Array.from({ length: 10 }, () =>
      characters.charAt(Math.floor(Math.random() * characters.length)),
    ).join('');
  }

  async create(userProfile: CreateCredentialsInput): Promise<Credentials> {
    return await this.credentialRepository.save({
      id: uuidv4(),
      ...userProfile,
    });
  }

  async createAccount(dataUser: CreateUserInputAccount): Promise<boolean> {
    const queryRunner = this.connection.createQueryRunner();

    let peopleData: People = new People();
    const userData = new Credentials();

    userData.id = uuidv4();
    userData.password = dataUser.password;
    Object.assign(peopleData, dataUser.people);
    peopleData.id = uuidv4();
    peopleData.emailVerified = true;
    peopleData.codeVerification = this.generateCode();

    userData.people = peopleData;

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.save(peopleData);
      await queryRunner.manager.save(userData);
      await queryRunner.commitTransaction();

      this.mailService.sendUserVerificationCode(peopleData);
      return true;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async createAccountUserForAdmin(
    dataUser: CreateUserInputAccount,
  ): Promise<boolean> {
    const queryRunner = this.connection.createQueryRunner();

    let peopleData: People = new People();
    const userData = new Credentials();

    const newPassword = this.generatePassword();

    userData.id = uuidv4();
    userData.password = newPassword;

    Object.assign(peopleData, dataUser.people);

    peopleData.id = uuidv4();

    peopleData.emailVerified = true;
    peopleData.codeVerification = this.generateCode();

    userData.people = peopleData;

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.save(peopleData);
      await queryRunner.manager.save(userData);
      await queryRunner.commitTransaction();

      this.mailService.sendCredentials(peopleData, newPassword);

      return true;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async findPeopleId(peopleId: string): Promise<Credentials> {
    return await this.credentialRepository.findOne({
      where: {
        people: {
          id: peopleId,
        },
        deletedAt: IsNull(),
      },
    });
  }

  // async updateVerifiedEmail(id: string): Promise<Boolean> {
  //   try {
  //     await this.userRepository.update({ id }, { emailVerified: true });
  //     return true;
  //   } catch (e) {
  //     throw e;
  //   }
  // }

  // async findOne(email: string): Promise<User> {
  //   return await this.userRepository.findOne({
  //     where: {
  //       email,
  //      deletedAt: IsNull(),
  //     },
  //   });
  // }
  // async findOneId(id: string): Promise<User> {
  //   return await this.userRepository.findOne({
  //     where: {
  //       id,
  //     },
  //   });
  // }

  // async recoveryAccount(email: string): Promise<boolean> {
  //   const userData = await this.userRepository.findOne({
  //     where: {
  //       email,
  //     },
  //   });

  //   if (!userData) throw new Error('The email does not exist.');

  //   const newToken = await this.jwtService.sign({
  //     id: userData.id,
  //   });

  //   const link = `${process.env.HOST_CLIENT}/recovery_account/${newToken}`;

  //   await this.mailService.sendLinkRecoveryAccount(userData, link);

  //   return true;
  // }

  async verifyTokenRecovery(token: string): Promise<People> {
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });

      if (!payload) throw new UnauthorizedException();

      // let people = await this.peopleRepository.findOne({
      //   where: {
      //     id: payload?.id,
      //     deletedAt: IsNull(),
      //   },
      // });
      let people= await this.peoplesService.getPersona_ById(payload?.id);

      if (!people) throw new UnauthorizedException();

      return people;
    } catch {
      throw new UnauthorizedException();
    }
  }

  async updateCredentials(token: string, password): Promise<boolean> {
    const peopleData = await this.verifyTokenRecovery(token);
    if (!peopleData) throw new Error('the page has expired.');

    let newUser = await this.credentialRepository.update(
      { people: { id: peopleData.id }, deletedAt: IsNull() },
      {
        password: await bcrypt.hash(password, 10),
      },
    );

    return newUser.affected === 1;
  }

  async changePasswordCredential(
    id: string,
    oldPassword: string | null,
    newPassword: string,
  ): Promise<Boolean> {
    // const peopleData = await this.peopleRepository.findOne({
    //   where: { 
    //     id, 
    //     deletedAt: IsNull() 
    //   },
    // });

    const peopleData = await this.peoplesService.getPersona_ById(id);

    const userData = await this.credentialRepository.findOne({
      where: { people: { id: peopleData.id }, deletedAt: IsNull() },
    });

    if (!userData.password) {
      const updatePassword = await this.credentialRepository.update(
        { id: userData.id, deletedAt: IsNull() },
        {
          password: await bcrypt.hash(newPassword, 10),
        },
      );
      return updatePassword.affected === 1;
    } else {
      if (!(await bcrypt.compare(oldPassword, userData.password)))
        throw new UnauthorizedException();

      if (!userData) throw new UnauthorizedException();

      const updatePassword = await this.credentialRepository.update(
        { id: userData.id, deletedAt: IsNull() },
        {
          password: await bcrypt.hash(newPassword, 10),
        },
      );
      return updatePassword.affected === 1;
    }
  }

  async loginWithpassword(email: string, password: string): Promise<string> {
    const peopleData= await this.peoplesService.getPersonWithVerifiedEmail_ByEmail(email);
    if (!peopleData) throw new UnauthorizedException();

    const searchUser= await this.getCredential_ByPeopleId(peopleData.id);
    if (!searchUser) throw new UnauthorizedException();

    if (!(await bcrypt.compare(password, searchUser.password)))
      throw new UnauthorizedException();
    const cod_roles:string[]= ['user'];
    const payload:Payload={
      sub: peopleData.id,
      name: peopleData.name,
      lastName: peopleData.lastName,
      roles: cod_roles,
    }

    return `Bearer ${await this.authServiceJwt.createCredentials(
      // {
      //   id: peopleData.id,
      //   rol: 'user',
      // }
      payload
    )}`;
  }

  getCredential_ByPeopleId(peopleId: string): Promise<Credentials> {
    return this.credentialRepository.findOne({
      where: {
        people: { id: peopleId },
        deletedAt: IsNull(),
      },
    });
  }
  createPayload(peopleData: People, cod_roles:string[]): Payload {
    const payload: Payload = {
      sub: peopleData.id,
      name: peopleData.name,
      lastName: peopleData.lastName,
      roles: cod_roles,
    };
    return payload;
  }

  async loginWithEmailAndPassword(
    email: string,
    password: string,
  ): Promise<string> {
    const peopleData= await this.peoplesService.getPersona_ByEmail(email);
    if (!peopleData) throw new UnauthorizedException();

    const rolesPeople= await this.adminsService.getRoles_ByEmail2(email);
    if (rolesPeople.length==0) throw new UnauthorizedException();

    const codigos_roles:string[]= rolesPeople.map((rp)=>rp.cod_rol);

    const searchUser= await this.getCredential_ByPeopleId(peopleData.id);
    if (!searchUser) throw new UnauthorizedException();

    if (!(await bcrypt.compare(password, searchUser.password)))
      throw new UnauthorizedException();

    const payload:Payload= this.createPayload(peopleData, codigos_roles);

    return `Bearer ${await this.authServiceJwt.createCredentials(payload)}`;
  }

  // // @Cron(CronExpression.EVERY_30_MINUTES)
  // @Cron(CronExpression.EVERY_5_MINUTES)
  // async clearUsersNotVerified() {
  //   // const fiveMinutesAgo = new Date(Date.now() - 60 * 60 * 1000);
  //   const fiveMinutesAgo = new Date(Date.now() - 4 * 60 * 1000);

  //   try {
  //     let res1 = await this.connection
  //       .createQueryBuilder()
  //       .delete()
  //       .from('user')
  //       .where('emailVerified = :emailVerified', { emailVerified: false })
  //       .andWhere('createdAt < :fiveMinutesAgo', { fiveMinutesAgo })
  //       .execute();

  //     let res2 = await this.connection
  //       .createQueryBuilder()
  //       .delete()
  //       .from('people')
  //       .where('id NOT IN (SELECT DISTINCT peopleId FROM user)')
  //       .execute();

  //   } catch (e) {
  //   }
  // }
}
