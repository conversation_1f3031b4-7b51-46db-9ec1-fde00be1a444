import {
  Args,
  Mu<PERSON>,
  Query,
  Resolver,
  ResolveField,
  Parent,
  Context,
} from '@nestjs/graphql';
import { CredentialsService } from './credentials.service';
import { Credentials } from './entities/credentials.entity';
import { CreateUserInputAccount } from './dto/create-account.input';
// import { CreateUserInput } from './dto/create-user.input';
import { CodeVerificationInput } from './dto/verify-code.input';

import { PeoplesService } from './../peoples/peoples.service';
import { SalesBookOutput } from '../books/dto/sales-book.output';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { UseGuards } from '@nestjs/common';
import { People } from '../peoples/entities/people.entity';
import { JwtAuthGuard } from 'src/jwt/guards/auth.guard';

import * as bcrypt from 'bcrypt';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Public } from 'src/common/decorators/public.decorator';

@Resolver(() => Credentials)
export class CredentialsResolver {
  constructor(
    private readonly credentialsService: CredentialsService,
    private readonly peopleService: PeoplesService,
  ) {}

  @Public()
  @Mutation(() => Boolean, { name: 'createAccountUser' })
  async createAccount(
    @Args('dataUser') dataUser: CreateUserInputAccount,
  ): Promise<boolean> {
    return await this.credentialsService.createAccount(dataUser);
  }
  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'createAccountUserForAdmin' })
  async createAccountUserForAdmin(
    @Args('dataUser') dataUser: CreateUserInputAccount,
  ): Promise<boolean> {
    return await this.credentialsService.createAccountUserForAdmin(dataUser);
  }

  @Public()
  @Query(() => String, { name: 'loginUserPassword' })
  async loginUserPassword(
    @Args('email') email: string,
    @Args('password') password: string,
  ): Promise<string> {
    return await this.credentialsService.loginWithpassword(email, password);
    // return await this.credentialsService.loginWithEmailAndPassword(
    //   email,
    //   password,
    // );
  }

  @Public()
  @Query(() => String, { name: 'loginAdminPassword' })
  async loginAdminPassword(
    @Args('email') email: string,
    @Args('password') password: string,
  ): Promise<string> {
    return await this.credentialsService.loginWithEmailAndPassword(
      email,
      password,
    );
  }

  @Query(() => String, { name: 'generateHash' })
  async generateHash(@Args('password') password: string): Promise<string> {
    return await bcrypt.hash(password, 10);
  }

  @Public()
  @Mutation(() => Boolean, { name: 'updateCredentials' })
  async updateCredentials(
    @Args('token') token: string,
    @Args('password') password: string,
  ): Promise<boolean> {
    return await this.credentialsService.updateCredentials(token, password);
  }

  @UseGuards(JwtAuthGuard)
  @Mutation(() => Boolean, { name: 'changePasswordCredential' })
  async changePasswordCredential(
    @Context() context,
    @Args('password') password: string,
    @Args('newPassword') newPassword: string,
  ): Promise<Boolean> {
    return await this.credentialsService.changePasswordCredential(
      context.req.user.sub,
      password,
      newPassword,
    );
  }
}
