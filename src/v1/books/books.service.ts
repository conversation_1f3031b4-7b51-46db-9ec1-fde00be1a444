import { Injectable } from '@nestjs/common';
import { CreateBookInput, Data } from './dto/create-book.input';
import { UpdateBookInput } from './dto/update-book.input';
import { InjectConnection, InjectRepository } from '@nestjs/typeorm';
import { Book } from './entities/book.entity';
import { Connection, IsNull, Not, Repository } from 'typeorm';
import { Upload } from 'graphql-upload/Upload.js';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import { Version } from '../versions/entities/version.entity';
import { VersionsService } from '../versions/versions.service';
import { SalesBookOutput, VersionOutput } from './dto/sales-book.output';
import { EnumPaymentStatus } from '../shopping/entities/shopping.entity';
import { ChaptersService } from '../chapters/chapters.service';
import { SimulatorsService } from '../simulators/simulators.service';

@Injectable()
export class BooksService {
  constructor(
    @InjectRepository(Book) private bookRepository: Repository<Book>,
    @InjectConnection() private readonly connection: Connection,
    private versionService: VersionsService,
    private readonly chaptersService: ChaptersService,
    private readonly simulatorsService: SimulatorsService,
  ) {}
  booksFormat = [
    'epub',
    'pdf',
    'mobi',
    'azw',
    'txt',
    'html',
    'fb2',
    'djvu',
    'cbz',
    'cbr',
    'lit',
    'iba',
    'kf8',
    'lrf',
    'pdb',
    'azw3',
    'opf',
    'dnl',
  ];

  coverFormats = [
    'JPEG',
    'JPG',
    'PNG',
    'GIF',
    'BMP',
    'SVG',
    'WebP',
    'ICO',
    'TIFF',
    'APNG',
    'JP2',
    'HEIC',
  ];
  async createNewBook(
    // bookFile: Upload,
    // cover: Upload,
    data: CreateBookInput,
  ): Promise<boolean> {
    const existingBook = await this.bookRepository.findOne({
      where: { code: data.code, deletedAt: IsNull() },
    });
    if (existingBook) {
      throw new Error(`A book with code ${data.code} already exists.`);
    }
    // if (
    //   !this.booksFormat.some(
    //     (format) =>
    //       format.toLowerCase() ==
    //       bookFile.filename.split('.').pop().toLowerCase(),
    //   )
    // ) {
    //   throw new Error(
    //     `the file format of the book variable must be one of the sgt list: ${this.booksFormat.join(
    //       ', ',
    //     )}`,
    //   );
    // }

    // if (
    //   !this.coverFormats.some(
    //     (format) =>
    //       format.toLowerCase() == cover.filename.split('.').pop().toLowerCase(),
    //   )
    // ) {
    //   throw new Error(
    //     `the file format of the cover variable must be one of the sgt list: ${this.coverFormats.join(
    //       ', ',
    //     )}`,
    //   );
    // }

    const bookData = new Book();
    // const versionData = new Version();

    bookData.id = uuidv4();
    bookData.author = data.author;
    bookData.title = data.title;
    bookData.description = data.description;
    bookData.code = data.code;

    // versionData.id = uuidv4();
    // versionData.versionActive = data.data.versionActive;
    // versionData.bookPath = await this.SaveFile(bookFile, 'books/');
    // versionData.coverPath = (
    // await this.SaveFile(cover, 'public/images/covers/')
    // ).replaceAll('public/', '');
    // versionData.version = data.data.version;
    // versionData.daysSubscription = data.data.daysSubscription;
    // versionData.book = bookData;

    // return await this.transactionFunction(async (queryRunner) => {
    //   await queryRunner.manager.save(bookData);
    //   await queryRunner.manager.save(versionData);
    //   this.versionService.resetActiveVersion(
    //     versionData.id,
    //     versionData.versionActive,
    //     bookData.id,
    //   );
    // });

    try {
      await this.transactionFunction(async (queryRunner) => {
        await queryRunner.manager.save(bookData);
        // await queryRunner.manager.save(versionData);
        // this.versionService.resetActiveVersion(
        //   versionData.id,
        //   versionData.versionActive,
        //   bookData.id,
        // );
      });

      // Si la transacción se completa sin errores, retorna true
      return true;
    } catch (error) {
      // Si hay un error durante la transacción, puedes manejarlo aquí
      console.error('Error al crear el libro:', error);
      return false;
    }
  }

  async addNewVersionBook(
    idBook: string,
    bookFile: Upload,
    cover: Upload,
    data: Data,
  ) {
    if (
      !this.booksFormat.some(
        (format) =>
          format.toLowerCase() ==
          bookFile.filename.split('.').pop().toLowerCase(),
      )
    ) {
      throw new Error(
        `the file format of the book variable must be one of the sgt list: ${this.booksFormat.join(
          ', ',
        )}`,
      );
    }

    if (
      !this.coverFormats.some(
        (format) =>
          format.toLowerCase() == cover.filename.split('.').pop().toLowerCase(),
      )
    ) {
      throw new Error(
        `the file format of the cover variable must be one of the sgt list: ${this.coverFormats.join(
          ', ',
        )}`,
      );
    }

    const bookData = await this.bookRepository.findOne({
      where: {
        id: idBook,
        deletedAt: IsNull(),
      },
    });
    const versionData = new Version();

    versionData.id = uuidv4();
    // versionData.price = data.price;
    versionData.versionActive = data.versionActive;
    // versionData.daysSubscription = data.daysSubscription;
    versionData.bookPath = await this.SaveFile(bookFile, 'books/');
    versionData.coverPath = (
      await this.SaveFile(cover, 'public/images/covers/')
    ).replaceAll('public/', '');
    versionData.version = data.version;
    versionData.book = bookData;

    return await this.transactionFunction(async (queryRunner) => {
      await queryRunner.manager.save(versionData);
      this.versionService.resetActiveVersion(
        versionData.id,
        versionData.versionActive,
        bookData.id,
      );
    });
  }

  async updateBook(updateData: UpdateBookInput): Promise<boolean> {
    // const bookFile = (await updateData?.version?.bookFile) || null;
    // const coverFile = (await updateData?.version?.coverFile) || null;

    const existingBook = await this.bookRepository.findOne({
      where: {
        code: updateData.code,
        deletedAt: IsNull(),
        id: Not(updateData.id),
      },
    });
    if (existingBook) {
      throw new Error(`A book with code ${updateData.code} already exists.`);
    }

    let newPathBook = '';
    let newPathCover = '';

    // if (bookFile) {
    //   const versionData = await this.versionService.findOne(
    //     updateData?.version?.id,
    //   );
    //   newPathBook = await this.SaveFile(bookFile, 'books/');
    //   await this.deleteFile(versionData.bookPath);
    // }

    // if (coverFile) {
    //   const versionData = await this.versionService.findOne(
    //     updateData?.version?.id,
    //   );
    //   newPathCover = (
    //     await this.SaveFile(coverFile, 'public/images/covers/')
    //   ).replaceAll('public/', '');
    //   await this.deleteFile('public/' + versionData.coverPath);
    // }

    const bookData = await this.bookRepository.findOne({
      where: { id: updateData.id, deletedAt: IsNull() },
    });
    const newBookData = {
      title: updateData?.title || bookData.author,
      author: updateData?.author || bookData.author,
      description: updateData?.description || bookData.description,
      code: updateData?.code || bookData.code,
    };

    let resUpdateBook = await this.bookRepository.update(
      { id: updateData.id, deletedAt: IsNull() },
      newBookData,
    );

    // if (updateData?.version) {
    //   const {
    //     bookFile,
    //     coverFile,
    //     id: versionId,
    //     ...data
    //   } = updateData.version;

    //   let dataVersionUpdate: any = {
    //     ...data,
    //   };

    //   if (newPathBook !== '') {
    //     dataVersionUpdate.bookPath = newPathBook;
    //   }
    //   if (newPathCover !== '') {
    //     dataVersionUpdate.coverPath = newPathCover;
    //   }

    //   if (updateData?.version?.versionActive) {
    //     await this.versionService.resetActiveVersion(
    //       versionId,
    //       dataVersionUpdate,
    //       bookData.id,
    //     );
    //   }

    //   let res = await this.versionService.update(versionId, dataVersionUpdate);

    //   return res;
    // }

    return resUpdateBook.affected === 1;
  }

  transactionFunction(callback): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      const queryRunner = this.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        await callback(queryRunner);

        await queryRunner.commitTransaction();
        resolve(true);
        return true;
      } catch (e) {
        await queryRunner.rollbackTransaction();
        throw e;
        reject(e);
      } finally {
        await queryRunner.release();
      }
    });
  }

  async deleteFile(filePath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.unlink(filePath, (error) => {
        if (error) {
          reject(new Error(`Error deleting file: ${error.message}`));
          return;
        }
        resolve(true);
      });
    });
  }

  async SaveFile(file: Upload, path: string): Promise<string> {
    if (!file) throw new Error('no file provided.');

    const uniqueFileName = uuidv4();
    const fileExtension = file.filename.split('.').pop() || 'txt';
    const uploadPath = `${path}${uniqueFileName}.${fileExtension}`;

    return new Promise((resolve, reject) => {
      const writeStream = fs.createWriteStream(uploadPath);
      file
        .createReadStream()
        .pipe(writeStream)
        .on('finish', () => resolve(uploadPath))
        .on('error', reject);
    });
  }

  async findAll(): Promise<Book[]> {
    return await this.bookRepository.find({
      where: { deletedAt: IsNull() },
      order: { createdAt: 'desc' },
    });
  }

  async findOne(id: string): Promise<Book> {
    return await this.bookRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });
  }

  async bookForSales(): Promise<SalesBookOutput[]> {
    let resBooks = await this.bookRepository.find({
      where: { deletedAt: IsNull() },
    });

    // consulta eliminada
    // SELECT
    //     b.id AS bookId,
    //     COUNT(sh.id) AS sales,
    //     (SELECT COUNT(ch.id) FROM chapter AS ch
    //     where ch.deletedAt is null
    //     and ch.bookId= ?) AS chapters,
    //     (SELECT COUNT(sim.id) FROM chapter AS ch
    //     inner join simulator as sim
    //     on sim.chapterId = ch.id
    //     where ch.deletedAt is null
    //     and sim.deletedAt is null
    //     and ch.bookId= ?) AS simulators
    // FROM
    //     book AS b
    //     INNER JOIN version AS v ON v.bookId = b.id
    //     INNER JOIN products AS p ON p.versionId = v.id
    //     INNER JOIN shopping AS sh ON sh.id = p.shoppingId
    // WHERE
    //     b.id = ?
    // GROUP BY
    //     b.id;
    const salesBookPromises = resBooks.map(
      async ({ id, title, author, description, code }) => {
        let resVersion = await this.versionService.findOneActive(id);
        let salesData = await this.bookRepository
          .query(
            `
        SELECT 
            b.id AS bookId, 
            COUNT(DISTINCT sh.id) AS sales, 
            COUNT(DISTINCT v.id) AS versions,
            COUNT(DISTINCT ch.id) AS chapters,
            COUNT(DISTINCT sim.id) AS simulators
        FROM 
            book AS b
            INNER JOIN version AS v ON v.bookId = b.id
            LEFT JOIN products AS p ON p.versionId = v.id
            LEFT JOIN shopping AS sh ON sh.id = p.shoppingId AND sh.paymentStatus=?
            LEFT JOIN chapter AS ch ON ch.versionId = v.id AND ch.deletedAt IS NULL
            LEFT JOIN simulator AS sim ON sim.chapterId = ch.id AND sim.deletedAt IS NULL
        WHERE 
            b.id = ?
        GROUP BY 
            b.id;
        `,
            [EnumPaymentStatus.PAGO_CONFIRMADO, id],
          )
          .catch((e) => {
            console.log('error', e);
          });

        if (salesData.length === 0) {
          console.log('id 2', id);
          // salesData = await this.bookRepository.query(
          //   `
          // SELECT
          //     b.id AS bookId,
          //     0 AS sales,
          //     (SELECT COUNT(ch.id) FROM chapter AS ch
          //     where ch.deletedAt is null
          //     and ch.bookId= ?) AS chapters,
          //     (SELECT COUNT(sim.id) FROM chapter AS ch
          //     inner join simulator as sim
          //     on sim.chapterId = ch.id
          //     where ch.deletedAt is null
          //     and sim.deletedAt is null
          //     and ch.bookId= ?) AS simulators
          // FROM
          //     book AS b
          //     INNER JOIN version AS v ON v.bookId = b.id

          // WHERE
          //     b.id = ?
          // GROUP BY
          //     b.id;

          // `,
          //   [id, id, id],
          // );
          salesData = await this.bookRepository.query(
            `
          SELECT 
              b.id AS bookId, 
              0 AS sales, 
              COUNT(DISTINCT ch.id) AS chapters,
              COUNT(DISTINCT sim.id) AS simulators 
          FROM 
              book AS b
              INNER JOIN version AS v ON v.bookId = b.id
              LEFT JOIN chapter AS ch ON ch.versionId = v.id AND ch.deletedAt IS NULL
              LEFT JOIN simulator AS sim ON sim.chapterId = ch.id AND sim.deletedAt IS NULL
          WHERE 
              b.id = ?  -- Parámetro seguro para el ID del libro
          GROUP BY 
              b.id;
      `,
            [id],
          );
        }
        const chapterCount =
          await this.chaptersService.getCantidadDeCapitulosPorVersion_ByVersionId(
            resVersion.id,
          );
        const simulatorCount =
          await this.simulatorsService.getCantidadDeSimuladoresPorVersion_ByVersionId(
            resVersion.id,
          );
        return {
          id,
          title,
          author,
          description,
          code,
          sales: salesData[0]?.sales ? salesData[0]?.sales : 0,
          chapters: chapterCount ? chapterCount : 0,
          simulators: simulatorCount ? simulatorCount : 0,
          // chapters: salesData[0]?.chapters ? salesData[0]?.chapters : 0,
          // simulators: salesData[0]?.simulators ? salesData[0]?.simulators : 0,

          version: resVersion
            ? ({
                id: resVersion.id,
                version: resVersion.version,
                // price: resVersion.price,
                versionActive: resVersion.versionActive,
                cover: process.env.HOST_ADMIN + '/' + resVersion.coverPath,
                // daysSubscription: resVersion.daysSubscription,
              } as VersionOutput)
            : null,
        };
      },
    );

    return Promise.all(salesBookPromises);
  }

  async deleteBook(bookId: string): Promise<boolean> {
    try {
      return (
        (
          await this.bookRepository.update(
            { id: bookId },
            {
              deletedAt: new Date(),
            },
          )
        ).affected === 1
      );
    } catch (e) {
      throw new Error(e);
    }
  }
}
