import { InputType, Field, Int, Float, ObjectType } from '@nestjs/graphql';
import {
  IsString,
  IsNumber,
  IsBoolean,
  Min,
  IsOptional,
} from 'class-validator';

@ObjectType()
export class VersionOutput {
  @Field(() => String)
  id: string;

  // @Field(() => Float, { nullable: true })
  // @IsNumber({}, { message: 'Price must be a number' })
  // @Min(0, { message: 'Price must be a positive number' })
  // price?: number;

  @Field(() => Boolean, { nullable: true })
  @IsBoolean({ message: 'VersionActive must be a boolean value' })
  versionActive?: boolean;

  @Field(() => String, { nullable: false })
  @IsString({ message: 'Version must be a string' })
  version?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Cover must be a string' })
  cover?: string;

  // @Field(() => Int)
  // daysSubscription: number;
}

@ObjectType()
export class SalesBookOutput {
  @Field(() => String)
  id: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Title must be a string' })
  title?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Author must be a string' })
  author?: string;

  @Field(() => Int)
  sales: number;

  @Field(() => Int)
  chapters: number;

  @Field(() => Int)
  simulators: number;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Code must be a string' })
  code?: string;

  @Field(() => VersionOutput, { nullable: true })
  @IsOptional()
  version?: VersionOutput;
}
