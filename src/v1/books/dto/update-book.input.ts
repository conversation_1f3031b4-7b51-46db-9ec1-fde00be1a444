import { CreateBookInput } from './create-book.input';
import { InputType, Field, Int, PartialType, Float } from '@nestjs/graphql';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  Min,
} from 'class-validator';

import { Upload } from 'graphql-upload/Upload.js';
import * as GraphQLUpload from 'graphql-upload/GraphQLUpload.js';

@InputType()
export class VersionInput {
  @Field(() => String)
  @IsString({ message: 'ID must be a string' })
  id: string;

  // @Field(() => Float, { nullable: true })
  // @IsOptional()
  // @IsNumber({}, { message: 'Price must be a number' })
  // @Min(0, { message: 'Price must be a positive number' })
  // price?: number;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean({ message: 'VersionActive must be a boolean value' })
  versionActive?: boolean;

  @Field(() => String, { nullable: false })
  @IsOptional()
  @IsString({ message: 'Version must be a string' })
  version?: string;

  @Field(() => GraphQLUpload, { nullable: true })
  bookFile?: Upload;

  @Field(() => GraphQLUpload, { nullable: true })
  coverFile?: Upload;

  // @Field(() => Int, { nullable: true })
  // @IsOptional()
  // @IsNumber({}, { message: 'DaysSubscription must be a number' })
  // @Min(1, { message: 'DaysSubscription must be at least 1' })
  // daysSubscription?: number;
}

@InputType()
export class UpdateBookInput {
  @Field(() => String)
  @IsString({ message: 'ID must be a string' })
  id: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Title must be a string' })
  title?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Author must be a string' })
  author?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString({ message: 'code must be a string' })
  code?: string;

  // @Field(() => VersionInput, { nullable: true })
  // version?: VersionInput;
}
