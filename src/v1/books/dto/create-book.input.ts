import { InputType, Int, Field, Float } from '@nestjs/graphql';
import {
  IsString,
  IsBoolean,
  IsNumber,
  Min,
  Max,
  IsNotEmpty,
  Length,
} from 'class-validator';

@InputType()
export class Data {

  @Field(() => Boolean)
  @IsBoolean({ message: 'VersionActive must be a boolean value' })
  versionActive: boolean;

  @Field(() => String)
  @IsString({ message: 'Version must be a string' })
  @IsNotEmpty({ message: 'Version cannot be empty' })
  version: string;

  // @Field(() => Int)
  // @IsNumber({}, { message: 'DaysSubscription must be a number' })
  // @IsNotEmpty({ message: 'DaysSubscription cannot be empty' })
  // @Min(1, { message: 'DaysSubscription must be at least 1' })
  // daysSubscription: number;
}

@InputType()
export class CreateBookInput {
  @Field(() => String)
  @IsString({ message: 'El título debe ser un texto.' })
  title: string;

  @Field(() => String)
  @IsString({ message: 'El autor debe ser un texto.' })
  author: string;

  @Field(() => String)
  @IsNotEmpty({ message: 'La descripción no puede estar vacía.' })
  @IsString({ message: 'La descripción debe ser un texto.' })
  @Length(1, 900, { message: 'La descripción debe tener entre 1 y 900 caracteres.' })
  description: string;

  @Field(() => String)
  @IsString({ message: 'Code must be a string' })
  code: string;

  // @Field(() => Data)
  // data: Data;
}
