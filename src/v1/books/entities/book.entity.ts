import { ObjectType, Field } from '@nestjs/graphql';
import { IsDate, IsString, IsUUID, Length } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

import { Version } from './../../versions/entities/version.entity';
import { Chapter } from 'src/v1/chapters/entities/chapter.entity';
import { CouponBookPersonalized } from 'src/v1/coupon-book-personalizeds/entities/coupon-book-personalized.entity';

@ObjectType()
@Entity()
export class Book extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsString({ message: 'Title must be a string' })
  @Length(1, 255, { message: 'Title must be between 1 and 255 characters' })
  @Column()
  @Field()
  title: string;

  @IsString({ message: 'Author must be a string' })
  @Length(1, 255, { message: 'Author must be between 1 and 255 characters' })
  @Column()
  @Field()
  author: string;

  @IsString({ message: 'Description must be a string' })
  @Column({ type: 'varchar', length: 900 , default: '' })
  @Field()
  description: string;

  @IsString({ message: 'Codigo must be a string' })
  @Column({ type: 'varchar', length: '10', nullable: false })
  @Field()
  code: string;

  @OneToMany(() => Version, (version) => version.book, { cascade: true })
  version: Version[];

  @OneToMany(() => CouponBookPersonalized, (couponBookPersonalized) => couponBookPersonalized.book)
  couponBookPersonaizeds: CouponBookPersonalized[];

  // @OneToMany(() => Chapter, (chapter) => chapter.book)
  // chapter: Chapter[];

  // @OneToMany(() => Coupon, (coupon) => coupon.book)
  // coupon: Coupon[];
}
