import { Module } from '@nestjs/common';
import { BooksService } from './books.service';
import { BooksResolver } from './books.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Book } from './entities/book.entity';
import { VersionsModule } from './../versions/versions.module';
import { ChaptersModule } from '../chapters/chapters.module';
import { SimulatorsModule } from '../simulators/simulators.module';
import { JwtModule } from '@nestjs/jwt';
// import { ChaptersModule } from '../chapters/chapters.module';
import { BooksController } from './books.controller';

@Module({
  exports: [BooksService],
  imports: [
    VersionsModule,
    TypeOrmModule.forFeature([Book]),
    JwtModule,
    ChaptersModule,
    SimulatorsModule,
  ],
  providers: [BooksResolver, BooksService],
  controllers: [BooksController],
})
export class BooksModule {}
