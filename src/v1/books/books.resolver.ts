import {
  Resolver,
  Query,
  Mutation,
  Args,
  Int,
  Parent,
  ResolveField,
} from '@nestjs/graphql';
import { BooksService } from './books.service';
import { Book } from './entities/book.entity';

import * as GraphQLUpload from 'graphql-upload/GraphQLUpload.js';
import { Upload } from 'graphql-upload/Upload.js';

import { CreateBookInput, Data } from './dto/create-book.input';
import { Version } from '../versions/entities/version.entity';

import { VersionsService } from './../versions/versions.service';

import { UseGuards } from '@nestjs/common';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { UpdateBookInput } from './dto/update-book.input';
import { SalesBookOutput } from './dto/sales-book.output';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Public } from 'src/common/decorators/public.decorator';

@Resolver(() => Book)
export class BooksResolver {
  constructor(
    private readonly booksService: BooksService,
    private readonly versionService: VersionsService,
  ) {}

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean)
  async createBook(
    // @Args({ name: 'bookFile', type: () => GraphQLUpload })
    // bookFile: Upload,
    // @Args({ name: 'cover', type: () => GraphQLUpload })
    // cover: Upload,
    @Args({ name: 'info', type: () => CreateBookInput })
    info: CreateBookInput,
  ) {
    return await this.booksService.createNewBook(
      // bookFile, 
      // cover, 
      info
      );
  }

  // @UseGuards(JwtAuthAdminGuard)
// @UseGuards(RolesGuard)
// @Roles('admin')
//   @Mutation(() => Boolean, { name: 'addNewVersionBook' })
//   async addNewVersionBook(
//     @Args({ name: 'bookId', type: () => String })
//     bookId: string,
//     @Args({ name: 'bookFile', type: () => GraphQLUpload })
//     bookFile: Upload,
//     @Args({ name: 'cover', type: () => GraphQLUpload })
//     cover: Upload,
//     @Args({ name: 'info', type: () => Data })
//     info: Data,
//   ) {
//     return await this.booksService.addNewVersionBook(
//       bookId,
//       bookFile,
//       cover,
//       info,
//     );
//   }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean)
  async updateBook(
    @Args({ name: 'updateData', type: () => UpdateBookInput })
    updateData: UpdateBookInput,
  ) {
    return await this.booksService.updateBook(updateData);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Book], { name: 'findAllBook' })
  findAll() {
    return this.booksService.findAll();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Book, { name: 'findBookId' })
  async findOne(
    @Args({ name: 'id', type: () => String }) id: string,
  ): Promise<Book> {
    return await this.booksService.findOne(id);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @ResolveField(() => [Version])
  async versions(@Parent() book: Book): Promise<Version[]> {
    return await this.versionService.findAll(book.id);
  }

  @Public()
  @Query(() => [SalesBookOutput], { name: 'bookForSales' })
  // @UseGuards(RolesGuard)
  // @Roles('admin')
  async bookForSales(): Promise<SalesBookOutput[]> {
    return await this.booksService.bookForSales();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'deleteBook' })
  async deleteBook(
    @Args({ name: 'bookId', type: () => String })
    bookId: string,
  ) {
    return await this.booksService.deleteBook(bookId);
  }
  // @Query(() => Boolean, { name: 'bookForSales' })
  // async bookForSales(): Promise<boolean> {
  //   return true;
  // }
}
