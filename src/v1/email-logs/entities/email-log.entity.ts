import { ObjectType, Field, Int, Float } from '@nestjs/graphql';
import { IsUUID, IsOptional, IsNumber, IsDate } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import {
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

@Entity()  //revisar 
@ObjectType()
export class EmailLog extends AuditableEntity {
  @PrimaryGeneratedColumn()
  @Field(() => Int)
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @Field()
  recipientEmail: string;

  @Column({ type: 'varchar', length: 255 })
  @Field()
  senderEmail: string;

  @Column({ type: 'varchar', length: 100 })
  @Field({ nullable: true,defaultValue: ''})
  emailType?: string;

  @Column({ type: 'varchar', length: 255 })
  @Field()
  emailSubject: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  @Field(() => Date)
  sendDate: Date;

  @Column({ type: 'varchar', length: 50 })
  @Field()
  status: string;

  @Column({ type: 'text' })
  @Field({ nullable: true, defaultValue: ''})
  errorMessage?: string;

  @Column({ type: 'simple-json', nullable: true })
  @Field({ nullable: true, defaultValue: ''})
  additionalInfo?: string;
}
