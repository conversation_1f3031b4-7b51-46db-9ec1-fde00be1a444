import { Field, InputType } from "@nestjs/graphql";

@InputType()
export class EmailLogInput {

  @Field()
  recipientEmail: string;

  @Field()
  senderEmail: string;

  @Field({ nullable: true })
  emailType?: string;

  @Field()
  emailSubject: string;

  @Field(() => Date)
  sendDate: Date;

  @Field()
  status: string;

  @Field({ nullable: true })
  errorMessage?: string;

  @Field({ nullable: true })
  additionalInfo?: string;
}