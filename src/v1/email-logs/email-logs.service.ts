import { Injectable } from '@nestjs/common';

import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailLog } from './entities/email-log.entity';
import { EmailLogInput } from './dto/email-log.input';

@Injectable()
export class EmailLogsService {
    constructor(
        @InjectRepository(EmailLog) 
        private emailLogRepository: Repository<EmailLog>
    ) {}

    async insertEmailLog(dataEmailLog:EmailLogInput):Promise<EmailLog>{
        const newEmailLog= this.emailLogRepository.create(dataEmailLog);
        return await this.emailLogRepository.save(newEmailLog);
    }
}
