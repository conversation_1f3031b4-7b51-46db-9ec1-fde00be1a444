import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { FormulasService } from './formulas.service';
import { Formula } from './entities/formula.entity';
import { CreateFormulaInput } from './dto/create-formula.input';
import { UpdateFormulaInput } from './dto/update-formula.input';

@Resolver(() => Formula)
export class FormulasResolver {
  constructor(private readonly formulasService: FormulasService) {}

  @Query(() => [Formula], { name: 'findAllFormula' })
  async findAll(): Promise<Formula[]> {
    return await this.formulasService.findAll();
  }
}
