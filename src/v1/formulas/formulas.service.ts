import { Injectable } from '@nestjs/common';
import { IsNull, Repository } from 'typeorm';
import { Formula } from './entities/formula.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class FormulasService {
  constructor(
    @InjectRepository(Formula) public formulaRepository: Repository<Formula>,
  ) {}
  async create(data: Formula[]): Promise<Formula[]> {
    return await this.formulaRepository.save(data);
  }

  async findAll(): Promise<Formula[]> {
    return await this.formulaRepository.find({
      where: { deletedAt: IsNull() },
    });
  }

  async findOne(simulatorId: string): Promise<Formula[]> {
    return await this.formulaRepository
      .createQueryBuilder('formula')
      .leftJoinAndSelect('formula.simulator', 'simulator')
      .where('formula.simulatorId = :simulatorId', { simulatorId })
      .andWhere('formula.deletedAt IS NULL')
      .getMany();
  }

  async deleteAllFormula(simulatorId: string): Promise<Boolean> {
    await this.formulaRepository
      .createQueryBuilder('formula')
      .delete()
      .from(Formula)
      .where('simulatorId = :simulatorId', { simulatorId })
      .execute();

    return true;
  }

  async getFormulas_BySimulatorId(simulatorId: string): Promise<Formula[]> {
    return await this.formulaRepository.find({
      where: { simulator: { id: simulatorId }, deletedAt: IsNull() },
    });
  }

  async saveFormulas(data: Formula[]): Promise<Formula[]> {
    return await this.formulaRepository.save(data);
  }
}
