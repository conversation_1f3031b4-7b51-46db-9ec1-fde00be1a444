import { Module } from '@nestjs/common';
import { FormulasService } from './formulas.service';
import { FormulasResolver } from './formulas.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Formula } from './entities/formula.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Formula])],
  providers: [FormulasResolver, FormulasService],
  exports: [FormulasService],
})
export class FormulasModule {}
