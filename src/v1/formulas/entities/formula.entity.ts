import { ObjectType, Field, Int } from '@nestjs/graphql';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn, RelationId } from 'typeorm';
import { IsUUID, IsArray, IsNotEmpty, IsString } from 'class-validator';

// Entities
import { Simulator } from './../../simulators/entities/simulator.entity';
import { AuditableEntity } from 'src/config/auditable-entity.config';

@Entity()
@ObjectType()
export class Formula extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsArray({ message: 'Position must be an array of numbers' })
  @IsNotEmpty({ message: 'Position cannot be empty' })
  @Column({ type: 'json' })
  @Field(() => [Int])
  position: number[];

  @IsString({ message: 'Formula must be a string' })
  @IsNotEmpty({ message: 'Formula cannot be empty' })
  @Column({ type: 'text' })
  @Field()
  formula: string;

  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  @Column()
  @Field()
  name: string;

  // @Field(() => Simulator)
  @ManyToOne(() => Simulator, (simulator) => simulator.formulas)
  simulator: Simulator;
}
