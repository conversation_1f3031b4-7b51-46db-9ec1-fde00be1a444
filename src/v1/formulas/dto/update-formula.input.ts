import { CreateFormulaInput } from './create-formula.input';
import { InputType, Field, Int } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';

@InputType()
export class UpdateFormulaInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => String)
  id: string;

  @Field(() => Int, { nullable: true })
  position?: number[];

  @Field({ nullable: true })
  formula?: string;

  @Field({ nullable: true })
  name?: string;
}
