import { Test, TestingModule } from '@nestjs/testing';
import { FormulasResolver } from './formulas.resolver';
import { FormulasService } from './formulas.service';

describe('FormulasResolver', () => {
  let resolver: FormulasResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FormulasResolver, FormulasService],
    }).compile();

    resolver = module.get<FormulasResolver>(FormulasResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
