import { InputType, Int, Field, Float, ObjectType } from '@nestjs/graphql';
import { IsString, IsNumber, IsBoolean, IsOptional } from 'class-validator';

@ObjectType()
export class ProductItemOutput {
  @IsString({ message: 'Title must be a string' })
  @Field({ description: 'Title of the product' })
  title?: string;

  @IsString({ message: 'Version must be a string' })
  @Field(() => String, { description: 'Version of the product' })
  version?: string;

  @IsString({ message: 'Author must be a string' })
  @Field({ description: 'Author of the product' })
  author?: string;

  @IsString({ message: 'Cover path must be a string' })
  @Field({ description: 'Cover path of the product' })
  coverPath?: string;

  @IsOptional()
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'Price must be a valid number' },
  )
  @Field(() => Float, {
    description: 'Price of the product',
    defaultValue: null,
  })
  price?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Quantity must be a number' })
  @Field(() => Int, {
    description: 'Quantity of the product',
    defaultValue: null,
  })
  quantity?: number;

  @IsOptional()
  @IsBoolean({ message: 'Is subscribed must be a boolean' })
  @Field(() => Boolean, {
    description: 'Is subscribed flag',
    defaultValue: null,
  })
  isSubscribed?: boolean;
}

@ObjectType()
export class Summary {
  @IsString({
    each: true,
    message: 'Each product in products array must be a string',
  })
  @Field(() => [ProductItemOutput], { description: 'List of products' })
  products: ProductItemOutput[];

  @IsOptional()
  @IsString({ message: 'Coupon must be a string' })
  @Field({ description: 'Coupon code', nullable: true })
  coupon?: string | null;

  @IsNumber({}, { message: 'Discount must be a number' })
  @Field(() => Float, { description: 'Discount amount' })
  discount?: number;

  @IsNumber({}, { message: 'Total must be a number' })
  @Field(() => Float, { description: 'Total amount' })
  total?: number;

  @IsNumber({}, { message: 'Total without discount must be a number' })
  @Field(() => Float, { description: 'Total amount without discount' })
  totalWithoutDiscount?: number;
}

@ObjectType()
export class Summary2 {
  @IsString({ message: 'Product must be a string' })
  @Field(() => ProductItemOutput, { description: 'Product details' })
  product: ProductItemOutput;

  @IsOptional()
  @IsString({ message: 'Coupon must be a string' })
  @Field({ description: 'Coupon code', nullable: true })
  coupon?: string | null;

  @IsNumber({}, { message: 'Discount must be a number' })
  @Field(() => Float, { description: 'Discount amount' })
  discount?: number;

  @IsNumber({}, { message: 'Total must be a number' })
  @Field(() => Float, { description: 'Total amount' })
  total?: number;

  @IsNumber({}, { message: 'Total without discount must be a number' })
  @Field(() => Float, { description: 'Total amount without discount' })
  totalWithoutDiscount?: number;
}
