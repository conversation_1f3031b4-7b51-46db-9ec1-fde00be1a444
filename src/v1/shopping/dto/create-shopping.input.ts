import { InputType, Int, Field } from '@nestjs/graphql';
import { IsOptional, IsString, IsUUID, IsArray } from 'class-validator';

@InputType()
export class CreateShoppingInput {
  @IsOptional()
  @IsUUID('4', { message: 'Invalid UUID format for couponId' })
  @Field({ nullable: true })
  couponId?: string | null;

  @IsOptional()
  @IsUUID('4', { message: 'Invalid UUID format for planVersionId' })
  @Field({ nullable: true })
  planVersionId?: string | null;

  @IsArray({ message: 'cartVersionBooksIds must be an array of strings' })
  @IsUUID('4', {
    each: true,
    message: 'Invalid UUID format in cartVersionBooksIds',
  })
  @Field(() => [String])
  cartVersionBooksIds: string[];
}

@InputType()
export class CreateShopping2Input {
  @IsOptional()
  @IsUUID('4', { message: 'Invalid UUID format for couponId' })
  @Field({ nullable: true })
  couponId?: string | null;

  @IsUUID('4', { message: 'Invalid UUID format for versionBookId' })
  @Field(() => String)
  versionBookId: string;

  @IsUUID('4', { message: 'Invalid UUID format for versionSubscriptionPlanId' })
  @Field(() => String)
  versionSubscriptionPlanId: string;
}
