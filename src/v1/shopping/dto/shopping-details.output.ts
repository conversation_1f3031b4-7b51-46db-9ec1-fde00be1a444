import { InputType, Int, Field, Float, ObjectType } from '@nestjs/graphql';
import {
  IsString,
  IsNumber,
  IsEmail,
  IsOptional,
  IsDate,
  isString,
  IsBoolean,
} from 'class-validator';
import {
  QrBody,
  QrInfo,
  QrPaymentBankNotification,
} from 'src/v1/shopping/entities/shopping.entity';
import { Coupon } from 'src/v1/coupons/entities/coupon.entity';
import { People } from 'src/v1/peoples/entities/people.entity';
import { Version } from 'src/v1/versions/entities/version.entity';
import { VersionSubscriptionPlan } from 'src/v1/version-subscription-plans/entities/version-subscription-plan.entity';

@ObjectType()
export class ProductItem {
  @IsDate()
  @Field(() => Date)
  createdAt: Date;

  @IsDate()
  @Field(() => Date)
  updatedAt: Date;

  @IsString({ message: 'ID must be a string' })
  @Field(() => String)
  id: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Field(() => Float, { nullable: true })
  price: number;

  @IsNumber({}, { message: 'Price must be a number' })
  @Field(() => Float, { nullable: true })
  priceWithDiscount: number;

  @IsNumber({}, { message: 'Price must be a number' })
  @Field(() => Float, { nullable: true })
  amount: number;

  @IsDate()
  @Field(() => Date, { nullable: true })
  dateStart: Date | null;

  @IsDate()
  @Field(() => Date, { nullable: true })
  dateEnd: Date | null;

  @Field(() => Version, { nullable: true })
  version: Version;

  @Field(() => VersionSubscriptionPlan, { nullable: true })
  versionSubscriptionPlan: VersionSubscriptionPlan;
}

@ObjectType()
export class ShoppingDetailsOutput {
  @IsDate()
  @Field(() => Date)
  createdAt: Date;

  @IsDate()
  @Field(() => Date)
  updatedAt: Date;

  @IsString({ message: 'ID must be a string' })
  @Field(() => String)
  id: string;

  @IsString({ message: 'ProofOfPayment must be a string' })
  @Field(() => String, { nullable: true })
  proofOfPayment?: string | null;

  @IsNumber({}, { message: 'AmountPay must be a number' })
  @Field(() => Float)
  amountPay: number;

  @Field(() => Boolean)
  paymentMade: boolean;

  @IsString({ message: 'PaymentStatus must be a string' })
  @Field(() => String)
  paymentStatus: string;

  @IsString({ message: 'Method must be a string' })
  @Field(() => String)
  method: string;

  @Field(() => [ProductItem], { description: 'List of products' })
  products: ProductItem[];

  @Field(() => QrInfo, { nullable: true })
  qrInfo?: QrInfo | null;

  @Field(() => QrBody, { nullable: true })
  qrBody?: QrBody | null;

  @Field(() => QrPaymentBankNotification, { nullable: true })
  qrBank?: QrPaymentBankNotification | null;

  @Field(() => Coupon, { nullable: true })
  coupon?: Coupon | null;

  @Field(() => People, { nullable: true })
  people: People;
}
