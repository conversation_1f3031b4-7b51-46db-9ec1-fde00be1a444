import { InputType, Int, Field, Float, ObjectType } from '@nestjs/graphql';
import {
  IsString,
  IsNumber,
  IsEmail,
  IsOptional,
  IsDate,
  isString,
} from 'class-validator';

@ObjectType()
export class ReportUser {
  @IsOptional()
  @IsString({ message: 'ID must be a string' })
  @Field({ nullable: true })
  id?: string | null;

  @IsOptional()
  @IsString({ message: 'Name must be a string' })
  @Field({ nullable: true })
  name?: string | null;

  @IsOptional()
  @IsString({ message: 'Last name must be a string' })
  @Field({ nullable: true })
  lastName?: string | null;

  @IsOptional()
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @Field({ nullable: true })
  email?: string | null;

  @IsOptional()
  @IsNumber({}, { message: 'Shopping quantity must be a number' })
  @Field(() => Int, { nullable: true })
  shoppingQuantity?: number | null;
}

@ObjectType()
export class ReportShopping {
  @IsOptional()
  @IsString({ message: 'ID must be a string' })
  @Field({ nullable: true })
  id?: string | null;

  @IsOptional()
  @IsString({ message: 'Title must be a string' })
  @Field({ nullable: true })
  title?: string | null;

  @IsOptional()
  @IsString({ message: 'Author must be a string' })
  @Field({ nullable: true })
  author?: string | null;

  @IsOptional()
  @IsNumber({}, { message: 'Sales must be a number' })
  @Field(() => Int, { nullable: true })
  sales?: number | null;
}

@ObjectType()
export class ReportTotalIncomeMonth {
  @IsNumber({}, { message: 'Year must be a number' })
  @Field(() => Int)
  year: number | null;

  @IsNumber({}, { message: 'Month must be a number' })
  @Field(() => Int)
  month: number | null;

  @IsNumber({}, { message: 'Income for books must be a number' })
  @Field(() => Float)
  incomeBooks: number | null;
}

@ObjectType()
export class ReportTotalSalesBooksMonth {
  @IsNumber({}, { message: 'Year must be a number' })
  @Field(() => Int)
  year: number | null;

  @IsNumber({}, { message: 'Month must be a number' })
  @Field(() => Int)
  month: number | null;

  @IsNumber({}, { message: 'Sales of books must be a number' })
  @Field(() => Int)
  salesBooks: number | null;
}

@ObjectType()
export class ReportSalesData {
  @IsString({ message: 'ID must be a string' })
  @Field(() => String)
  id: string;

  @IsDate()
  @Field(() => Date)
  date: Date;

  @IsNumber({}, { message: 'Amount must be a number' })
  @Field(() => Float)
  amount: number;

  @IsString({ message: 'Method must be a string' })
  @Field(() => String)
  method: string;

  @IsString({ message: 'Status must be a string' })
  @Field(() => String)
  status: string;

  @IsString({ message: 'Email must be a string' })
  @Field(() => String)
  email: string;

  @Field({ nullable: true })
  isDisabled?: boolean | null;

  @IsString({ message: 'Person ID must be a string' })
  @Field(() => String)
  personId: string;

  @IsString({ message: 'Person name must be a string' })
  @Field(() => String)
  user: string;

  @IsString({ message: 'Profile ID must be a string' })
  @Field(() => String)
  profile: string;
}

@ObjectType()
export class ProofOfPaymentFromAUser {
  @IsString({ message: 'ID must be a string' })
  @Field(() => String)
  id: string;

  @IsString({ message: 'Proof of payment must be a string' })
  @Field(() => String)
  proofOfpayment: string;

  @IsNumber({}, { message: 'Amount must be a number' })
  @Field(() => Float)
  amount: number;

  @Field(() => Boolean)
  paymentMade: boolean;

  @IsString({ message: 'Payment status must be a string' })
  @Field(() => String)
  paymentStatus: string;

  @IsString({ message: 'Client ID must be a string' })
  @Field(() => String)
  clientId: string;

  @IsString({ message: 'Client name must be a string' })
  @Field(() => String)
  nameClient: string;

  @IsString({ message: 'Client last name must be a string' })
  @Field(() => String)
  lastNameClient: string;

  @IsString({ message: 'Client email must be a string' })
  @Field(() => String)
  emailClient: string;

  @IsString({ message: 'Client photo profile must be a string' })
  @Field(() => String)
  photoProfileClient: string;

  @IsString({ message: 'Coupon must be a string' })
  @Field(() => String, { nullable: true })
  nameCoupon: string | null;

  @IsNumber({}, { message: 'Discount must be a number' })
  @Field(() => Float, { nullable: true })
  discountCoupon: number | null;

  @IsNumber({}, { message: 'Total price must be a number' })
  @Field(() => Float)
  totalPrice: number;

  @IsNumber({}, { message: 'Total price with discount must be a number' })
  @Field(() => Float)
  totalPriceWithDiscount: number;

  @IsDate()
  @Field(() => Date)
  datePurchase: Date;
}
