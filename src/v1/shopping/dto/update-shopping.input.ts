import { CreateShoppingInput } from './create-shopping.input';
import { InputType, Field, Int } from '@nestjs/graphql';
import { IsInt } from 'class-validator';

@InputType()
export class UpdateShoppingInput {
  @IsInt({ message: 'ID must be an integer' })
  @Field(() => Int)
  id: number;

  @Field(() => Int, { nullable: true })
  couponId?: number | null;

  @Field(() => Int, { nullable: true })
  planVersionId?: number | null;

  @Field(() => [Int])
  cartVersionBooksIds: number[];
}
