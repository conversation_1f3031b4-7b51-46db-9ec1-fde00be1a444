import { Injectable } from '@nestjs/common';
import {
  CreateShopping2Input,
  CreateShoppingInput,
} from './dto/create-shopping.input';
import * as fs from 'fs';

import { InjectRepository } from '@nestjs/typeorm';
import {
  LessThanOrEqual,
  MoreThanOrEqual,
  MoreThan,
  Repository,
  Not,
  IsNull,
  Between,
  Brackets,
} from 'typeorm';
import { Book } from '../books/entities/book.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import {
  DataEmailExpiration,
  EnumPaymentStatus,
  Shopping,
} from './entities/shopping.entity';
import { Version } from '../versions/entities/version.entity';
import { Products } from '../products/entities/products.entity';
import { ProductItemOutput, Summary, Summary2 } from './dto/shopping.output';

import { v4 as uuidv4 } from 'uuid';
import { Upload } from 'graphql-upload/Upload.js';
import { People } from '../peoples/entities/people.entity';
import { AttachmentInput } from 'src/mail/dto/attachments-input';
import { MailService } from 'src/mail/mail.service';
import {
  ProofOfPaymentFromAUser,
  ReportSalesData,
  ReportShopping,
  ReportTotalIncomeMonth,
  ReportTotalSalesBooksMonth,
  ReportUser,
} from './dto/report.output';
import { SubscriptionPlan } from '../subscription-plans/entities/subscription-plan.entity';
import { VersionsService } from '../versions/versions.service';
import { VersionSubscriptionPlansService } from '../version-subscription-plans/version-subscription-plans.service';
import { BooksService } from '../books/books.service';
import { BankService } from 'src/bank/bank.service';
import { SubscriptionPlansService } from '../subscription-plans/subscription-plans.service';
import {
  ProductItem,
  ShoppingDetailsOutput,
} from './dto/shopping-details.output';
import { CouponsService } from '../coupons/coupons.service';
import { ProductsService } from '../products/products.service';
import { Operaciones } from 'src/@core/utils/operations';

import * as cron from 'node-cron';
import { AttachedFilesService } from '../attached-files/attached-files.service';
import { EnumNombreTabla_AttachedFile } from '../attached-files/entities/attached-file.entity';

@Injectable()
export class ShoppingService {
  opAux = Operaciones;
  constructor(
    @InjectRepository(Shopping)
    private shoppingRepository: Repository<Shopping>,
    @InjectRepository(Book) private bookRepository: Repository<Book>,
    // @InjectRepository(Coupon) private couponRepository: Repository<Coupon>,
    private couponsService: CouponsService,
    @InjectRepository(Version) private versionRepository: Repository<Version>,
    @InjectRepository(Products)
    private productsRepository: Repository<Products>,
    private productsService: ProductsService,
    @InjectRepository(People) private peopleRepository: Repository<People>,
    @InjectRepository(SubscriptionPlan)
    private subscriptionPlanRepository: Repository<SubscriptionPlan>,

    private mailService: MailService,

    private versionsService: VersionsService,
    private version_sub_planService: VersionSubscriptionPlansService,
    private booksService: BooksService,

    private bankService: BankService,
    private versionSubscriptionPlanService: VersionSubscriptionPlansService,
    private subscriptionPlanService: SubscriptionPlansService,
    private readonly attached_filesService: AttachedFilesService,
  ) {
    const h = process.env.TIME_TO_VERIFY_PRODUCTS_TO_EXPIRE || '0';
    const executionTime = '0 ' + h + ' * * *';
    cron.schedule(executionTime, () => {
      this.productAboutToExpire();
    });
  }
  proofOfPayments = [
    'JPEG',
    'JPG',
    'PNG',
    // 'GIF',
    'BMP',
    // 'SVG',
    'WebP',
    // 'ICO',
    'TIFF',
    // 'APNG',
    'JP2',
    'HEIC',
    'PDF',
  ];
  async purchaseDetail(
    data: CreateShoppingInput,
    peopleId: string,
  ): Promise<Summary> {
    try {
      const booksVersion = await (
        await Promise.all(
          data.cartVersionBooksIds.map(async (id) => {
            let res = await this.versionRepository.query(`
              select v.createdAt, 
              v.updatedAt,
              v.deletedAt,
              v.id,
              v.coverPath,
              v.bookPath,
              v.versionActive,
              v.version,
              v.bookId,
              sp.duration_days as daysSubscription, 
              CAST(vs.price AS float) as price
              from version as v 
              inner join version_subscription_plans as vs 
              on vs.versionId = v.id
              inner join subscription_plan as sp
              on sp.id = vs.subscriptionPlanId
              where v.versionActive=true 
              and v.id ="${id}"
              and vs.id = "${data.planVersionId}";
            `);
            if (res.length > 0) return res[0];
            else return null;
          }),
        )
      ).filter((element) => element !== null);

      if (data.cartVersionBooksIds.length <= 0)
        throw new Error('No hay productos para comprar.');

      // const coupon = data?.couponId
      //   ? await this.couponRepository
      //       .createQueryBuilder('coupon')
      //       .where('coupon.id = :id', { id: data?.couponId })
      //       .andWhere('coupon.dateStart <= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('coupon.dateEnd >= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('quantity>:quantity', { quantity: 0 })
      //       .andWhere('isEnable=:isEnable', { isEnable: true })
      //       .orWhere('unlimited=:unlimited', { unlimited: true })
      //       .andWhere('coupon.id = :id', { id: data?.couponId })
      //       .andWhere('coupon.dateStart <= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('coupon.dateEnd >= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('isEnable=:isEnable', { isEnable: true })
      //       .getOne()
      //   : null;

      // if (!coupon && data?.couponId) throw new Error('El cupón no es válido.');
      const couponId = data?.couponId || null;
      let coupon = null;
      if (couponId != null) {
        coupon = await this.couponsService.getCoupon_ById(couponId);
        const couponValid = await this.couponsService.validateCoupon2(coupon);
      }

      let totalWithoutDiscount = booksVersion.reduce(
        (sum, value) => sum + value.price,
        0,
      );
      totalWithoutDiscount = this.opAux.roundNumber(totalWithoutDiscount, 2);

      let total =
        totalWithoutDiscount -
        (coupon ? totalWithoutDiscount * (coupon.discountPercentage / 100) : 0);
      total = this.opAux.roundNumber(total, 2);

      const productsData = await this.productsService.getProducts_ByPeopleId(
        peopleId,
      );

      const result = await Promise.all(
        booksVersion.map(async (val) => {
          const { title, author } = await this.bookRepository.findOne({
            where: { id: val.bookId, deletedAt: IsNull() },
          });

          return {
            title,
            version: val.version,
            author,
            quantity: 1,
            coverPath: process.env.HOST_ADMIN + '/' + val.coverPath,
            price: val.price,
            isSubscribed: productsData.some((x) => x.versionId === val.id),
          } as ProductItemOutput;
        }),
      );

      return {
        products: result,
        coupon: coupon?.name || null,
        discount: coupon?.discountPercentage || 0,
        total,
        totalWithoutDiscount,
      };
    } catch (error) {
      throw new Error(error);
    }
  }
  async purchaseDetail2(
    data: CreateShopping2Input,
    peopleId: string,
  ): Promise<Summary2> {
    try {
      const bookVersion = await this.versionsService.getActiveVersionBook_ById(
        data.versionBookId,
      );
      const version_subscription_plan =
        await this.version_sub_planService.getVersionSubscriptionPlan_ById(
          data.versionSubscriptionPlanId,
        );
      const book = await this.booksService.findOne(bookVersion.bookId);

      const couponId = data?.couponId || null;
      let coupon = null;
      if (couponId != null) {
        coupon = await this.couponsService.getCoupon_ById(couponId);
      }

      const totalWithoutDiscount = version_subscription_plan.price;

      const total =
        totalWithoutDiscount -
        (coupon ? totalWithoutDiscount * (coupon.discountPercentage / 100) : 0);

      const productsData = await this.productsService.getProducts_ByPeopleId(
        peopleId,
      );

      let result = {} as ProductItemOutput;
      result.title = book.title;
      result.version = bookVersion.version;
      result.author = book.author;
      result.quantity = 1;
      result.coverPath = process.env.HOST_ADMIN + '/' + bookVersion.coverPath;
      result.price = version_subscription_plan.price;
      result.isSubscribed = productsData.some(
        (x) => x.versionId === bookVersion.id,
      );

      return {
        product: result,
        coupon: coupon?.name || null,
        discount: coupon?.discountPercentage || 0,
        total,
        totalWithoutDiscount,
      } as Summary2;
    } catch (error) {
      throw new Error(error);
    }
  }

  async confirmPurchase(
    data: CreateShoppingInput,
    peopleId: string,
  ): Promise<String> {
    try {
      const booksVersion = await (
        await Promise.all(
          data.cartVersionBooksIds.map(async (id) => {
            let res = await this.versionRepository.query(`
              select v.createdAt, 
              v.updatedAt,
              v.deletedAt,
              v.id,
              v.coverPath,
              v.bookPath,
              v.versionActive,
              v.version,
              v.bookId,
              sp.duration_days as daysSubscription, 
              CAST(vs.price AS float) as price
              from version as v 
              inner join version_subscription_plans as vs 
              on vs.versionId = v.id
              inner join subscription_plan as sp
              on sp.id = vs.subscriptionPlanId
              where v.versionActive=true 
              and v.id ="${id}"
              and vs.id = "${data.planVersionId}";
            `);
            if (res.length > 0) return res[0];
            else return null;
          }),
        )
      ).filter((element) => element !== null);

      if (data.cartVersionBooksIds.length <= 0)
        throw new Error('No hay productos para comprar.');

      const couponId = data?.couponId || null;
      let coupon = null;
      if (couponId != null) {
        coupon = await this.couponsService.getCoupon_ById(couponId);
        const validCoupon = await this.couponsService.validateCoupon2(coupon);
      }

      let totalWithoutDiscount = booksVersion.reduce(
        (sum, value) => sum + value.price,
        0,
      );
      totalWithoutDiscount = this.opAux.roundNumber(totalWithoutDiscount, 2);

      let total =
        totalWithoutDiscount -
        (coupon ? totalWithoutDiscount * (coupon.discountPercentage / 100) : 0);
      total = this.opAux.roundNumber(total, 2);

      const productsData = await this.productsService.getProducts_ByPeopleId(
        peopleId,
      );

      const result = await Promise.all(
        booksVersion.map(async (val) => {
          const { title, author } = await this.bookRepository.findOne({
            where: { id: val.bookId, deletedAt: IsNull() },
          });

          if (productsData.some((x) => x.versionId === val.id))
            throw new Error('Ya tienes una suscripción al libro ' + title);

          return {
            versionId: val.id,
            title,
            version: val.version,
            author,
            quantity: 1,
            coverPath: process.env.HOST_ADMIN + '/' + val.coverPath,
            price: val.price,
            isSubscribed: productsData.some((x) => x.versionId === val.id),
          };
        }),
      );
      const pendingPurchases = await this.getPendingPurchace_ByPeopleId(
        peopleId,
      );

      if (pendingPurchases) {
        if (pendingPurchases.proofOfpayment)
          throw new Error(
            'No podrás realizar una nueva compra hasta que no se haya verificado la anterior.',
          );

        await this.productsRepository.delete({
          shopping: { id: pendingPurchases.id },
        });

        const deleteCart = await this.shoppingRepository.delete({
          paymentMade: false,
          people: { id: peopleId },
        });

        if (deleteCart.affected !== 1)
          throw new Error('La transacción no se puede completar.');
      }

      const shoppingId = uuidv4();

      const bodyQR = {
        currency: 'BOB',
        gloss: `EconomixHub-${shoppingId}`,
        amount: total,
        singleUse: true,
        expirationDate: new Date().toISOString().split('T')[0],
        additionalData: `Libros: ${result.map((x) => x.title).join(', ')}.`,
        destinationAccountId: '1',
      };

      // ---estraer qr del banco
      const fakeData = {
        id: '81596',
        qr: '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',
        success: true,
        message: '',
      };

      await this.shoppingRepository.save({
        id: shoppingId,
        amountPay: total,
        coupon: { id: coupon?.id || null },
        paymentMade: false,
        paymentStatus: EnumPaymentStatus.SIN_ESTADO,
        qrBody: bodyQR,
        qrInfo: fakeData,
        people: { id: peopleId },
      });
      const productsRes = await this.productsRepository.save(
        result.map(
          (x) =>
            ({
              id: uuidv4(),

              price: x.price,

              priceWithDiscount: this.opAux.roundNumber(
                coupon?.discountPercentage
                  ? x.price - x.price * (coupon.discountPercentage / 100)
                  : x.price,
                2,
              ),

              amount: 1,
              shopping: { id: shoppingId },
              version: { id: x.versionId },
            } as Products),
        ),
      );

      return 'data:image/png;base64,' + fakeData.qr;
    } catch (error) {
      throw new Error(error);
    }
  }

  async confirmPurchase2(
    data: CreateShopping2Input,
    peopleId: string,
  ): Promise<String> {
    try {
      const bookVersion = await this.versionsService.getActiveVersionBook_ById(
        data.versionBookId,
      );
      const version_subscription_plan =
        await this.version_sub_planService.getVersionSubscriptionPlan_ById(
          data.versionSubscriptionPlanId,
        );
      const book = await this.booksService.findOne(bookVersion.bookId);

      const couponId = data?.couponId || null;
      let coupon = null;
      if (couponId != null) {
        coupon = await this.couponsService.getCoupon_ById(couponId);
        await this.couponsService.useCoupon(coupon);
      }

      let totalWithoutDiscount = version_subscription_plan.price;
      totalWithoutDiscount = this.opAux.roundNumber(totalWithoutDiscount, 2);
      const discount = coupon ? totalWithoutDiscount * (coupon.discountPercentage / 100) : 0;

      let total = totalWithoutDiscount - discount;
      total = this.opAux.roundNumber(total, 2);

      const productsData: Products[] =
        await this.productsService.getProducts_ByPeopleId(peopleId);

      let result: any = {};
      result.title = book.title;
      result.versionId = data.versionBookId;

      result.version = bookVersion.version;
      result.author = book.author;
      result.quantity = 1;
      result.coverPath = process.env.HOST_ADMIN + '/' + bookVersion.coverPath;
      result.price = version_subscription_plan.price;
      result.isSubscribed = productsData.some(
        (x) => x.versionId === bookVersion.id,
      );

      // const pendingPurchases = await this.shoppingRepository.findOne({
      //   where: {
      //     paymentMade: false,
      //     people: { id: peopleId },
      //     deletedAt: IsNull(),
      //   },
      // });

      const pendingPurchases = await this.getPendingPurchace_ByPeopleId(
        peopleId,
      );

      if (pendingPurchases) {
        if (pendingPurchases.proofOfpayment)
          throw new Error(
            'No podrás realizar una nueva compra hasta que no se haya verificado la anterior',
          );

        let shoppingData = await this.shoppingRepository.query(
          `
          select * from shopping where proofOfpayment is null and qrInfo is not null and qrBody is not null and peopleId=?
        `,
          [peopleId],
        );

        console.log('peopleId');
        console.log(peopleId);

        console.log('shoppingData');
        console.log(shoppingData);
        const qrId = JSON.parse(shoppingData[0]?.qrInfo)?.id;
        console.log('----', qrId);
        if (qrId) {
          const resBnbQrDeleted: any = await this.bankService.CancelQRByIdAsync(
            qrId,
          );
          console.log('----->>>', resBnbQrDeleted);
          if (!resBnbQrDeleted?.success)
            throw new Error('No se pudo conectar con el servicio bancario.');
        }

        // await this.productsRepository.delete({
        //   shopping: { id: pendingPurchases.id },
        // });

        const deleteCart = await this.shoppingRepository.delete({
          paymentMade: false,
          people: { id: peopleId },
        });

        if (deleteCart.affected !== 1)
          throw new Error('La transacción no se puede completar.');
      }
      const shoppingId = uuidv4();

      let bodyQR = null;
      let dataBNB = null;
      let paymentStatus = EnumPaymentStatus.SIN_ESTADO;
      let paymentMade = false;

      const subscriptionPlan =
        await this.subscriptionPlanService.getSubscriptionPlan_ById(
          version_subscription_plan.subscriptionPlanId,
        );
      let dateStart = null;
      let newDateEnd = null;

      if (total > 0) {
        bodyQR = {
          currency: 'BOB',
          gloss: `EconomixHub-${shoppingId}`,
          amount: total,
          singleUse: true,
          expirationDate: new Date().toISOString().split('T')[0],
          additionalData: `Libro: ${result.title}.`,
          destinationAccountId: '1',
        };
        dataBNB = await this.bankService.GetQRasync(bodyQR);
        paymentStatus = EnumPaymentStatus.QR_GENERADO;
      } else {
        paymentStatus = EnumPaymentStatus.PAGO_CONFIRMADO;
        paymentMade = true;
        dateStart = new Date();
        newDateEnd = new Date(dateStart);
        newDateEnd.setDate(
          dateStart.getDate() + subscriptionPlan.duration_days,
        );
      }

      // const authBNB = await this.bankService.GetToken();
      // console.log('xdd');

      // console.log(bodyQR);

      // console.log('holo'); rerererererr
      await this.shoppingRepository.save({
        id: shoppingId,
        amountPay: total,
        coupon: { id: coupon?.id || null },
        paymentMade: paymentMade,
        paymentStatus: paymentStatus,
        qrBody: bodyQR,
        qrInfo: dataBNB,
        people: { id: peopleId },
        versionSubscriptionsPlans: { id: data.versionSubscriptionPlanId },
      });

      const productsRes = await this.productsRepository.save({
        id: uuidv4(),

        price: result.price,

        priceWithDiscount: this.opAux.roundNumber(
          coupon?.discountPercentage
            ? result.price - result.price * (coupon.discountPercentage / 100)
            : result.price,
          2,
        ),

        amount: 1,
        shopping: { id: shoppingId },
        version: { id: result.versionId },
        versionSubscriptionsPlans: { id: data.versionSubscriptionPlanId },
        dateStart: dateStart,
        dateEnd: newDateEnd,
      } as Products);

      if (total > 0) {
        return 'data:image/png;base64,' + dataBNB?.qr;
      } else {
        const peopleData = await this.peopleRepository.findOne({
          where: { id: peopleId, deletedAt: IsNull() },
        });
        const productsData = await this.productsRepository.find({
          where: { shopping: { id: shoppingId }, deletedAt: IsNull() },
        });
    
        await this.sendEmailConfirmationPurchase(
          peopleData,
          productsData,
          total,
          discount,
        );
        return '';
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async confirmPurchaseProofOfPayment(
    data: CreateShoppingInput,
    peopleId: string,
  ): Promise<Boolean> {
    try {
      const booksVersion = await (
        await Promise.all(
          data.cartVersionBooksIds.map(async (id) => {
            let res = await this.versionRepository.query(`
              select v.createdAt, 
              v.updatedAt,
              v.deletedAt,
              v.id,
              v.coverPath,
              v.bookPath,
              v.versionActive,
              v.version,
              v.bookId,
              sp.duration_days as daysSubscription, 
              CAST(vs.price AS float) as price
              from version as v 
              inner join version_subscription_plans as vs 
              on vs.versionId = v.id
              inner join subscription_plan as sp
              on sp.id = vs.subscriptionPlanId
              where v.versionActive=true 
              and v.id ="${id}"
              and vs.id = "${data.planVersionId}";
            `);
            if (res.length > 0) return res[0];
            else return null;
          }),
        )
      ).filter((element) => element !== null);

      if (data.cartVersionBooksIds.length <= 0)
        throw new Error('No hay productos para comprar.');

      // const coupon = data?.couponId
      //   ? await this.couponRepository
      //       .createQueryBuilder('coupon')
      //       .where('coupon.id = :id', { id: data?.couponId })
      //       .andWhere('coupon.dateStart <= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('coupon.dateEnd >= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('quantity>:quantity', { quantity: 0 })
      //       .andWhere('isEnable=:isEnable', { isEnable: true })
      //       .orWhere('unlimited=:unlimited', { unlimited: true })
      //       .andWhere('coupon.id = :id', { id: data?.couponId })
      //       .andWhere('coupon.dateStart <= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('coupon.dateEnd >= :now', {
      //         now: new Date(),
      //       })
      //       .andWhere('isEnable=:isEnable', { isEnable: true })
      //       .getOne()
      //   : null;

      const couponId = data?.couponId || null;
      let coupon = null;
      if (couponId != null) {
        coupon = await this.couponsService.getCoupon_ById(couponId);
        await this.couponsService.validateCoupon2(coupon);
      }

      // if (!coupon && data?.couponId) throw new Error('El cupón no es válido.');

      let totalWithoutDiscount = booksVersion.reduce(
        (sum, value) => sum + value.price,
        0,
      );
      totalWithoutDiscount = this.opAux.roundNumber(totalWithoutDiscount, 2);

      let total =
        totalWithoutDiscount -
        (coupon ? totalWithoutDiscount * (coupon.discountPercentage / 100) : 0);
      total = this.opAux.roundNumber(total, 2);

      const productsData = await this.productsService.getProducts_ByPeopleId(
        peopleId,
      );

      const result = await Promise.all(
        booksVersion.map(async (val) => {
          const { title, author } = await this.bookRepository.findOne({
            where: { id: val.bookId, deletedAt: IsNull() },
          });

          if (productsData.some((x) => x.versionId === val.id))
            throw new Error('Ya tienes una suscripción al libro ' + title);

          return {
            versionId: val.id,
            title,
            version: val.version,
            author,
            quantity: 1,
            coverPath: process.env.HOST_ADMIN + '/' + val.coverPath,
            price: val.price,
            isSubscribed: productsData.some((x) => x.versionId === val.id),
          };
        }),
      );

      const pendingPurchases: Shopping =
        await this.getPendingPurchace_ByPeopleId(peopleId);

      if (pendingPurchases) {
        if (pendingPurchases.proofOfpayment)
          throw new Error(
            'No podrás realizar una nueva compra hasta que no se haya verificado la anterior',
          );

        await this.productsRepository.softDelete({
          shopping: { id: pendingPurchases.id },
        });

        const deleteCart = await this.shoppingRepository.softDelete({
          paymentMade: false,
          people: { id: peopleId },
        });

        if (deleteCart.affected !== 1)
          throw new Error('La transacción no se puede completar.');
      }

      const shoppingId = uuidv4();

      await this.shoppingRepository.save({
        id: shoppingId,
        amountPay: total,
        coupon: { id: coupon?.id || null },
        paymentMade: false,
        paymentStatus: EnumPaymentStatus.SIN_ESTADO,
        qrBody: null,
        qrInfo: null,
        people: { id: peopleId },
      });
      const productsRes = await this.productsRepository.save(
        result.map(
          (x) =>
            ({
              id: uuidv4(),

              price: x.price,

              priceWithDiscount: this.opAux.roundNumber(
                coupon?.discountPercentage
                  ? x.price - x.price * (coupon.discountPercentage / 100)
                  : x.price,
                2,
              ),

              amount: 1,
              shopping: { id: shoppingId },
              version: { id: x.versionId },
            } as Products),
        ),
      );

      return productsRes.length > 0;
    } catch (error) {
      throw new Error(error);
    }
  }

  getPendingPurchace_ByPeopleId(peopleId: string): Promise<Shopping | null> {
    return this.shoppingRepository.findOne({
      where: {
        paymentMade: false,
        paymentStatus: EnumPaymentStatus.COMPROBANTE_EN_REVISION,
        people: { id: peopleId },
        deletedAt: IsNull(),
      },
    });
  }

  // ----------------------------------------------------

  async confirmPurchaseProofOfPayment2(
    data: CreateShopping2Input,
    peopleId: string,
  ): Promise<Shopping> {
    try {
      const bookVersion = await this.versionsService.getActiveVersionBook_ById(
        data.versionBookId,
      );
      const version_subscription_plan =
        await this.version_sub_planService.getVersionSubscriptionPlan_ById(
          data.versionSubscriptionPlanId,
        );
      const book = await this.booksService.findOne(bookVersion.bookId);

      const couponId = data?.couponId || null;
      let coupon = null;
      if (couponId != null) {
        coupon = await this.couponsService.getCoupon_ById(couponId);
        await this.couponsService.useCoupon(coupon);
      }

      let totalWithoutDiscount = version_subscription_plan.price;
      totalWithoutDiscount = this.opAux.roundNumber(totalWithoutDiscount, 2);

      let total =
        totalWithoutDiscount -
        (coupon ? totalWithoutDiscount * (coupon.discountPercentage / 100) : 0);
      total = this.opAux.roundNumber(total, 2);

      const productsData = await this.productsService.getProducts_ByPeopleId(
        peopleId,
      );

      let result: any = {};
      result.title = book.title;
      result.versionId = data.versionBookId;
      result.version = bookVersion.version;
      result.author = book.author;
      result.quantity = 1;
      result.coverPath = process.env.HOST_ADMIN + '/' + bookVersion.coverPath;
      result.price = version_subscription_plan.price;
      result.isSubscribed = productsData.some(
        (x) => x.versionId === bookVersion.id,
      );

      const pendingPurchases = await this.getPendingPurchace_ByPeopleId(
        peopleId,
      );

      if (pendingPurchases) {
        if (pendingPurchases.proofOfpayment)
          throw new Error(
            'No podrás realizar una nueva compra hasta que no se haya verificado la anterior.',
          );
      }

      const shoppingId = uuidv4();

      const shoppingInserted = await this.shoppingRepository.save({
        id: shoppingId,
        amountPay: total,
        coupon: { id: coupon?.id || null },
        paymentMade: false,
        paymentStatus: EnumPaymentStatus.COMPROBANTE_EN_REVISION,
        qrBody: null,
        qrInfo: null,
        people: { id: peopleId },
      });
      const productsRes = await this.productsRepository.save({
        id: uuidv4(),
        price: result.price,
        priceWithDiscount: this.opAux.roundNumber(
          coupon?.discountPercentage
            ? result.price - result.price * (coupon.discountPercentage / 100)
            : result.price,
          2,
        ),
        amount: 1,
        shopping: { id: shoppingId },
        version: { id: result.versionId },
        versionSubscriptionsPlans: { id: data.versionSubscriptionPlanId },
        dateStart: new Date(),
      } as Products);

      const peopleData = await this.peopleRepository.findOne({
        where: { id: peopleId, deletedAt: IsNull() },
      });
      if (!shoppingInserted)
        throw new Error('Ocurrió un error inesperado al registrar la compra.');
      await this.mailService.proofOfPaymentSuccessful({
        email: peopleData.email,
        name: peopleData.name,
        lastName: peopleData.lastName,
      });

      return shoppingInserted;
    } catch (error) {
      throw new Error(error);
    }
  }

  async registerProofOfPayment(
    peopleId: string,
    proofOfPayment: Upload,
    shoppingId: string,
  ): Promise<boolean> {
    if (
      !this.proofOfPayments.some(
        (format) =>
          format.toLowerCase() ==
          proofOfPayment.filename.split('.').pop().toLowerCase(),
      )
    ) {
      throw new Error(
        `El formato de archivo de la variable de portada debe ser uno de la lista de siguiente: ${this.proofOfPayments.join(
          ', ',
        )}`,
      );
    }
    const updateData = await this.shoppingRepository.save({
      id: shoppingId,
      proofOfpayment: (
        await this.SaveFile(proofOfPayment, 'public/images/proofOfPayments/')
      ).replaceAll('public/', ''),
    });

    return updateData ? true : false;
  }

  async SaveFile(file: Upload, path: string): Promise<string> {
    if (!file) throw new Error('No se proporcionó ningún archivo.');

    const uniqueFileName = uuidv4();
    const fileExtension = file.filename.split('.').pop() || 'txt';
    const uploadPath = `${path}${uniqueFileName}.${fileExtension}`;

    return new Promise((resolve, reject) => {
      const writeStream = fs.createWriteStream(uploadPath);
      file
        .createReadStream()
        .pipe(writeStream)
        .on('finish', () => resolve(uploadPath))
        .on('error', reject);
    });
  }

  async listAllProofOfPayment(): Promise<Shopping[]> {
    let res = await this.shoppingRepository.find({
      where: {
        proofOfpayment: Not(IsNull()),
        deletedAt: IsNull(),
      },
    });

    return res.map((val) => {
      return {
        ...val,
        proofOfpayment: process.env.HOST_ADMIN + '/' + val.proofOfpayment,
      } as Shopping;
    });
  }

  async listAllProofOfPayment2(): Promise<Shopping[]> {
    let res = await this.shoppingRepository.find({
      where: {
        paymentStatus: EnumPaymentStatus.COMPROBANTE_EN_REVISION,
        deletedAt: IsNull(),
      },
      order: {
        createdAt: 'ASC',
      },
    });
    return res.map((val) => {
      return {
        ...val,
        proofOfpayment: process.env.HOST_ADMIN + '/' + val.proofOfpayment,
      } as Shopping;
    });
  }

  async enableAccountProofOfPayment(shoppingId: string): Promise<Boolean> {
    let productsData = await this.productsRepository.find({
      where: { shopping: { id: shoppingId }, deletedAt: IsNull() },
    });

    const versionData = await this.versionRepository.find({
      where: { deletedAt: IsNull() },
    });

    const updateDataState = await Promise.all(
      productsData.map(async (val) => {
        const versionSubscriptionPlan =
          await this.versionSubscriptionPlanService.getVersionSubscriptionPlan_ById(
            val.versionSubscriptionsPlansId,
          );

        const subscriptionPlan =
          await this.subscriptionPlanService.getSubscriptionPlan_ById(
            versionSubscriptionPlan.subscriptionPlanId,
          );

        const version = versionData.filter((x) => x.id === val.versionId)[0];
        let date = new Date();
        let newDateEnd = new Date(date);
        newDateEnd.setDate(date.getDate() + subscriptionPlan.duration_days);

        return (
          (
            await this.productsRepository.update(
              { id: val.id, deletedAt: IsNull() },
              {
                dateStart: date,
                dateEnd: newDateEnd,
              },
            )
          ).affected === 1
        );
      }),
    );

    productsData = await this.productsRepository.find({
      where: { shopping: { id: shoppingId }, deletedAt: IsNull() },
    });

    if (!updateDataState.every((x) => x))
      throw new Error('Ocurrió un error inesperado.');

    const shoppingData = await this.getShopping_ById(shoppingId);

    const peopleData = await this.peopleRepository.findOne({
      where: { id: shoppingData.peopleId, deletedAt: IsNull() },
    });


    const attachmentsData: AttachmentInput[] = await this.getAllAttachments(productsData);
    const productsDetail = await Promise.all(
      productsData.map(async (val) => {
        const versionData = await this.versionRepository.findOne({
          where: { id: val.versionId, deletedAt: IsNull() },
        });
        const bookData = await this.bookRepository.findOne({
          where: { id: versionData.bookId, deletedAt: IsNull() },
        });

        return {
          name: bookData.title,
          ...val,
          price: val.price.toLocaleString('es', {
            style: 'currency',
            currency: 'BOB',
          }),
          priceWithDiscount: val.priceWithDiscount.toLocaleString('es', {
            style: 'currency',
            currency: 'BOB',
          }),
          dateEnd: `${new Date(val.dateEnd).toLocaleDateString()} ${new Date(
            val.dateEnd,
          ).toLocaleTimeString()}`,
        };
      }),
    );

    const activeAccount = await this.shoppingRepository.update(
      {
        id: shoppingId,
        deletedAt: IsNull(),
      },
      {
        paymentMade: true,
        paymentStatus: EnumPaymentStatus.PAGO_CONFIRMADO,
      },
    );

    let total = productsData.reduce((acum, value) => acum + value.price, 0);
    total = this.opAux.roundNumber(total, 2);
    let totalDiscounted = productsData.reduce(
      (acum, value) => acum + value.priceWithDiscount,
      0,
    );
    totalDiscounted = this.opAux.roundNumber(totalDiscounted, 2);

    const discounted = total - totalDiscounted;

    if (activeAccount.affected === 1)
      await this.mailService.confirmationPurchase(
        peopleData,
        attachmentsData,
        productsDetail,
        totalDiscounted.toLocaleString('es', {
          style: 'currency',
          currency: 'BOB',
        }),
        discounted.toLocaleString('es', {
          style: 'currency',
          currency: 'BOB',
        }),
      );

    return (
      (
        await this.shoppingRepository.update(
          { id: shoppingId, deletedAt: IsNull() },
          {
            paymentMade: true,
            paymentStatus: EnumPaymentStatus.PAGO_CONFIRMADO,
          },
        )
      ).affected === 1
    );
  }

  async sendEmailConfirmationPurchase(
    peopleData:People,
    productsData:Products[],
    totalWithDiscount:number,
    totalDiscounted:number,
    ){
      const attachmentsData: AttachmentInput[] = await this.getAllAttachments(productsData);
      const productsDetail = await Promise.all(
        productsData.map(async (val) => {
          const versionData = await this.versionRepository.findOne({
            where: { id: val.versionId, deletedAt: IsNull() },
          });
          const bookData = await this.bookRepository.findOne({
            where: { id: versionData.bookId, deletedAt: IsNull() },
          });
  
          return {
            name: bookData.title,
            ...val,
            price: val.price.toLocaleString('es', {
              style: 'currency',
              currency: 'BOB',
            }),
            priceWithDiscount: val.priceWithDiscount.toLocaleString('es', {
              style: 'currency',
              currency: 'BOB',
            }),
            dateEnd: `${new Date(val.dateEnd).toLocaleDateString()} ${new Date(
              val.dateEnd,
            ).toLocaleTimeString()}`,
          };
        }),
      );
      await this.mailService.confirmationPurchase(
        peopleData,
        attachmentsData,
        productsDetail,
        totalWithDiscount.toLocaleString('es', {
          style: 'currency',
          currency: 'BOB',
        }),
        totalDiscounted.toLocaleString('es', {
          style: 'currency',
          currency: 'BOB',
        }),
      );
    
  }

  // Función para obtener los archivos adjuntos para un solo producto.
  async getAttachmentsForProduct(product): Promise<AttachmentInput[]>{
    const docs_version = await this.attached_filesService.getArchivosAdjuntos_Registro(
      EnumNombreTabla_AttachedFile.VERSIONES,
      product.versionId
    );
    
    // Mapea los documentos a sus attachments y retorna el resultado.
    return await Promise.all(docs_version.map(async (doc) => {
      const file = await doc.file;
      return {
        filename: file.originalname,
        path: "./files/archivos/" + file.filename,
        contentDisposition: 'attachment', 
      } as AttachmentInput;
    }));
  }

  // Función principal para obtener todos los attachments para todos los productos.
  async getAllAttachments(productsData): Promise<AttachmentInput[]>{
    let attachmentsData: AttachmentInput[] = [];

    for (const product of productsData) {
      const attachments = await this.getAttachmentsForProduct(product);
      attachmentsData = attachmentsData.concat(attachments); // Concatena los nuevos attachments al array existente.
    }

    return attachmentsData;
  }

  async getShopping_ById(id: string): Promise<Shopping> {
    const shopping = await this.shoppingRepository.findOne({
      where: { id: id, deletedAt: IsNull() },
    });
    if (!shopping) {
      throw new Error('El carrito no existe');
    }
    return shopping;
  }

  async rejectProofOfPayment(
    id: string,
    paymentRejectionReason: string | null,
  ): Promise<boolean> {
    try {
      const shoppingData = await this.getShopping_ById(id);
      if (shoppingData.couponId) {
        const coupon = await this.couponsService.getCoupon_ById(
          shoppingData.couponId,
        );
        await this.couponsService.cancelCouponUse(coupon);
      }
      shoppingData.paymentStatus = EnumPaymentStatus.COMPROBANTE_RECHAZADO;
      shoppingData.paymentRejectionReason = paymentRejectionReason;
      const shopping_actualizado = await this.shoppingRepository.save(
        shoppingData,
      );
      await this.mailService.sendRejectedProofOfPayment(
        shoppingData.people,
        shoppingData,
      );
      return shopping_actualizado ? true : false;
    } catch (e) {
      throw new Error(e);
    }
  }

  async incomeByRank(startDate: Date, endDate: Date): Promise<Number> {
    console.log('startDate');
    console.log(startDate);
    console.log('endDate');
    console.log(endDate);
    // const firstDayOfMonth = new Date(
    //   new Date().getFullYear(),
    //   new Date().getMonth(),
    //   1,
    //   0,
    //   0,
    //   0,
    // );
    // const lastDayOfMonth = new Date(
    //   new Date().getFullYear(),
    //   new Date().getMonth() + 1,
    //   0,
    //   23,
    //   59,
    //   59,
    // );

    const result = await this.shoppingRepository
      .createQueryBuilder('shopping')
      .select('SUM(shopping.amountPay)', 'sum')
      .where('shopping.paymentStatus = :status', { status: EnumPaymentStatus.PAGO_CONFIRMADO })
      .andWhere('shopping.createdAt BETWEEN :startDate AND :endDate', {
        startDate: startDate,
        endDate: endDate,
      })
      .getRawOne();

    return result.sum || 0;
  }

  async incomeByMonths(): Promise<ReportTotalIncomeMonth[]> {
    return await this.productsRepository.query(`
      select extract(MONTH from createdAt) as month,extract(YEAR from createdAt) as year, sum(priceWithDiscount) as incomeBooks  from products
      where dateStart is not null
      and dateEnd is not null
      group by month, year;
    `);
  }

  async quantitySalesBooksMonths(): Promise<ReportTotalSalesBooksMonth[]> {
    console.log('quantitySalesBooksMonths___');
    const res= await this.productsRepository.query(`
    SELECT
    COUNT(id) AS salesBooks,
    EXTRACT(MONTH FROM createdAt) AS month,
    EXTRACT(YEAR FROM createdAt) AS year
  FROM
    shopping
  WHERE
    paymentStatus = ?
    AND createdAt >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
  GROUP BY
    EXTRACT(MONTH FROM createdAt),
    EXTRACT(YEAR FROM createdAt)  
    `,[EnumPaymentStatus.PAGO_CONFIRMADO]);
    console.log('res');
    return res;
  }

  //pendiente ____
  async reportShopping(): Promise<ReportShopping[]> {
    return await this.shoppingRepository.query(`
    SELECT
      b.id as id,
      b.author as author,
      b.title as title,
      COUNT(s.id) as sales
    FROM shopping as s
      INNER JOIN products as p ON p.shoppingId = s.id
      INNER JOIN version as v ON p.versionId = v.id
      INNER JOIN book as b ON b.id = v.bookId
    WHERE
      s.paymentStatus = ?
    GROUP BY
      b.id
    ORDER BY
      b.id DESC;
    `,[EnumPaymentStatus.PAGO_CONFIRMADO]);
  }

  async reportUser(): Promise<ReportUser[]> {
    const res = await this.shoppingRepository.query(`
    SELECT
      p.id as id,
      p.name as name,
      p.lastName as lastName,
      p.email as email,
      COUNT(pr.id) as shoppingQuantity
    FROM people as p
      INNER JOIN shopping as s ON s.peopleId = p.id
      INNER JOIN products as pr ON pr.shoppingId = s.id
    WHERE
      s.paymentStatus = ?
    GROUP BY
      p.id
    ORDER BY
      p.name DESC;
    `,[EnumPaymentStatus.PAGO_CONFIRMADO]);
    return res;
  }

  async getAllShopping(): Promise<ReportSalesData[]> {
    let res = await this.shoppingRepository.find({
      where: { deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });
    console.log('res');
    console.log(res);

    return res.map((val) => {
      let method = '';
      if (val.qrInfo === null && val.qrBody === null && val.proofOfpayment) {
        method = 'Comprobante';
      } else if (val.qrInfo && val.qrBody && val.proofOfpayment == null) {
        method = 'QR';
      } else {
        method = '-';
      }
      console.log('val.people');
      console.log(val.people);
      return {
        id: val.id,
        date: val.createdAt,
        user: val.people.name + ' ' + val.people.lastName,
        amount: val.amountPay,
        method: method,
        // val.qrInfo === null && val.qrBody === null && val.proofOfpayment
        //   ? 'Comprobante'
        //   : 'QR',
        status:
          val.paymentStatus.charAt(0) +
          val.paymentStatus.slice(1).toLowerCase(),
        email: val.people.email,
        isDisabled:
          val.proofOfpayment !== null &&
          val.paymentStatus === EnumPaymentStatus.COMPROBANTE_EN_REVISION &&
          val.qrInfo === null &&
          val.qrBody === null
            ? false
            : true,
        personId: val.people.id,
        profile: val.people.photoProfile.startsWith('http')
          ? val.people.photoProfile
          : process.env.HOST_ADMIN + '/' + val.people.photoProfile,
      } as ReportSalesData;
    });
  }
  async getProofOfPaymentById(id: string): Promise<ProofOfPaymentFromAUser> {
    const shopping = await this.shoppingRepository.findOne({
      where: { id: id, deletedAt: IsNull() },
    });
    if (!shopping) {
      throw new Error('El carrito no existe');
    }
    let total = shopping.product.reduce((acum, val) => acum + val.price, 0);
    total = this.opAux.roundNumber(total, 2);
    let withDiscount = shopping.coupon
      ? total * (1 - shopping.coupon?.discountPercentage / 100)
      : total;
    withDiscount = this.opAux.roundNumber(withDiscount, 2);
    return {
      id: shopping.id,
      proofOfpayment: process.env.HOST_ADMIN + '/' + shopping.proofOfpayment,
      amount: shopping.amountPay,
      paymentMade: shopping.paymentMade,
      paymentStatus: shopping.paymentStatus,
      clientId: shopping.people.id,
      nameClient: shopping.people.name,
      lastNameClient: shopping.people.lastName,
      emailClient: shopping.people.email,
      photoProfileClient: shopping.people.photoProfile.startsWith('http')
        ? shopping.people.photoProfile
        : process.env.HOST_ADMIN + '/' + shopping.people.photoProfile,
      nameCoupon: shopping.coupon ? shopping.coupon.name : null,
      discountCoupon: shopping.coupon
        ? shopping.coupon.discountPercentage
        : null,
      totalPrice: total,
      totalPriceWithDiscount: withDiscount,
      datePurchase: shopping.createdAt,
    } as ProofOfPaymentFromAUser;
  }

  async getPurchaseDetails(id: string): Promise<ShoppingDetailsOutput> {
    const shopping = await this.shoppingRepository.findOne({
      where: { id: id, deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });
    const products = await this.productsRepository.find({
      where: { shopping: { id: id }, deletedAt: IsNull() },
    });
    if (!shopping) {
      throw new Error('El carrito no existe');
    }
    if (!products) {
      throw new Error('El carrito no existe');
    }
    let method = '';
    if (
      shopping.qrInfo === null &&
      shopping.qrBody === null &&
      shopping.proofOfpayment
    ) {
      method = 'Comprobante';
    } else if (
      shopping.qrInfo &&
      shopping.qrBody &&
      shopping.proofOfpayment == null
    ) {
      method = 'QR';
    } else {
      method = '-';
    }

    return {
      createdAt: shopping.createdAt,
      updatedAt: shopping.updatedAt,
      id: shopping.id,
      proofOfPayment: shopping.proofOfpayment
        ? process.env.HOST_ADMIN + '/' + shopping.proofOfpayment
        : null,
      amountPay: shopping.amountPay,
      paymentMade: shopping.paymentMade,
      paymentStatus: shopping.paymentStatus,
      method: method,
      // shopping.qrInfo === null &&
      // shopping.qrBody === null &&
      // shopping.proofOfpayment
      //   ? 'Comprobante'
      //   : 'QR',
      products: products.map((val) => {
        return {
          createdAt: val.createdAt,
          updatedAt: val.updatedAt,
          id: val.id,
          price: val.price,
          priceWithDiscount: val.priceWithDiscount,
          amount: val.amount,
          dateStart: val.dateStart,
          dateEnd: val.dateEnd,
          version: {
            createdAt: val.version.createdAt,
            updatedAt: val.version.updatedAt,
            id: val.version.id,
            coverPath: process.env.HOST_ADMIN + '/' + val.version.coverPath,
            bookPath: val.version.bookPath,
            versionActive: val.version.versionActive,
            version: val.version.version,
            book: {
              createdAt: val.version.book.createdAt,
              updatedAt: val.version.book.updatedAt,
              id: val.version.book.id,
              title: val.version.book.title,
              author: val.version.book.author,
              description: val.version.book.description,
            },
          },
          versionSubscriptionPlan: val.versionSubscriptionsPlans,
        } as ProductItem;
      }),
      qrInfo: shopping.qrInfo,
      qrBody: !shopping.qrBody
        ? null
        : {
            currency: shopping.qrBody.currency,
            gloss: shopping.qrBody.gloss,
            amount: shopping.qrBody.amount,
            singleUse: shopping.qrBody.singleUse,
            expirationDate: shopping.qrBody.expirationDate,
            additionalData: shopping.qrBody.additionalData,
            destinationAccountId: shopping.qrBody.destinationAccountId,
          },
      qrBank: shopping.qrPaymentBankNotification || null,
      coupon: shopping.coupon,
      people: {
        createdAt: shopping.people.createdAt,
        updatedAt: shopping.people.updatedAt,
        id: shopping.people.id,
        name: shopping.people.name,
        lastName: shopping.people.lastName,
        email: shopping.people.email,
        photoProfile: shopping.people.photoProfile.startsWith('http')
          ? shopping.people.photoProfile
          : process.env.HOST_ADMIN + shopping.people.photoProfile,
        birthDate: shopping.people.birthDate,
      },
    } as ShoppingDetailsOutput;
  }
  async productAboutToExpire(): Promise<Boolean> {
    const numberOfDays =
      process.env.LIMIT_DAYS_TO_SEND_MESSAGE_NEXT_TO_EXPIRATION.split(' ')
        .map(Number)
        .filter((num) => num >= 0)
        .sort((a, b) => a - b);
    let dateLimit: { start: Date; end: Date }[] = numberOfDays.map((val) => {
      const currentDate = new Date();
      const dateAdd = new Date(
        currentDate.setDate(currentDate.getDate() + val),
      );
      return {
        start: new Date(dateAdd.setHours(0, 0, 0, 0)),
        end: new Date(dateAdd.setHours(23, 59, 59, 59)),
      };
    });
    const products = await this.productsRepository
      .createQueryBuilder('product')
      .leftJoin('product.shopping', 'shopping')
      .where('shopping.paymentStatus = :status', {
        status: EnumPaymentStatus.PAGO_CONFIRMADO,
      })
      .andWhere(
        new Brackets((qb) => {
          dateLimit.forEach((dates, index) => {
            qb.orWhere(
              `product.dateEnd BETWEEN :start${index} AND :end${index}`,
              {
                [`start${index}`]: dates.start,
                [`end${index}`]: dates.end,
              },
            );
          });
        }),
      )
      .getMany();

    const usersWithProductsAboutToExpire = await Promise.all(
      products.map(async (product) => {
        const shopping = await this.shoppingRepository.findOne({
          where: { id: product.shoppingId },
        });
        const person = await this.peopleRepository.findOne({
          where: { id: shopping.peopleId },
        });
        const version = await this.versionRepository.findOne({
          where: { id: product.versionId },
        });
        const book = await this.bookRepository.findOne({
          where: { id: version.bookId },
        });
        const planProduct =
          await this.versionSubscriptionPlanService.getVersionSubscriptionPlan_ById(
            product.versionSubscriptionsPlansId,
          );
        const plan =
          await this.subscriptionPlanService.getSubscriptionPlan_ById(
            planProduct.subscriptionPlanId,
          );
        return { person, product, book, plan };
      }),
    );
    const dataExpiration: DataEmailExpiration[] =
      usersWithProductsAboutToExpire.map((user) => {
        const dateEnd = new Date(user.product.dateEnd);
        const currentDate = new Date();
        const remainingDays = (
          dateEnd.getDate() - currentDate.getDate()
        ).toString();

        const title = user.book.title;
        const email = user.person.email;
        const name = user.person.name + ' ' + user.person.lastName;
        const expirationDate = dateEnd.toLocaleDateString('es-ES');
        const daysLeft =
          remainingDays === '1'
            ? remainingDays + ' día'
            : remainingDays + ' días';
        const url = 'https://economixhub.com/shopping/' + user.book.id;
        const type =
          user.plan.duration_days === 30
            ? '1 mes'
            : user.plan.duration_days === 365
            ? '1 año'
            : 'Sin especificar';

        return {
          title,
          email,
          name,
          expirationDate,
          daysLeft,
          url,
          type,
        };
      });
    dataExpiration.forEach(async (user) => {
      await this.mailService.sendExpirationNotification(user);
    });
    return true;
  }
}
