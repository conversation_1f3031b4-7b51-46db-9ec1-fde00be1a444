import {
  Resolver,
  Query,
  Mutation,
  Args,
  Int,
  Context,
  Parent,
  ResolveField,
} from '@nestjs/graphql';
import { ShoppingService } from './shopping.service';
import { Shopping } from './entities/shopping.entity';
import {
  CreateShopping2Input,
  CreateShoppingInput,
} from './dto/create-shopping.input';
import { JwtAuthGuard } from '../../jwt/guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Summary, Summary2 } from './dto/shopping.output';
import * as GraphQLUpload from 'graphql-upload/GraphQLUpload.js';
import { Upload } from 'graphql-upload/Upload.js';
import { People } from '../peoples/entities/people.entity';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import {
  ProofOfPaymentFromAUser,
  ReportSalesData,
  ReportShopping,
  ReportTotalIncomeMonth,
  ReportTotalSalesBooks<PERSON><PERSON>h,
  ReportUser,
} from './dto/report.output';
import { Throttle } from '@nestjs/throttler';
import { PeoplesService } from '../peoples/peoples.service';
import { ShoppingDetailsOutput } from './dto/shopping-details.output';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';

@Resolver(() => Shopping)
export class ShoppingResolver {
  constructor(
    private readonly shoppingService: ShoppingService,
    private readonly peoplesService: PeoplesService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Mutation(() => Summary, { name: 'purchaseDetail' })
  async purchaseDetail(
    @Args('data', { type: () => CreateShoppingInput })
    data: CreateShoppingInput,
    @Context() context,
  ): Promise<Summary> {
    return this.shoppingService.purchaseDetail(data, context.req.user.sub);
  }

  @UseGuards(JwtAuthGuard)
  @Mutation(() => String, { name: 'confirmPurchaseQR' })
  async confirmPurchase(
    @Args('data', { type: () => CreateShoppingInput })
    data: CreateShoppingInput,
    @Context() context,
  ): Promise<String> {
    return this.shoppingService.confirmPurchase(data, context.req.user.sub);
  }

  @Throttle({ default: { limit: 1, ttl: 180000 } })
  @UseGuards(JwtAuthGuard)
  @Mutation(() => String, { name: 'confirmPurchaseQR2' })
  async confirmPurchase2(
    @Args('data', { type: () => CreateShopping2Input })
    data: CreateShopping2Input,
    @Context() context,
  ): Promise<String> {
    return this.shoppingService.confirmPurchase2(data, context.req.user.sub);
  }

  @UseGuards(JwtAuthGuard)
  @Mutation(() => Summary2, { name: '' })
  async purchaseDetail2(
    @Args('data', { type: () => CreateShopping2Input })
    data: CreateShopping2Input,
    @Context() context,
  ): Promise<Summary2> {
    return this.shoppingService.purchaseDetail2(data, context.req.user.sub);
  }

  @UseGuards(JwtAuthGuard)
  @Mutation(() => Boolean, { name: 'confirmPurchaseProofOfPayment' })
  async confirmPurchaseProofOfPayment(
    @Args('data', { type: () => CreateShoppingInput })
    data: CreateShoppingInput,
    @Context() context,
  ): Promise<Boolean> {
    return this.shoppingService.confirmPurchaseProofOfPayment(
      data,
      context.req.user.sub,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Mutation(() => Shopping, { name: 'confirmPurchaseProofOfPayment2' })
  async confirmPurchaseProofOfPayment2(
    @Args('data', { type: () => CreateShopping2Input })
    data: CreateShopping2Input,
    @Context() context,
  ): Promise<Shopping> {
    return this.shoppingService.confirmPurchaseProofOfPayment2(
      data,
      context.req.user.sub,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Mutation(() => Boolean, { name: 'uploadProofOfPayment' })
  async uploadProofOfPayment(
    @Args({ name: 'proofOfPayment', type: () => GraphQLUpload })
    { createReadStream, filename, mimetype, encoding }: Upload,
    @Args({ name: 'shoppingId', type: () => String }) shoppingId: string,
    @Context() context,
  ): Promise<Boolean> {
    // Validación del tamaño del archivo
    const stream = createReadStream();
    let totalSize = 0;
    for await (const chunk of stream) {
      totalSize += chunk.length;
      if (totalSize > 2 * 1024 * 1024) {
        // 2 MB en bytes
        throw new Error(
          'El archivo es demasiado grande, el tamaño máximo es de 2 MB',
        );
      }
    }

    return this.shoppingService.registerProofOfPayment(
      context.req.user.sub,
      { createReadStream, filename, mimetype, encoding },
      shoppingId,
    );
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'enableAccountProofOfPayment' })
  async enableAccountProofOfPayment(
    @Args({ name: 'shoopingId', type: () => String })
    shoppingId: string,
  ): Promise<Boolean> {
    return this.shoppingService.enableAccountProofOfPayment(shoppingId);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'rejectProofOfPayment' })
  async rejectProofOfPayment(
    @Args({ name: 'shoopingId', type: () => String })
    shoppingId: string,
    @Args({
      name: 'paymentRejectionReason',
      type: () => String,
      nullable: true,
    })
    paymentRejectionReason: string | null,
  ): Promise<Boolean> {
    return this.shoppingService.rejectProofOfPayment(
      shoppingId,
      paymentRejectionReason,
    );
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Shopping])
  async proofOfPayment() {
    return this.shoppingService.listAllProofOfPayment();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Shopping])
  async proofOfPaymentPending() {
    return this.shoppingService.listAllProofOfPayment2();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @ResolveField(() => People)
  async client(@Parent() shopping: Shopping): Promise<People> {
    // return await this.shoppingService.findPeopleProofOfPayment(
    //   shopping.peopleId,
    // );
    return await this.peoplesService.getPersona_ById(shopping.peopleId);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [ReportShopping])
  async reportShopping() {
    const res = this.shoppingService.reportShopping();
    return res;
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [ReportUser])
  async reportUser() {
    return this.shoppingService.reportUser();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [ReportTotalIncomeMonth])
  async incomeByMonths() {
    return this.shoppingService.incomeByMonths();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [ReportTotalSalesBooksMonth])
  async quantitySalesBooksMonths() {
    console.log('quantitySalesBooksMonths');
    return this.shoppingService.quantitySalesBooksMonths();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Number)
  async incomeByRank( 
    @Args({ name: 'startDate', type: () => Date }) startDate: Date,
    @Args({ name: 'endDate', type: () => Date }) endDate: Date,
  ) {
    return this.shoppingService.incomeByRank(startDate, endDate);
  }
  @Query(() => [ReportSalesData])
  async getAllShopping() {
    return this.shoppingService.getAllShopping();
  }

  @Query(() => ProofOfPaymentFromAUser)
  async getProofOfPaymentById(
    @Args({ name: 'id', type: () => String }) id: string,
  ) {
    return this.shoppingService.getProofOfPaymentById(id);
  }

  @Query(() => ShoppingDetailsOutput)
  async getPurchaseDetails(
    @Args({ name: 'id', type: () => String }) id: string,
  ) {
    return this.shoppingService.getPurchaseDetails(id);
  }
  @Query(() => Boolean)
  async productAboutToExpire(): Promise<Boolean> {
    return this.shoppingService.productAboutToExpire();
  }
}
