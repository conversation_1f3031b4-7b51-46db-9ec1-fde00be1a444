import { Module } from '@nestjs/common';
import { ShoppingService } from './shopping.service';
import { ShoppingResolver } from './shopping.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Shopping } from './entities/shopping.entity';
import { Book } from '../books/entities/book.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import { Version } from '../versions/entities/version.entity';
import { People } from '../peoples/entities/people.entity';
import { Products } from '../products/entities/products.entity';
import { MailModule } from 'src/mail/mail.module';
import { SubscriptionPlan } from '../subscription-plans/entities/subscription-plan.entity';
import { VersionsModule } from '../versions/versions.module';
import { VersionSubscriptionPlansModule } from '../version-subscription-plans/version-subscription-plans.module';
import { BooksModule } from '../books/books.module';
import { BankModule } from 'src/bank/bank.module';
import { SubscriptionPlansModule } from '../subscription-plans/subscription-plans.module';
import { PeoplesModule } from '../peoples/peoples.module';
import { CouponsModule } from '../coupons/coupons.module';
import { ProductModule } from '../products/products.module';
import { AttachedFilesModule } from '../attached-files/attached-files.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Shopping,
      Book,
      Coupon,
      Version,
      People,
      Products,
      SubscriptionPlan,
    ]),
    VersionsModule,
    VersionSubscriptionPlansModule,
    SubscriptionPlansModule,
    MailModule,
    BooksModule,
    BankModule,
    PeoplesModule,
    CouponsModule,
    ProductModule,
    AttachedFilesModule,
  ],
  providers: [ShoppingResolver, ShoppingService],
})
export class ShoppingModule {}
