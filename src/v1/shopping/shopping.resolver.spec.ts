import { Test, TestingModule } from '@nestjs/testing';
import { ShoppingResolver } from './shopping.resolver';
import { ShoppingService } from './shopping.service';

describe('ShoppingResolver', () => {
  let resolver: ShoppingResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ShoppingResolver, ShoppingService],
    }).compile();

    resolver = module.get<ShoppingResolver>(ShoppingResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
