import { ObjectType, Field, Int, Float } from '@nestjs/graphql';
import { IsUUID, IsBoolean, IsDate, IsString, IsNumber } from 'class-validator';
import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

import { Version } from './../../versions/entities/version.entity';
import { Credentials } from 'src/v1/credentials/entities/credentials.entity';
import { Coupon } from 'src/v1/coupons/entities/coupon.entity';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { People } from 'src/v1/peoples/entities/people.entity';

import { Products } from './../../products/entities/products.entity';
import { number } from 'mathjs';

// export enum EnumPaymentStatus {
//   PENDIENTE = 'PENDIENTE',
//   RECHAZADO = 'RECHAZADO',
//   COMPLETADO = 'COMPLETADO',
//   SIN_ESTADO = '',
// }

export enum EnumPaymentStatus {
  SIN_ESTADO = '',
  QR_GENERADO = 'QR_GENERADO',
  COMPROBANTE_EN_REVISION = 'COMPROBANTE_EN_REVISIÓN',
  COMPROBANTE_RECHAZADO = 'COMPROBANTE_RECHAZADO',
  PAGO_CONFIRMADO = 'PAGO_CONFIRMADO',
}

@ObjectType()
export class QrPaymentBankNotification {
  @Field(() => String)
  QRId: string;

  @Field(() => String)
  Gloss: string;

  @Field(() => Int)
  sourceBankId: number;

  @Field(() => String)
  originName: string;

  @Field(() => String)
  VoucherId: string;

  @Field(() => String)
  TransactionDateTime: string;

  @Field(() => String)
  additionalData: string;
}

@ObjectType()
export class QrInfo {
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Field(() => String, { nullable: true })
  qr: string;

  @IsBoolean()
  @Field(() => Boolean, { nullable: true })
  success: boolean;

  @IsString()
  @Field(() => String, { nullable: true })
  message: string;
}

@ObjectType()
export class QrBody {
  @IsString()
  @Field(() => String, { nullable: true })
  currency: string | null;

  @IsString()
  @Field(() => String, { nullable: true })
  gloss: string;

  @IsNumber()
  @Field(() => Float)
  amount: number;

  @IsBoolean()
  @Field(() => Boolean)
  singleUse: boolean;

  @IsDate()
  @Field(() => String)
  expirationDate: String;

  @IsString()
  @Field(() => String, { nullable: true })
  additionalData: string;

  @IsString()
  @Field(() => String, { nullable: true })
  destinationAccountId: string;
}

@Entity()
@ObjectType()
export class Shopping extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Field(() => String, { nullable: true })
  @Column({ type: 'text', default: null })
  proofOfpayment?: string | null;

  @IsNumber()
  @Field({ nullable: true })
  @Column({ type: 'decimal', precision: 19, scale: 2 })
  amountPay: number | null;

  // @Column({ type: 'timestamp', default: null })
  // @Field(() => Date)
  // dateStart: Date;

  // @Column({ type: 'timestamp', default: null })
  // @Field(() => Date)
  // dateEnd: Date;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => QrInfo, { nullable: true })
  @Column({ type: 'simple-json', default: null })
  qrInfo: QrInfo | null;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => QrBody, { nullable: true })
  @Column({ type: 'simple-json', default: null })
  qrBody: QrBody | null;

  @Field({ nullable: true })
  @Column({ type: 'simple-json', default: null })
  qrPaymentBankNotification: QrPaymentBankNotification | null;

  @IsBoolean()
  @Field(() => Boolean, { nullable: true })
  @Column({ type: 'boolean', default: null })
  paymentMade: boolean | null;

  @Field({ nullable: true })
  @Column({
    type: 'enum',
    enum: EnumPaymentStatus,
    nullable: false,
    default: EnumPaymentStatus.SIN_ESTADO,
  })
  paymentStatus: EnumPaymentStatus;

  @Column({ type: 'text', nullable: true })
  paymentRejectionReason?: string | null;

  @OneToMany(() => Products, (products) => products.shopping, { eager: true })
  product: Products[];

  @Field(() => People)
  @ManyToOne(() => People, (people) => people.shopping, { eager: true })
  people: People | null;

  @Field(() => Coupon, { nullable: true })
  @ManyToOne(() => Coupon, (coupon) => coupon.shopping, { eager: true })
  coupon: Coupon | null;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @RelationId((shopping: Shopping) => shopping.people)
  peopleId: string | null;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @RelationId((shopping: Shopping) => shopping.coupon)
  couponId: string | null;
}

@ObjectType()
export class DataEmailExpiration {
  //esto es para enviar los datos para enviar el email de expiracion, estos son los datos que se enviaran:
  //titulo del libro,
  // email del usuario,
  // nombre del usuario,
  // fecha de expiracion,
  // dias restantes,
  // la url para comprar el libro,
  // el email de soporte,
  // el tipo de suscripcion,

  @Field(() => String)
  title: string;

  @Field(() => String)
  email: string;

  @Field(() => String)
  name: string;

  @Field(() => String)
  expirationDate: string;

  @Field(() => String)
  daysLeft: string;

  @Field(() => String)
  url: string;

  @Field(() => String)
  type: string;
}
