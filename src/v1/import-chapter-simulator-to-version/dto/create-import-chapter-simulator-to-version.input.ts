import { InputType, Int, Field } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';

@InputType()
export class CreateImportChapterSimulatorToVersionInput {
  @Field(() => Int, { description: 'Example field (placeholder)' })
  exampleField: number;
}

@InputType()
class ChapterIds {
  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  chapterId: string;

  @Field(() => [String])
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  simulatorIds: string[];
}

@InputType()
export class ChaptersSimulatorsIdsDTO {
  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  currentVersion: string;

  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  bookId: string;

  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  versionId: string;

  @Field(() => [ChapterIds])
  chapterIds: ChapterIds[];
}

@InputType()
export class SimulatorsToChapterDTO {
  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  currentVersion: string;

  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  bookId: string;

  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  versionId: string;

  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  chapterId: string;

  @Field(() => [String])
  simulatorIds: string[];
}
