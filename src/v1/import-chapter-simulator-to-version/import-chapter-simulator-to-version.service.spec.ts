import { Test, TestingModule } from '@nestjs/testing';
import { ImportChapterSimulatorToVersionService } from './import-chapter-simulator-to-version.service';

describe('ImportChapterSimulatorToVersionService', () => {
  let service: ImportChapterSimulatorToVersionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ImportChapterSimulatorToVersionService],
    }).compile();

    service = module.get<ImportChapterSimulatorToVersionService>(ImportChapterSimulatorToVersionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
