import { Module } from '@nestjs/common';
import { ImportChapterSimulatorToVersionService } from './import-chapter-simulator-to-version.service';
import { ImportChapterSimulatorToVersionResolver } from './import-chapter-simulator-to-version.resolver';
import { ChaptersModule } from '../chapters/chapters.module';
import { VersionsModule } from '../versions/versions.module';
import { SimulatorsModule } from '../simulators/simulators.module';
import { FormulasModule } from '../formulas/formulas.module';
import { SheetsModule } from '../sheets/sheets.module';

@Module({
  imports: [VersionsModule, ChaptersModule, SimulatorsModule, FormulasModule, SheetsModule],
  providers: [
    ImportChapterSimulatorToVersionResolver,
    ImportChapterSimulatorToVersionService,
  ],
})
export class ImportChapterSimulatorToVersionModule {}
