import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { ImportChapterSimulatorToVersionService } from './import-chapter-simulator-to-version.service';
import { ImportChapterSimulatorToVersion } from './entities/import-chapter-simulator-to-version.entity';
import {
  ChaptersSimulatorsIdsDTO,
  CreateImportChapterSimulatorToVersionInput,
  SimulatorsToChapterDTO,
} from './dto/create-import-chapter-simulator-to-version.input';

@Resolver(() => ImportChapterSimulatorToVersion)
export class ImportChapterSimulatorToVersionResolver {
  constructor(
    private readonly importChapterSimulatorToVersionService: ImportChapterSimulatorToVersionService,
  ) {}

  @Mutation(() => String)
  createImportChapterSimulatorToVersion(
    @Args('body') body: ChaptersSimulatorsIdsDTO,
  ) {
    return this.importChapterSimulatorToVersionService.create(body);
  }

  @Mutation(() => String)
  createImportSimulatorsToChapter(
    @Args('body') body: SimulatorsToChapterDTO,
  ) {
    return this.importChapterSimulatorToVersionService.importSimulatorsToChapter(body);
  }

  @Query(() => [ImportChapterSimulatorToVersion], {
    name: 'importChapterSimulatorToVersion',
  })
  findAll() {
    return this.importChapterSimulatorToVersionService.findAll();
  }
}
