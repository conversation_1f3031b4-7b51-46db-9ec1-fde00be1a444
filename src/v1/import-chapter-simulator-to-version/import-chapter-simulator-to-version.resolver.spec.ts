import { Test, TestingModule } from '@nestjs/testing';
import { ImportChapterSimulatorToVersionResolver } from './import-chapter-simulator-to-version.resolver';
import { ImportChapterSimulatorToVersionService } from './import-chapter-simulator-to-version.service';

describe('ImportChapterSimulatorToVersionResolver', () => {
  let resolver: ImportChapterSimulatorToVersionResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ImportChapterSimulatorToVersionResolver, ImportChapterSimulatorToVersionService],
    }).compile();

    resolver = module.get<ImportChapterSimulatorToVersionResolver>(ImportChapterSimulatorToVersionResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
