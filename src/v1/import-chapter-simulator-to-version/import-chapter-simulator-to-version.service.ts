import { Injectable } from '@nestjs/common';
import {
  ChaptersSimulatorsIdsDTO,
  CreateImportChapterSimulatorToVersionInput,
  SimulatorsToChapterDTO,
} from './dto/create-import-chapter-simulator-to-version.input';
import { UpdateImportChapterSimulatorToVersionInput } from './dto/update-import-chapter-simulator-to-version.input';
import { SimulatorsService } from '../simulators/simulators.service';
import { ChaptersService } from '../chapters/chapters.service';
import { VersionsService } from '../versions/versions.service';
import { Chapter } from '../chapters/entities/chapter.entity';
import { Simulator } from '../simulators/entities/simulator.entity';
import { FormulasService } from '../formulas/formulas.service';
import { SheetsService } from '../sheets/sheets.service';

interface ChapterSimulatorIMP {
  chapter: Chapter;
  simulators: Simulator[];
}

@Injectable()
export class ImportChapterSimulatorToVersionService {
  constructor(
    private chapterService: ChaptersService,
    private versionService: VersionsService,
    private simulatorsService: SimulatorsService,
    private formulasService: FormulasService,
    private sheetsService: SheetsService,
  ) {}
  async create(body: ChaptersSimulatorsIdsDTO) {
    // async registerChapter(body: ChaptersSimulatorsIdsDTO) {
    console.log('This is body: ', body);
    const findCurrentVersion = await this.versionService.findOne(
      body.currentVersion,
    );
    const findVersion = await this.versionService.findOne(body.versionId);
    const findChaptersSimulators: ChapterSimulatorIMP[] = [];
    // Verifica que los capítulos existan
    for (const item of body.chapterIds) {
      let findSimulators = [];
      const chapter = await this.chapterService.getChapter_ById(item.chapterId);
      // Si existe el capítulo, guardamos los simuladores
      if (chapter) {
        for (const simulatorId of item.simulatorIds) {
          // Verifica que los simuladores existan
          const simulator = await this.simulatorsService.getSimulatorById(
            simulatorId,
          );
          if (simulator) findSimulators.push(simulator); // Si existe, lo añadimos al array
        }
        // Ahora que todos los simuladores están cargados, guarda el capítulo y sus simuladores
        findChaptersSimulators.push({
          chapter,
          simulators: findSimulators,
        });
      }
    }
    // ======== Registrar capitulos simuladores formulas sheets =========
    for (const { chapter, simulators } of findChaptersSimulators) {
      const chapters = await this.chapterService.getChapters_ByVersionId(
        findCurrentVersion.id,
      );
      // Verificar si ya existe capitulo
      const checkDuplicate = await this.chapterService.checkDuplicateChaper(
        chapter,
        findCurrentVersion,
      );
      console.log('🚀 checkDuplicate:', checkDuplicate);
      if (!checkDuplicate) {
        const newChapter =
          await this.chapterService.capituloNuevoConNumeroCapitulo(
            chapter,
            chapters.length + 1,
            findCurrentVersion.id,
          );
        await this.chapterService.saveChapter(newChapter);
        // ========== Registrar simuladores
        const simulatorsLength =
          await this.simulatorsService.getSimulators_ByChapterId(chapter.id);
        // console.log('longitud de simulador: ', simulators.length);
        for (const simulator of simulators) {
          const newSimulator =
            await this.chapterService.simuladorNuevoWithSimulatorNumber(
              simulator,
              simulatorsLength.length + 1,
              newChapter.id,
            );
          // console.log(newSimulator);
          await this.simulatorsService.simulatorRepository.save(newSimulator);
          const formulas = await this.formulasService.getFormulas_BySimulatorId(
            simulator.id,
          );
          // console.log('Formulas encontradas: ', formulas);
          for (const form of formulas) {
            const newFormula = await this.chapterService.formulaNueva(
              form,
              newSimulator.id,
            );
            // console.log('Nueva Formulas: ', newFormula);
            await this.formulasService.formulaRepository.save(newFormula);
          }
          const sheets = await this.sheetsService.getSheets_BySimulatorId2(
            simulator.id,
          );
          // console.log('Sheets encontradas: ', sheets);
          for (const sheet of sheets) {
            const newSheet = await this.chapterService.sheetNueva(
              sheet,
              newSimulator.id,
            );
            // console.log('New Sheet: ', newSheet);
            await this.sheetsService.sheetRepository.save(newSheet);
          }
        }
      }
    }
    return await 'Capitulos y simuladores importados correctamente mas no los ya existentes';
  }

  // ===================  IMPORTAR SIMULADORES A UN CAPITULO  =========
  async importSimulatorsToChapter(body: SimulatorsToChapterDTO) {
    // console.log('This is body: ', body);
    const findCurrentVersion = await this.versionService.findOne(
      body.currentVersion,
    );
    const findVersion = await this.versionService.findOne(body.versionId);
    if (!findVersion) return 'La version no existe';
    const findChapter = await this.chapterService.getChapter_ById(
      body.chapterId,
    );
    if (!findChapter) return 'El capítulo no existe';
    // Verifica que los simuladores existan
    let findSimulators = [];
    for (const simulatorId of body.simulatorIds) {
      const simulator = await this.simulatorsService.getSimulatorById(
        simulatorId,
      );
      if (simulator) findSimulators.push(simulator); // Si existe, lo añadimos al array
    }
    console.log(findSimulators);
    // ======== Registrar simuladores formulas sheets =========
    for (const simulator of findSimulators) {
      const checkDuplicateSimulator =
        await this.chapterService.checkDuplicateSimulatorInChapter(
          findChapter,
          simulator,
        );
      console.log('checkDuplicateSimulator', checkDuplicateSimulator);
      if (!checkDuplicateSimulator) {
        const simulatorsLength =
          await this.simulatorsService.getSimulators_ByChapterId(
            findChapter.id,
          );
        const newSimulator =
          await this.chapterService.simuladorNuevoWithSimulatorNumber(
            simulator,
            simulatorsLength.length + 1,
            findChapter.id,
          );
        // console.log(newSimulator);
        await this.simulatorsService.simulatorRepository.save(newSimulator);
        const formulas = await this.formulasService.getFormulas_BySimulatorId(
          simulator.id,
        );
        // console.log('Formulas encontradas: ', formulas);
        for (const form of formulas) {
          const newFormula = await this.chapterService.formulaNueva(
            form,
            newSimulator.id,
          );
          // console.log('Nueva Formulas: ', newFormula);
          await this.formulasService.formulaRepository.save(newFormula);
        }
        const sheets = await this.sheetsService.getSheets_BySimulatorId2(
          simulator.id,
        );
        // console.log('Sheets encontradas: ', sheets);
        for (const sheet of sheets) {
          const newSheet = await this.chapterService.sheetNueva(
            sheet,
            newSimulator.id,
          );
          // console.log('New Sheet: ', newSheet);
          await this.sheetsService.sheetRepository.save(newSheet);
        }
      }
    }
    return await 'Simuladores importados correctamente mas no los ya existentes';
  }

  findAll() {
    return `This action returns all importChapterSimulatorToVersion`;
  }
}

// INSERTAR CAPITULOS  21 rows
// INSERTAR SIMULADORES  63 rows
// INSERTAR FORMULAS 500 rows
// INSERTAR SHEETS 500 rows
