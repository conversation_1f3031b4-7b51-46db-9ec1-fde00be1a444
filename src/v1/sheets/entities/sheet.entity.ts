import { ObjectType, Field, Int } from '@nestjs/graphql';
import {
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

//entities
import { Simulator } from './../../simulators/entities/simulator.entity';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { IsString, IsBoolean, IsArray, IsInt } from 'class-validator';

export enum EnumSheetType {
  'STATIC' = 'static',
  'N_ROWS_INPUT_DATA' = 'n_rows_input_data',
  'N_ROWS_CALCULATIONS' = 'n_rows_calculations',
}

@ObjectType()
export class Items {
  @IsString()
  @Field()
  value: string;

  @IsBoolean()
  @Field((type) => Boolean)
  editable: boolean;

  @IsString()
  @Field()
  columnName: string;

  @IsInt()
  @Field(() => Int)
  row: number;

  @IsBoolean()
  @Field(() => Boolean, { nullable: true })
  bold?: boolean;

  @IsInt()
  @Field(() => Int, { nullable: true })
  orderNumber?: number;

  @IsArray()
  @Field((type) => [Int])
  position: number[];

  @IsBoolean()
  @Field((type) => Boolean)
  show: Boolean;
}

@Entity()
@ObjectType()
export class Sheet extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Column()
  @Field()
  name: string;

  @IsInt()
  @Column({ type: 'integer' })
  @Field(() => Int)
  sheetNumber: number;

  @Field( {nullable:true} )
  @Column({ type: 'enum', enum: EnumSheetType, default: EnumSheetType.STATIC })
  type: EnumSheetType;

  @IsArray()
  @Column({ type: 'json' })
  @Field(() => [[Items]])
  data: Items[][];

  @IsBoolean()
  @Column({ type: 'boolean' })
  @Field(() => Boolean)
  show: boolean;

  @IsInt()
  @Column({ type: 'integer', default: null })
  @Field(() => Int, { nullable: true })
  orderNumber?: number;

  @ManyToOne(() => Simulator, (simulator) => simulator.sheets, )
  simulator: Simulator;

  @RelationId((sheet: Sheet) => sheet.simulator)
  simulatorId: string;
}
