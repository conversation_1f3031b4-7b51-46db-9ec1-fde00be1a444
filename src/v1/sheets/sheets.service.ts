import { Injectable } from '@nestjs/common';
import { Sheet } from './entities/sheet.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SheetOutput } from './dto/sheet.output';
import { IsNull, Repository } from 'typeorm';
import { PeoplesService } from '../peoples/peoples.service';

@Injectable()
export class SheetsService {
  constructor(
    @InjectRepository(Sheet) public sheetRepository: Repository<Sheet>,
    private peopleService: PeoplesService,
  ) {}

  async saveSheets(data: Sheet[]): Promise<Sheet[]> {
    return await this.sheetRepository.save(data);
  }

  async findAllSheet(): Promise<SheetOutput[]> {
    return await this.sheetRepository.find({
      where: { deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });
  }

  async getSheets_BySimulatorId(
    // idPeople: string,
    simulatorId: string,
  ): Promise<SheetOutput[]> {
    return await this.sheetRepository
      .createQueryBuilder('sheet')
      .leftJoinAndSelect('sheet.simulator', 'simulator')
      .where('sheet.simulatorId = :simulatorId', { simulatorId })
      .orderBy('sheet.sheetNumber', 'ASC')
      .getMany();
  }

  async getSheets_BySimulatorId2(
    simulatorId: string,
  ): Promise<Sheet[]> {
    return await this.sheetRepository.find({
      where: { simulator:{ id:simulatorId }, deletedAt: IsNull() },
      order: { sheetNumber: 'ASC' },
    });
  }
  

  async deleteAllSheet(simulatorId: string): Promise<Boolean> {
    await this.sheetRepository
      .createQueryBuilder('sheet')
      .delete()
      .from(Sheet)
      .where('simulatorId = :simulatorId', { simulatorId })
      .execute();

    return true;
  }

  async findOneSheetAdmin(simulatorId: string): Promise<SheetOutput[]> {
    let data = await this.sheetRepository
      .createQueryBuilder('sheet')
      .leftJoinAndSelect('sheet.simulator', 'simulator')
      .where('sheet.simulatorId = :simulatorId', { simulatorId })
      .getMany();

    // data.filter(({})=>)

    return data;
  }
}
