import { ObjectType, Field, Int } from '@nestjs/graphql';
import { EnumSheetType, Items } from './../entities/sheet.entity';
import { IsUUID, IsString, IsBoolean, IsInt, IsArray } from 'class-validator';

@ObjectType()
export class SheetOutput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Field()
  name: string;

  @IsBoolean()
  @Field()
  show: Boolean;

  @IsInt()
  @Field(() => Int, { nullable: true })
  orderNumber?: number;

  @IsInt()
  @Field(() => Int)
  sheetNumber: number;

  @Field()
  type: EnumSheetType;

  @IsArray()
  @Field((type) => [[Items]])
  data: Items[][];

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  simulatorId: string;
}
