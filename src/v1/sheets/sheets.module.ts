import { Module } from '@nestjs/common';
import { SheetsService } from './sheets.service';
import { SheetsResolver } from './sheets.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Sheet } from './entities/sheet.entity';
import { PeoplesModule } from '../peoples/peoples.module';
@Module({
  imports: [TypeOrmModule.forFeature([Sheet]), PeoplesModule],
  providers: [SheetsResolver, SheetsService],
  exports: [SheetsService],
})
export class SheetsModule {}
