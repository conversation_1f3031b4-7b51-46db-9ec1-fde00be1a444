import { Resolver, Query, Mutation, Args, Int, Context } from '@nestjs/graphql';
import { PeoplesService } from './peoples.service';
// import { People } from './entities/people.entity';
import { PeopleInput } from './dto/create-people.input';
import { UpdatePeopleInput } from './dto/update-people.input';
import { People } from './entities/people.entity';
import { CodeVerificationInput } from './dto/verify-code.input';
import { Credentials } from '../credentials/entities/credentials.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/jwt/guards/auth.guard';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { PeopleDataFilterOutput, PeopleOutput } from './dto/people-output';
import { PeopleInputFilter } from './dto/people-filters.input';
import { CommentInput } from './dto/comment.input';
import { Shopping } from '../shopping/entities/shopping.entity';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Public } from 'src/common/decorators/public.decorator';

@Resolver(() => People)
export class PeoplesResolver {
  constructor(private readonly peoplesService: PeoplesService) {}

  @Public()
  @Mutation(() => Boolean, { name: 'confirmCode' })
  async confirmCode(
    @Args('dataUser') dataUser: CodeVerificationInput,
  ): Promise<boolean> {
    await this.peoplesService.confirmCode(
      dataUser.email,
      dataUser.codeVerification,
    );

    return true;
  }

  @Public()
  @Mutation(() => Boolean, { name: 'sendComment' })
  async sendComment(@Args('data') data: CommentInput): Promise<Boolean> {
    return await this.peoplesService.sendComment(data);
  }

  @Public()
  @Mutation(() => Boolean, { name: 'recoveryAccountUser' })
  async ount(@Args('email') email: string): Promise<boolean> {
    return await this.peoplesService.recoveryAccount(email);
  }

  @Public()
  @Query(() => People, { name: 'verifyTokenRecovery' })
  async verifyTokenRecovery(@Args('token') token: string): Promise<People> {
    return await this.peoplesService.verifyTokenRecovery(token);
  }

  @UseGuards(JwtAuthGuard)
  @Query(() => PeopleOutput, { name: 'getProfileUser' })
  async getProfile(@Context() context): Promise<PeopleOutput> {
    // console.log("getProfile")
    // console.log(context.req.user);
    return await this.peoplesService.findOneUser(context.req.user.sub);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'updateEmailUser' })
  async updateEmail(
    @Args('id') id: string,
    @Args('email') email: string,
  ): Promise<boolean> {
    return await this.peoplesService.updateEmail(id, email);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => PeopleOutput, { name: 'findOneUser' })
  async findOne(@Args('id') id: string): Promise<PeopleOutput> {
    return await this.peoplesService.findOneUser(id);
  }
  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [People], { name: 'findAllUsers' })
  async findAll(): Promise<People[]> {
    return await this.peoplesService.findAll();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => PeopleDataFilterOutput, { name: 'findAllUsersFilter' })
  async findAllFIlter(
    @Args('filterData') filterData: PeopleInputFilter,
  ): Promise<PeopleDataFilterOutput> {
    return await this.peoplesService.findAllFilter(filterData);
  }

  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Boolean, { name: 'suspendAccountUser' })
  async suspendAccount(@Args('id') id: string): Promise<boolean> {
    return await this.peoplesService.suspendAccount(id);
  }

  @Mutation(() => People)
  createPeople(@Args('createPeopleInput') createPeopleInput: PeopleInput) {
    return this.peoplesService.create(createPeopleInput);
  }
  @UseGuards(JwtAuthGuard)
  @Mutation(() => Boolean)
  updatePeople(
    @Context() context,
    @Args('peopleData') peopleData: PeopleInput,
  ) {
    return this.peoplesService.update(context.req.user.sub, peopleData);
  }

  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean)
  updatePeopleAdmin(
    @Context() context,
    @Args('peopleData') peopleData: PeopleInput,
  ) {
    return this.peoplesService.updateAdminPeople(
      context.req.user.sub,
      peopleData,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Query(() => [Shopping])
  proofOfPaymentPendingState(@Context() context): Promise<Shopping[]> {
    console.log("aqui");
    return this.peoplesService.proofOfPaymentPendingState(context.req.user.sub);
  }

  @UseGuards(JwtAuthGuard)
  @Query(() => [Shopping])
  getShoppingByPeopleId (@Context() context): Promise<Shopping[]> {
    return this.peoplesService.getShoppingByPeopleId(context.req.user.sub);
  }
  // @Mutation(() => People)
  // removePeople(@Args('id', { type: () => Int }) id: number) {
  //   return this.peoplesService.remove(id);
  // }
}
