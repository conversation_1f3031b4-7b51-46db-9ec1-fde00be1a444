import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { PeopleInput } from './dto/create-people.input';
import { People } from './entities/people.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  <PERSON>ike,
  IsNull,
  LessThanOrEqual,
  Like,
  MoreThanOrEqual,
  Not,
  Repository,
} from 'typeorm';

import { v4 as uuidv4 } from 'uuid';
import { CredentialsService } from '../credentials/credentials.service';

import { JwtService } from '@nestjs/jwt';

import { MailService } from './../../mail/mail.service';
import { Credentials } from '../credentials/entities/credentials.entity';
import { PeopleDataFilterOutput, PeopleOutput } from './dto/people-output';
import { Admin } from '../admins/entities/admin.entity';
import { EnumPaymentStatus, Shopping } from '../shopping/entities/shopping.entity';
import { Products } from '../products/entities/products.entity';
import { Version } from '../versions/entities/version.entity';
import { Book } from '../books/entities/book.entity';
import { Simulator } from '../simulators/entities/simulator.entity';
import { PeopleInputFilter } from './dto/people-filters.input';
import { CommentInput } from './dto/comment.input';
import { CommentsService } from '../comments/comments.service';

export interface ISubcriberForBook {
  people_id: string;
  people_name: string;
  people_lastName: string;
  people_email: string;
  product_dateStart: Date;
  product_dateEnd: Date;
  shopping_paymentStatus: EnumPaymentStatus;
  shopping_id: string;
  product_id: string;
  version_id: string;
  version_bookId: string;
  book_title: string;
  version_version: string;
}
@Injectable()
export class PeoplesService {
  constructor(
    @InjectRepository(People)
    private peopleRepository: Repository<People>,
    @InjectRepository(Credentials)
    private readonly credentialRepository: Repository<Credentials>,
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,

    @InjectRepository(Shopping)
    private readonly shoppingRepository: Repository<Shopping>,

    private readonly mailService: MailService,
    private jwtService: JwtService,
    private commentsService: CommentsService,
  ) {}
  async create(createPeopleInput: PeopleInput): Promise<People> {
    return this.peopleRepository.save({ ...createPeopleInput, id: uuidv4() });
  }

  generateCode(): string {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789#_%$&/()?¡';
    return Array.from({ length: 6 }, () =>
      characters.charAt(Math.floor(Math.random() * characters.length)),
    ).join('');
  }

  async findOne(idPeople: string): Promise<People> {
    const res = await this.peopleRepository.findOne({
      where: {
        id: idPeople,
        deletedAt: IsNull(),
      },
    });

    if (!res.photoProfile.match('https')) {
      res.photoProfile = process.env.HOST_ADMIN + res.photoProfile;
    } else {
      res.photoProfile = res.photoProfile;
    }
    return res;
  }

  async getPersona_ById(idPeople: string): Promise<People> {
    const res = await this.peopleRepository.findOne({
      where: {
        id: idPeople,
        deletedAt: IsNull(),
      },
    });
    if (!res) {
      throw new NotFoundException('La Persona no existe a');
    }
    if (!res.photoProfile.startsWith('http'))
      res.photoProfile = process.env.HOST_ADMIN + res.photoProfile;
    return res;
  }

  async findOneUser(idPeople: string): Promise<PeopleOutput> {
    const res = await this.peopleRepository.findOne({
      where: {
        id: idPeople,
        deletedAt: IsNull(),
      },
    });

    const credentialUser = await this.credentialRepository.findOne({
      where: {
        people: {
          id: res.id,
        },
        deletedAt: IsNull(),
      },
    });

    if (!res.photoProfile.match('https')) {
      res.photoProfile = process.env.HOST_ADMIN + res.photoProfile;
    } else {
      res.photoProfile = res.photoProfile;
    }

    return {
      ...res,
      updatedAt: credentialUser.updatedAt,
      credentialExists: credentialUser?.password ? true : false,
    };
  }

  async getPersona_ByEmail(email: string): Promise<People> {
    const res = await this.peopleRepository.findOne({
      where: {
        email,
        deletedAt: IsNull(),
      },
    });
    // if(!res){
    //   throw new NotFoundException('La Persona no existe b');
    // }
    return res;
  }

  async confirmCode(email: string, code: string): Promise<boolean> {
    const res = await this.peopleRepository.findOne({
      where: { email, codeVerification: code, deletedAt: IsNull() },
    });

    if (!res) throw new Error('Incorrect code.');

    return (
      (
        await this.peopleRepository.update(
          { id: res.id, deletedAt: IsNull() },
          {
            emailVerified: true,
          },
        )
      ).affected === 1
    );
  }

  async recoveryAccount(email: string): Promise<boolean> {
    const peopleData = await this.peopleRepository.findOne({
      where: {
        email,
        deletedAt: IsNull(),
      },
    });

    if (!peopleData) throw new Error('The email does not exist.');

    const newToken = await this.jwtService.sign(
      {
        id: peopleData.id,
      },
      {
        secret: process.env.JWT_SECRET_KEY,
      },
    );

    const link = `${process.env.HOST_CLIENT}/recovery_account/${newToken}`;

    await this.mailService.sendLinkRecoveryAccount(peopleData, link);

    return true;
  }
  async findAll(): Promise<People[]> {
    const res = await this.peopleRepository.find({
      where: { deletedAt: IsNull() },
    });

    return res.map((data) => {
      if (!data.photoProfile.match('https')) {
        data.photoProfile = process.env.HOST_ADMIN + data.photoProfile;
      } else {
        data.photoProfile = data.photoProfile;
      }
      return data;
    });
  }

  async findAllFilter(
    filterData: PeopleInputFilter,
  ): Promise<PeopleDataFilterOutput> {
    // console.log(filterData);
    let whereClause = {};
    if (filterData?.search && filterData?.search !== '') {
      whereClause = {
        where: [
          { name: ILike(`%${filterData?.search}%`), deletedAt: IsNull() },
          { lastName: ILike(`%${filterData?.search}%`), deletedAt: IsNull() },
          { email: ILike(`%${filterData?.search}%`), deletedAt: IsNull() },
        ],
      };
    }

    try {
      const res = await this.peopleRepository.find({
        ...whereClause,
        skip: (filterData.page - 1) * filterData.quantityItemsPage,
        take: filterData.quantityItemsPage,
        order: {
          name: filterData.order,
        },
      });

      const peoples = res.map((data) => {
        if (!data.photoProfile.match('https')) {
          data.photoProfile = process.env.HOST_ADMIN + data.photoProfile;
        } else {
          data.photoProfile = data.photoProfile;
        }
        return data;
      });

      const quantityItems = (
        await this.peopleRepository.find({
          where: { deletedAt: IsNull() },
        })
      ).length;

      const quantityItemsFilter = (
        await this.peopleRepository.find({
          ...whereClause,
        })
      ).length;

      return {
        peoples,
        infoTable: {
          quantityItems,
          quantityItemsFilter,
        },
      };
    } catch (e) {
      throw new Error(e);
    }
  }

  async suspendAccount(id: string): Promise<boolean> {
    return (
      (
        await this.peopleRepository.update(
          { id, deletedAt: IsNull() },
          { deletedAt: new Date() },
        )
      ).affected === 1
    );
  }
  async updateEmail(id: string, email: string): Promise<boolean> {
    const res = await this.peopleRepository.update(
      {
        id,
        deletedAt: IsNull(),
      },
      {
        email,
        codeVerification: this.generateCode(),
      },
    );

    if (res.affected !== 1) throw new Error('Username does not exist');

    // const people = await this.peopleRepository.findOne({
    //   where: { id, deletedAt: IsNull() },
    // });

    const people = await this.getPersona_ById(id);

    this.mailService.sendUpdateAccountUser(people);
    return true;
  }

  async update(id: string, data: PeopleInput): Promise<boolean> {
    return (
      (
        await this.peopleRepository.update(
          { id, deletedAt: IsNull() },
          { ...data },
        )
      ).affected === 1
    );
  }

  async updateAdminPeople(id: string, data: PeopleInput): Promise<boolean> {
    const res = await this.adminRepository.findOne({
      where: {
        id,
        deletedAt: IsNull(),
      },
    });

    return (
      (
        await this.peopleRepository.update(
          { id: res.peopleId, deletedAt: IsNull() },
          { ...data },
        )
      ).affected === 1
    );
  }

  async verifyTokenRecovery(token: string): Promise<People> {
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });
      if (!payload) throw new UnauthorizedException();

      // let user = await this.peopleRepository.findOne({
      //   where: {
      //     id: payload?.id,
      //     deletedAt: IsNull(),
      //   },
      // });

      let user = await this.getPersona_ById(payload?.id);

      if (!user) throw new UnauthorizedException();

      return user;
    } catch {
      throw new UnauthorizedException();
    }
  }

  async verifySubscription(
    simulatorId: string,
    peopleId?: string,
  ): Promise<boolean> {
    console.log('id 3', simulatorId);

    const resPublic = await this.peopleRepository.query(`
      select * from simulator 
      where simulator.id="${simulatorId}" 
      and simulator.isPublic = true
    `);
    if (resPublic.length > 0) return true;
    if(peopleId){
      const res = await this.peopleRepository.query(`
      select * from people as p 
      inner join shopping as sh on p.id = sh.peopleId
      inner join products as pr on pr.shoppingId = sh.id
      inner join version as ve on pr.versionId = ve.id
      inner join book as b on b.id = ve.bookId
      inner join chapter as ch on ch.versionId = ve.id
      inner join simulator as si on si.chapterId = ch.id
      where 
            p.id = ? 
            AND si.id = ? 
            AND sh.paymentMade = true
            AND sh.paymentStatus= ?
            AND pr.dateStart <= NOW() 
            AND pr.dateEnd >= NOW()
      `,[peopleId,simulatorId,EnumPaymentStatus.PAGO_CONFIRMADO]);
      if (res.length > 0) return true;
      else return false;
    } else {
      return false;
    }
    
    // console.log('resPublic', resPublic);
    // return res.length > 0 || resPublic.length > 0;
  }

  async proofOfPaymentPendingState(peopleId): Promise<Shopping[]> {
    const res = await this.shoppingRepository.find({
      where: { people: { id: peopleId }, proofOfpayment: Not(IsNull()) },
    });
    // console.log("res", res);
    return res.map((val) => {
      return {
        ...val,
        proofOfpayment: val.proofOfpayment
          ? process.env.HOST_ADMIN + '/' + val.proofOfpayment
          : '',
      } as Shopping;
    });
  }

  async getShoppingByPeopleId(peopleId: string): Promise<Shopping[]> {
    const res = await this.shoppingRepository.find({
      where:[ 
        { people: { id: peopleId  },paymentStatus:EnumPaymentStatus.PAGO_CONFIRMADO },
        { people: { id: peopleId  },paymentStatus:EnumPaymentStatus.COMPROBANTE_RECHAZADO },
        { people: { id: peopleId  },paymentStatus:EnumPaymentStatus.COMPROBANTE_EN_REVISION },
      ],
      order:{ createdAt: 'DESC'}
    });
    console.log(res);
    return res.map((val) => {
      return {
        ...val,
        proofOfpayment: val.proofOfpayment
          ? process.env.HOST_ADMIN + '/' + val.proofOfpayment
          : '',
      } as Shopping;
    });
  }

  async sendComment(data: CommentInput): Promise<Boolean> {
    await this.commentsService.create(data);
    await this.mailService.comment(data);
    await this.mailService.sendConfirmationOfReceiptOfContactForm(data);
    return true;
  }

  async getPersonWithVerifiedEmail_ByEmail(
    emailPeople: string,
  ): Promise<People> {
    const res = await this.peopleRepository.findOne({
      where: {
        email: emailPeople,
        emailVerified: true,
        deletedAt: IsNull(),
      },
    });
    return res;
  }

  async getPerson_byEmail(emailPeople: string): Promise<People> {
    const res = await this.peopleRepository.findOne({
      where: {
        email: emailPeople,
        deletedAt: IsNull(),
      },
    });
    return res;
  }

  async getSubcribersForBook(bookId: string): Promise<ISubcriberForBook[]> {
    const res = await this.peopleRepository
      .createQueryBuilder('people')
      .innerJoin('people.shopping', 'shopping')
      .innerJoin('shopping.product', 'product')
      .innerJoin('product.version', 'version')
      .innerJoin('version.book', 'book')
      .select('people.id', 'people_id')
      .addSelect('people.name', 'people_name')
      .addSelect('people.lastName', 'people_lastName')
      .addSelect('people.email', 'people_email')
      .addSelect('product.dateStart', 'product_dateStart')
      .addSelect('product.dateEnd', 'product_dateEnd')
      .addSelect('shopping.paymentStatus', 'shopping_paymentStatus')
      .addSelect('shopping.id', 'shopping_id')
      .addSelect('product.id', 'product_id')
      .addSelect('version.id', 'version_id')
      .addSelect('version.bookId', 'version_bookId')
      .addSelect('version.version', 'version_version')
      .addSelect('book.title', 'book_title')
      .where('version.bookId=:bookId', { bookId })
      .andWhere('product.dateStart<=:now', { now: new Date() })
      .andWhere('product.dateEnd>=:now', { now: new Date() })
      .andWhere('shopping.paymentStatus=:status', {
        status: EnumPaymentStatus.PAGO_CONFIRMADO,
      })
      .getRawMany();
    return res;
  }
}
