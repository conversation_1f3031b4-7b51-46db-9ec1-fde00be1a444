import { Module } from '@nestjs/common';
import { PeoplesService } from './peoples.service';
import { PeoplesResolver } from './peoples.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { People } from './entities/people.entity';
// import { CredentialsModule } from '../credentials/credentials.module';

import { JwtModule } from '@nestjs/jwt';
import { MailModule } from 'src/mail/mail.module';
import { Credentials } from '../credentials/entities/credentials.entity';
import { Admin } from '../admins/entities/admin.entity';
import { Shopping } from '../shopping/entities/shopping.entity';
import { Products } from '../products/entities/products.entity';
import { Version } from '../versions/entities/version.entity';
import { Book } from '../books/entities/book.entity';
import { Simulator } from '../simulators/entities/simulator.entity';
import { CommentsModule } from '../comments/comments.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      People,
      Credentials,
      Admin,
      Shopping,
      Products,
      Version,
      Book,
      Simulator,
    ]),
    MailModule,
    CommentsModule,
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET_KEY,
      signOptions: { expiresIn: '5m' },
    }),
  ],
  providers: [PeoplesResolver, PeoplesService],
  exports: [PeoplesService],
})
export class PeoplesModule {}
