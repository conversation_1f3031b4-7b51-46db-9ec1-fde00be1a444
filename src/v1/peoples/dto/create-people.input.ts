import { Field, Int, InputType } from '@nestjs/graphql';
import { IsString, IsDate, IsEmail, IsOptional } from 'class-validator';

@InputType()
export class PeopleInput {
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  name?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  lastName?: string;

  @IsOptional()
  @IsDate()
  @Field(() => Date, { nullable: true })
  birthDate?: Date;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  photoProfile?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  cellPhone?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  regionCode?: string;

  @IsOptional()
  @IsEmail({}, { message: 'Invalid email format' })
  @Field({ nullable: true })
  email?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  codeVerification?: string;

  @IsOptional()
  @Field(() => Boolean, { nullable: true })
  emailVerified?: boolean;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  academicLevel?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  country?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  city?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  companyInstitution?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  studentProfessional?: string;
}
