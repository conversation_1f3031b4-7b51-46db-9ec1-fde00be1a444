import { Field, InputType } from '@nestjs/graphql';
import { IsString, IsEmail, IsOptional } from 'class-validator';

@InputType()
export class CommentInput {
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  name?: string;

  @IsOptional()
  @IsEmail({}, { message: 'Invalid email format' })
  @Field({ nullable: true })
  mail?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  regionCode?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  cellphone?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  affair?: string;

  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  message?: string;
}
