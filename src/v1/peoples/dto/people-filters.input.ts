import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsString, IsIn, IsInt, Min } from 'class-validator';

@InputType()
export class PeopleInputFilter {
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  search?: string;

  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'], { message: 'Order must be "asc" or "desc"' })
  @Field({ nullable: true })
  order?: 'asc' | 'desc';

  @IsOptional()
  @IsInt()
  @Min(1, { message: 'Page must be at least 1' })
  @Field({ nullable: true })
  page?: number;

  @IsOptional()
  @IsInt()
  @Min(1, { message: 'QuantityItemsPage must be at least 1' })
  @Field({ nullable: true })
  quantityItemsPage?: number;
}
