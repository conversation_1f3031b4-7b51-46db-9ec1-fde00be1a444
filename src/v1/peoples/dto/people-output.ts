import { ObjectType, Field, Int } from '@nestjs/graphql';

import {
  IsUUID,
  IsString,
  Length,
  Min,
  Max,
  Matches,
  IsInt,
  IsDate,
  IsOptional,
  IsPositive,
} from 'class-validator';
import { People } from '../entities/people.entity';

@ObjectType()
export class PeopleOutput {
  @Field(() => Date, { nullable: true })
  updatedAt: Date;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Length(3, 10, {
    message:
      'Name length must be between $constraint1 and $constraint2 characters',
  })
  @Field({ nullable: true })
  name?: string;

  @IsString()
  @Length(3, 20, {
    message:
      'lastName length must be between $constraint1 and $constraint2 characters',
  })
  @Field({ nullable: true })
  lastName?: string;

  @IsDate()
  @Min(new Date().getFullYear() - 130, {
    message: 'Age must be at least 130 years',
  })
  @Max(new Date().getFullYear() - 10, {
    message: 'Age must be at most 10 years',
  })
  @Field({ nullable: true })
  birthDate?: Date | null;

  @Field({ nullable: true })
  photoProfile?: string;

  @IsString()
  @Field()
  email: string;

  @Field({ nullable: true })
  codeVerification?: string;

  @Field(() => Boolean)
  emailVerified?: boolean;

  @Field(() => Boolean)
  credentialExists?: boolean;

  @IsString()
  @Matches(/^\d{8,}$/, { message: 'Invalid cellphone format' })
  @Field({ nullable: true })
  cellPhone?: string;

  @IsInt()
  @Matches(/^\+\d{1,3}$/, { message: 'Invalid region code format' })
  @Field({ nullable: true })
  regionCode?: string;

  @Field({ nullable: true })
  academicLevel?: string;

  @Field({ nullable: true })
  country?: string;

  @Field({ nullable: true })
  city?: string;

  @Field({ nullable: true })
  companyInstitution?: string;

  @Field({ nullable: true })
  studentProfessional?: string;
}

@ObjectType()
export class InfoTableDataPeople {
  @IsOptional()
  @IsInt()
  @IsPositive()
  @Field(() => Int, { nullable: true })
  quantityItems: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  @Field(() => Int, { nullable: true })
  quantityItemsFilter: number;
}

@ObjectType()
export class PeopleDataFilterOutput {
  @IsOptional()
  @Field(() => [People], { nullable: true })
  peoples: People[];

  @IsOptional()
  @Field(() => InfoTableDataPeople, { nullable: true })
  infoTable: InfoTableDataPeople;
}
