import { ObjectType, Field } from '@nestjs/graphql';
import {
  IsUUID,
  IsString,
  Length,
  Matches,
  IsInt,
  Min,
  Max,
  <PERSON>Date,
  IsEmail,
} from 'class-validator';
import { AuditableEntity } from './../../../config/auditable-entity.config';
import {
  Column,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Admin } from './../../admins/entities/admin.entity';
import { Credentials } from './../../credentials/entities/credentials.entity';
import { Shopping } from 'src/v1/shopping/entities/shopping.entity';

@Entity()
@ObjectType()
export class People extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Length(3, 10, {
    message:
      'Name length must be between $constraint1 and $constraint2 characters',
  })
  @Column({ default:"", nullable: true })
  @Field({ nullable: true })
  name?: string;

  @IsString()
  @Length(3, 20, {
    message:
      'lastName length must be between $constraint1 and $constraint2 characters',
  })
  @Column({ default: "", nullable: true })
  @Field({ nullable: true })
  lastName?: string;

  @Column({ type: 'timestamp', default: null })
  @IsDate()
  @Min(new Date().getFullYear() - 130, {
    message: 'Age must be at least 130 years',
  })
  @Max(new Date().getFullYear() - 10, {
    message: 'Age must be at most 10 years',
  })
  @Field({ nullable: true })
  birthDate?: Date;

  @Column({
    default: '/images/defaultUser.png',
  })
  @Field({ nullable: true })
  photoProfile?: string;

  @Column({ unique: true })
  @IsString()
  @IsEmail({}, { message: 'Invalid email format' })
  @Field()
  email: string;

  @Column({ default: null })
  @Field({ nullable: true })
  codeVerification?: string;

  @Column({ type: 'boolean', default: false })
  @Field(() => Boolean)
  emailVerified?: boolean;

  @IsString()
  @Matches(/^\d{8,}$/, { message: 'Invalid cell phone number' })
  @Column({ type: 'varchar', length: 20, default: null })
  @Field({ nullable: true })
  cellPhone?: string;

  @IsInt()
  @Matches(/^\+\d{1,3}$/, { message: 'Invalid region code' })
  @Column({ type: 'varchar', length: 10, default: null })
  @Field({ nullable: true })
  regionCode?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  academicLevel?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  country?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  city?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  companyInstitution?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  studentProfessional?: string;

  @OneToOne(() => Admin, (admin) => admin.people)
  admin: Admin;

  @OneToMany(() => Shopping, (shopping) => shopping.people)
  shopping: Shopping;

  @OneToOne(() => Credentials, (credential) => credential.people)
  credential: Credentials;
}
