import { Injectable } from '@nestjs/common';

import { InjectConnection, InjectRepository } from '@nestjs/typeorm';
import { Connection, IsNull, Repository } from 'typeorm';
import { Admin } from './entities/admin.entity';
import { v4 as uuidv4 } from 'uuid';

import { CreateAdminInput } from './dto/create-admin.input';
import { MailService } from 'src/mail/mail.service';
import { People } from '../peoples/entities/people.entity';
import { UpdateAdminInput } from './dto/update-admin.input';

import { PeoplesService } from './../peoples/peoples.service';
import Rol from '../roles/entities/rol.entity';
import { RolesService } from '../roles/roles.service';

@Injectable()
export class AdminsService {
  constructor(
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    private readonly mailService: MailService,
    private readonly peopleService: PeoplesService,
    @InjectConnection() private readonly connection: Connection,
    private readonly rolesService: RolesService,
  ) {}

  // async createAccountAdmin(dataAdmin: CreateAdminInput): Promise<boolean> {
  //   const queryRunner = this.connection.createQueryRunner();

  //   const peopleData = new People();
  //   const adminData = new Admin();

  //   const rolAdmin= await this.rolesService.getRolAdmin();

  //   peopleData.id = uuidv4();
  //   peopleData.name = dataAdmin.name;
  //   peopleData.lastName = dataAdmin.lastName;
  //   peopleData.email = dataAdmin.email;
  //   peopleData.emailVerified = false;

  //   adminData.id = uuidv4();
  //   adminData.people = peopleData;
  //   adminData.rolId = rolAdmin.id;

  //   console.log("ddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd");
  //   console.log(adminData);

  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();

  //   try {
  //     await queryRunner.manager.save(peopleData);
  //     await queryRunner.manager.save(adminData);
  //     await queryRunner.commitTransaction();

  //     this.mailService.sendUserConfirmationCreateAccountAdmin(peopleData);
  //     return true;
  //   } catch (e) {
  //     await queryRunner.rollbackTransaction();
  //     throw e;
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  async findOneId(id: string): Promise<Admin | null > {
    const admin= await this.adminRepository.findOne({
      where: {
        id,
        deletedAt: IsNull(),
      },
    });
    // if(!admin){
    //   throw new Error('El usuario Admin no existe');
    // }
    return admin;
  }

  async getRoles_ByEmail(email: string): Promise<Admin[]> {
    return await this.adminRepository.find({
      where: {
        people: {
          email,
          deletedAt: IsNull(),
        },
      },
    });
  }

  async getRoles_ByEmail2(email: string): Promise<Rol[]> {
    // const res= await this.adminRepository.createQueryBuilder
    //   ('admin')
    //   .innerJoin('admin.people', 'people')
    //   .innerJoin('admin.rol', 'rol')
    //   .where('people.email = :email', { email })
    //   .andWhere('people.deletedAt is null')
    //   .getMany();

    let res= await this.adminRepository.query(
      `SELECT R.*
       FROM admin A
       INNER JOIN people P ON A.peopleId = P.id
       INNER JOIN roles R ON A.rolId = R.id
       WHERE P.email = ? AND P.deletedAt IS NULL`, 
      [email]
    );
    console.log(res);
    return res;
  }

  //continuar desde aqui :V

  async upgradeUserToAdmin(peopleId: string): Promise<boolean> {
    const rolAdmin= await this.rolesService.getRolAdmin();
    try {
      const res = await this.adminRepository.save({
        id: uuidv4(),
        people: {
          id: peopleId,
        },
        rolId: rolAdmin.id,
      });

      const peopleData = await this.peopleRepository.findOne({
        where: { id: peopleId, deletedAt: IsNull() },
      });

      if (res)
        this.mailService.sendUserConfirmationCreateAccountAdmin(peopleData);

      return res ? true : false;
    } catch (e) {
      throw new Error('El usuario ya es administrador.');
    }
  }

  async findOne(email: string): Promise<Admin> {
    const res: People = await this.peopleService.getPersona_ByEmail(email);

    return await this.adminRepository.findOne({
      where: {
        people: {
          id: res.id,
          deletedAt: IsNull(),
        },
      },
    });
  }

  async findAll(): Promise<Admin[]> {
    return await this.adminRepository.find({ where: { deletedAt: IsNull() },order:{createdAt:"DESC"} });
  }

  async deleteAdmin(id: string): Promise<boolean> {
    const peopleData = await this.peopleRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });

    return (
      (await this.adminRepository.delete({ people: { id: peopleData.id } }))
        .affected === 1
    );
  }

  async update(data: UpdateAdminInput): Promise<boolean> {
    const res = await this.adminRepository.findOne({
      where: { id: data.id, deletedAt: IsNull() },
    });

    if (!res) throw new Error('This user does not exist.');
    return await this.peopleService.updateEmail(res.peopleId, data.email);
  }
}
