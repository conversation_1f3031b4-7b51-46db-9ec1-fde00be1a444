import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsEmail, Length, IsNotEmpty } from 'class-validator';

@InputType()
export class CreateAdminInput {
  @Field()
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  email: string;

  @Field()
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  @Length(1, 50, { message: 'Name must be between 1 and 50 characters' })
  name: string;

  @Field()
  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name cannot be empty' })
  @Length(1, 50, { message: 'Last name must be between 1 and 50 characters' })
  lastName: string;
}
