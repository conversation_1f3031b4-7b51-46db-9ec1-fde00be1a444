import { Is<PERSON><PERSON>, IsUUID } from 'class-validator';
import { CreateAdminInput } from './create-admin.input';
import { InputType, Field, Int, PartialType } from '@nestjs/graphql';

@InputType()
export class UpdateAdminInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => String)
  id: string;

  @IsEmail({}, { message: 'Email must be a valid email address' })
  @Field(() => String)
  email: string;
}
