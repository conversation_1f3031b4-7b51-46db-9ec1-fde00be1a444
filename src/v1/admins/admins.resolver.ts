import {
  Resolver,
  Query,
  Mutation,
  Args,
  Int,
  ResolveField,
  Parent,
  Context,
} from '@nestjs/graphql';
import { AdminsService } from './admins.service';
import { Admin } from './entities/admin.entity';
import { CreateAdminInput } from './dto/create-admin.input';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { UseGuards } from '@nestjs/common';
import { UpdateAdminInput } from './dto/update-admin.input';
import { People } from '../peoples/entities/people.entity';
import { PeoplesService } from '../peoples/peoples.service';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Payload } from '../credentials/entities/credentials.entity';

@Resolver(() => Admin)
export class AdminsResolver {
  constructor(
    private readonly adminsService: AdminsService,
    private peopleService: PeoplesService,
  ) {}

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  // @Mutation(() => Boolean, { name: 'createAdmin' })
  // async createAdmin(
  //   @Args('createAdminInput') createAdminInput: CreateAdminInput,
  // ): Promise<boolean> {
  //   return await this.adminsService.createAccountAdmin(createAdminInput);
  // }

  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'createAdmin' })
  async createAdmin(@Args('idPeople') idPeople: string): Promise<boolean> {
    return await this.adminsService.upgradeUserToAdmin(idPeople);
  }

  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Admin], { name: 'findAllAdmin' })
  async findAll(): Promise<Admin[]> {
    return this.adminsService.findAll();
  }

  //@UseGuards(JwtAuthAdminGuard)
// @UseGuards(RolesGuard)
// @Roles('admin')
//   @Query(() => Admin, { name: 'getProfileAdmin' })
//   async getProfile(@Context() context): Promise<Admin> {
//     const user:Payload= context.req.user;
//     console.log("getProfile")
//     console.log(user);

//     // return await this.adminsService.findOneId(context.req.user.sub);
//     return await this.adminsService.findOneId(user.sub);
//   }

  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Admin, { name: 'findOneAdmin' })
  async findOne(@Args('id') id: string) {
    return await this.adminsService.findOneId(id);
  }

  // -
  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'updateItemAdmin' })
  async updateAdmin(@Args('data') data: UpdateAdminInput): Promise<Boolean> {
    return await this.adminsService.update(data);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  // @Mutation(() => Boolean, { name: 'updateDataAdmin' })
  // async updateDataAdmin(@Args('data') data: UpdateAdminInput): Promise<Boolean> {
  //   return await this.adminsService.update(data);
  // }

  //@UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean, { name: 'deleteAdmin' })
  async deleteAdmin(@Args('id') id: string) {
    return await this.adminsService.deleteAdmin(id);
  }

  @ResolveField(() => People)
  async infoPeople(@Parent() admin: Admin): Promise<People> {
    return await this.peopleService.findOne(admin.peopleId);
  }
}
