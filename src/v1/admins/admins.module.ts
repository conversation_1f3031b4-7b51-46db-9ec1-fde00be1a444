import { Module } from '@nestjs/common';
import { AdminsService } from './admins.service';
import { AdminsResolver } from './admins.resolver';
import { Admin } from './entities/admin.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

import { PeoplesModule } from './../peoples/peoples.module';
import { MailModule } from './../../mail/mail.module';
import { AuthModule } from 'src/jwt/auth.module';
import { People } from '../peoples/entities/people.entity';
import Rol from '../roles/entities/rol.entity';
import { RolesModule } from '../roles/roles.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Admin, People, Rol]),
    PeoplesModule,
    MailModule,
    AuthModule,
    RolesModule,
  ],
  providers: [AdminsResolver, AdminsService],
  exports: [AdminsService],
})
export class AdminsModule {}
