import { ObjectType, Field, Int } from '@nestjs/graphql';
import { IsString, IsUUID, isString } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { People } from 'src/v1/peoples/entities/people.entity';
import Rol from 'src/v1/roles/entities/rol.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  Unique,
} from 'typeorm';

@Entity()
@Unique(['people'])
@ObjectType()
export class Admin extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @OneToOne(() => People, (people) => people.admin)
  @JoinColumn()
  people: People;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @RelationId((admin: Admin) => admin.people)
  peopleId: string;

  @Field( type => Int, { nullable: true })
  @Column({ name: "rolId" , nullable: true})
  rolId: number | null;
  @Field(type => Rol , { nullable: true })
  @ManyToOne(type => Rol, rol => rol.administradores)
  @JoinColumn({ name: "rolId" })
  rol: Promise<Rol|null>;
}
