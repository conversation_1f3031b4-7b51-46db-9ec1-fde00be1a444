import { Module } from '@nestjs/common';
import { SimulatorsService } from './simulators.service';
import { SimulatorsResolver } from './simulators.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Simulator } from './entities/simulator.entity';
import { SheetsModule } from '../sheets/sheets.module';
import { FormulasModule } from '../formulas/formulas.module';
import { AuthModule } from 'src/jwt/auth.module';
import { PeoplesModule } from '../peoples/peoples.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Simulator]),
    SheetsModule,
    FormulasModule,
    AuthModule,
    PeoplesModule,
  ],
  exports: [SimulatorsService],
  providers: [SimulatorsResolver, SimulatorsService],
})
export class SimulatorsModule {}
