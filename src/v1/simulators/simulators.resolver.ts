import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { SimulatorsService } from './simulators.service';
import { Simulator } from './entities/simulator.entity';
import { CreateAllsfsInput } from './dto/create-allsfs.simulator.input';
import { SheetsService } from '../sheets/sheets.service';
import { SheetOutput } from '../sheets/dto/sheet.output';
import { CalculateDynamicInput, CalculateInput } from './dto/calculate.input';
import { SimulatorOutput } from './dto/allsfs.output';
import { SimulatorUpdateInput } from './dto/update-sfs.input';
import { JwtAuthGuard } from '../../jwt/guards/auth.guard';
import { UseGuards } from '@nestjs/common';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { PeoplesService } from '../peoples/peoples.service';
import { query } from 'express';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { CurrentUser } from 'src/common/decorators/system_user.decorator';

@Resolver(() => Simulator)
export class SimulatorsResolver {
  constructor(
    private readonly simulatorsService: SimulatorsService,
    private sheetService: SheetsService,
    private peopleService: PeoplesService,
  ) {}

  // @UseGuards(JwtAuthGuard)
  @Public()
  @Mutation(() => SimulatorOutput, { name: 'simulatorResolve' })
  async calculateData(
    @Context() context,
    @Args('data') data: CalculateInput,
  ): Promise<SimulatorOutput> {
    const people_id =context.req.user? context.req.user.sub : null;
    let resValidRequest = await this.peopleService.verifySubscription(
      data.id,
      people_id,
    );

    // if (!resValidRequest)
    //   throw new Error('You do not have access to this area');

    return await this.simulatorsService.calculateResults(data);
  }

  // @UseGuards(JwtAuthGuard)
  @Public()
  @Mutation(() => SimulatorOutput, { name: 'calculateDynamicSimulatorData' })
  async calculateDynamicSimulatorData(
    @CurrentUser() user: any,
    @Context() context,
    @Args('data') data: CalculateDynamicInput,
  ): Promise<SimulatorOutput> {
    // console.log('data', data);
    console.log('currentUser', user);
    const people_id = context.req.user? context.req.user.sub : null;
    let resValidRequest = await this.peopleService.verifySubscription(
      data.id,
      people_id,
    );

    // if (!resValidRequest)
    //   throw new Error('You do not have access to this area');

    const res = await this.simulatorsService.calculateResultsDynamic(data);
    //  console.log('res');
    //  console.log(res);
    return res;
  }

  // @UseGuards(JwtAuthGuard)
  @Public()
  @Query(() => [Simulator], { name: 'simulators' })
  findAll(@Context() context): Promise<Simulator[]> {
    // return this.simulatorsService.findAllForSubscriptions(context.req.user.id);
    return this.simulatorsService.findAll();
  }

  // @UseGuards(JwtAuthGuard)
  @Public()
  @Query(() => [SheetOutput], { name: 'simulatorTables' })
  async tables(
    @Context() context,
    @CurrentUser() user: any,
    @Args('simulatorId') simulatorId: string,
  ): Promise<SheetOutput[]> {
    // console.log("sub");
    // console.log(context.req.user);
    console.log('currentUser', user);
    const people_id =context.req.user? context.req.user.sub : null;
    console.log('people_id', people_id);
    let res = await this.sheetService.getSheets_BySimulatorId(simulatorId);
    let resValidRequest = await this.peopleService.verifySubscription(
      simulatorId, 
      people_id, 
    );

    // if (!resValidRequest)
    //   throw new Error('You do not have access to this area');

    return res;
  }

  // ------------------------------------------------------------------------------------------

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Simulator], { name: 'simulatorAllAdmin' })
  findAllAdmin() {
    return this.simulatorsService.findAllAdmin();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => SimulatorOutput, { name: 'simulatorAdmin' })
  findServiceAdmin(
    @Args('simulatorId') simulatorId: string,
  ): Promise<SimulatorOutput> {
    return this.simulatorsService.simulatorAdmin(simulatorId);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Simulator)
  createAllsfs(
    @Args('createAllsfsInput') createAllsfsInput: CreateAllsfsInput,
  ): Promise<Simulator> {
    return this.simulatorsService.saveAllsfs(createAllsfsInput);
  }

  @Mutation(() => SimulatorOutput)
  updateAllsfs(
    @Args('updateAllsfsInput') updateAllsfsInput: SimulatorUpdateInput,
  ): Promise<SimulatorOutput> {
    return this.simulatorsService.updateSimulator(updateAllsfsInput);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => [Simulator])
  async deleteSimulator(
    @Args('simulatorId') simulatorId: string,
  ): Promise<Simulator[]> {
    return await this.simulatorsService.deleteSimulator(simulatorId);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => SimulatorOutput, { name: 'simulatorResolveAdmin' })
  async calculateDataAdmin(
    @Args('data') data: CalculateInput,
  ): Promise<SimulatorOutput> {
    return await this.simulatorsService.calculateResults(data);
  }

  // @ResolveField(() => [SheetOutput])
  // async tables(@Parent() Simulator: Simulator): Promise<SheetOutput[]> {
  //   let res = await this.sheetService.findOneSheet(Simulator.id);
  //   return res;
  // }

  @Mutation(() => Boolean)
  reassignChapterToSimulator(
    @Args('id') id: string,
    @Args('chapterId') chapterId: string,
  ): Promise<boolean> {
    return this.simulatorsService.reassignChapterToSimulator(id, chapterId);
  }
  //un query que devuelva un simulador por su id
  @Query(() => Simulator)
  async getSimulatorById(@Args('id') id: string): Promise<Simulator> {
    return await this.simulatorsService.getSimulatorById(id);
  }
}
