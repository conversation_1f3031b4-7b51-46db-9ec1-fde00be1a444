import { Injectable } from '@nestjs/common';
import { InjectConnection, InjectRepository } from '@nestjs/typeorm';
import { Column, Connection, IsNull, Repository } from 'typeorm';
import { Simulator } from './entities/simulator.entity';

import { Upload } from 'graphql-upload/Upload.js';
import * as fs from 'fs';
import * as math from 'mathjs';
import { inv, create, all, ConfigOptions } from 'mathjs';

// import { CreateServiceInput } from './dto/create-service.input';
import { CreateAllsfsInput, Table } from './dto/create-allsfs.simulator.input';

import { v4 as uuidv4 } from 'uuid';

import { SheetsService } from '../sheets/sheets.service';
import { FormulasService } from '../formulas/formulas.service';

import { Formula } from '../formulas/entities/formula.entity';
import {
  CalculateDynamicInput,
  CalculateInput,
  TableCalculate,
} from './dto/calculate.input';

import { esES } from '../../config/hf-langEs.config';
import { optionsHf } from '../../config/hf.config';

import { HyperFormula, Sheet } from 'hyperformula';
import { SimulatorOutput, TableOutput } from './dto/allsfs.output';
import { SimulatorUpdateInput } from './dto/update-sfs.input';
import { EnumSheetType } from '../sheets/entities/sheet.entity';
import { SheetOutput } from '../sheets/dto/sheet.output';
import { SimulatorOrder } from '../chapters/dto/update-chapter.input';
import { EnumPaymentStatus } from '../shopping/entities/shopping.entity';
HyperFormula.registerLanguage('esES', esES);

@Injectable()
export class SimulatorsService {
  
  config: ConfigOptions = {
    number: 'BigNumber', // Elije 'BigNumber' como tipo de número
    precision: 50        // Define la cantidad de dígitos significativos para los cálculos
  };

  math1=math.create(all, this.config);
  constructor(
    @InjectRepository(Simulator)
    public simulatorRepository: Repository<Simulator>,
    @InjectConnection() private connection: Connection,
    private sheetService: SheetsService,
    private formulaService: FormulasService,
  ) {

     
  }

  async findAll(): Promise<Simulator[]> {
    return await this.simulatorRepository.find({
      where: {
        deletedAt: IsNull(),
      },
    });
  }

  async findAllForSubscriptions(peopleId: string): Promise<Simulator[]> {
    const simulators = await this.simulatorRepository.find({
      where: {
        deletedAt: IsNull(),
      },
      order: {
        createdAt: 'DESC',
      },
    });

    // console.log('id 4', peopleId);

    // const simulatorsSubscriptionData = await this.simulatorRepository.query(`
    //     select si.* from people as p
    //     inner join shopping as sh
    //     on p.id = sh.peopleId
    //     inner join products as pr
    //     on pr.shoppingId = sh.id
    //     inner join version as ve
    //     on pr.versionId = ve.id
    //     inner join book as b
    //     on b.id = ve.bookId
    //     inner join chapter as ch
    //     on ch.bookId = b.id
    //     inner join simulator as si
    //     on si.chapterId = ch.id
    //     where p.id = "${peopleId}"
    //     and sh.paymentMade = true
    //     and pr.dateStart<=now()
    //     and pr.dateEnd>=now();
    // `);

    const simulatorsSubscriptionData = await this.simulatorRepository.query(
      `
      SELECT si.*
      FROM people AS p
      INNER JOIN shopping AS sh ON p.id = sh.peopleId
      INNER JOIN products AS pr ON pr.shoppingId = sh.id
      INNER JOIN version AS ve ON pr.versionId = ve.id
      INNER JOIN book AS b ON b.id = ve.bookId
      INNER JOIN chapter AS ch ON ch.versionId = ve.id
      INNER JOIN simulator AS si ON si.chapterId = ch.id
      WHERE
          p.id = ? -- Parámetro seguro para el ID de la persona
          AND sh.paymentMade = true
          AND sh.paymentStatus = ?
          AND pr.dateStart <= NOW()
          AND pr.dateEnd >= NOW();
      `,
      [peopleId, EnumPaymentStatus.PAGO_CONFIRMADO],
    );

    return simulators.map((val) => {
      return {
        ...val,
        coverSimulatorPath: val.coverSimulatorPath
          ? process.env.HOST_ADMIN + '/' + val.coverSimulatorPath
          : null,
        isPublic: !val.isPublic
          ? simulatorsSubscriptionData.some((x) => x.id === val.id)
          : val.isPublic,
      } as Simulator;
    });
  }

  async findAllAdmin(): Promise<Simulator[]> {
    const res = await this.simulatorRepository.find({
      where: {
        deletedAt: IsNull(),
      },
      order: {
        createdAt: 'desc',
      },
    });

    return res.map((val) => {
      return {
        ...val,
        coverSimulatorPath: val?.coverSimulatorPath
          ? process.env.HOST_ADMIN + '/' + val.coverSimulatorPath
          : null,
      } as Simulator;
    });
  }

  extractFormula(data, name, simulatorId) {
    return data
      .map((row, i) =>
        row
          .map(({ value }, j) => {
            return value.charAt(0) === '='
              ? {
                  id: uuidv4(),
                  formula: value,
                  position: [i, j],
                  name,
                  simulator: simulatorId,
                }
              : null;
          })
          .filter((elemento) => elemento !== null),
      )
      .filter((elemento) => elemento !== null);
  }

  async SaveFile(file: Upload, path: string): Promise<string> {
    if (!file) throw new Error('no file provided.');
    console.log(file);

    const uniqueFileName = uuidv4();
    const fileExtension = file.filename.split('.').pop() || 'txt';
    const uploadPath = `${path}${uniqueFileName}.${fileExtension}`;

    return new Promise((resolve, reject) => {
      const writeStream = fs.createWriteStream(uploadPath);
      file
        .createReadStream()
        .pipe(writeStream)
        .on('finish', () => resolve(uploadPath))
        .on('error', reject);
    });
  }

  async saveAllsfs(createAllsfsInput: CreateAllsfsInput): Promise<Simulator> {
    let {
      name: nameSimulator,
      tables: dataSimulator,
      isPublic,
      chapterId,
      coverSimulator,
    } = createAllsfsInput;
    let simulatorId: string = uuidv4();
    let formulas: Formula[] = [];

    let tablesData: any[] = dataSimulator.map(
      ({ name, data, show, orderNumber, type }, index) => {
        formulas.push(...this.extractFormula(data, name, simulatorId));
        return {
          id: uuidv4() as string,
          sheetNumber: index + 1,
          name,
          data: data.map((row, i) =>
            row.map((column, j) =>
              column.value.charAt(0) === '='
                ? { ...column, value: '', position: [i, j] }
                : { ...column, position: [i, j] },
            ),
          ),
          show,
          orderNumber,
          type,
          simulator: simulatorId,
        };
      },
    );

    return new Promise((resolve, reject) => {
      this.connection.transaction(async (entityManager) => {
        const coverSimulatorFile = (await coverSimulator) || null;
        try {
          let res = await this.simulatorRepository.save({
            id: simulatorId,
            name: nameSimulator,
            isPublic,
            chapter: { id: chapterId },
            coverSimulatorPath: coverSimulator
              ? (
                  await this.SaveFile(
                    coverSimulatorFile,
                    'public/images/simulators/',
                  )
                ).replace('public/', '')
              : null,
          });

          await this.sheetService.saveSheets(tablesData);
          await this.formulaService.create([].concat.apply([], formulas));

          resolve(res);
        } catch (e) {
          reject(e);
        }
      });
    });
  }
  async deleteFile(filePath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.unlink(filePath, (error) => {
        if (error) {
          reject(new Error(`Error deleting file: ${error.message}`));
          return;
        }
        resolve(true);
      });
    });
  }

  async updateSimulator(
    updateSfs: SimulatorUpdateInput,
  ): Promise<SimulatorOutput> {
    let {
      id: simulatorId,
      name,
      tables: dataSimulator,
      isPublic,
      chapterId,
      coverSimulator,
    } = updateSfs;

    let formulas: Formula[] = [];
    let tablesData: any[] = dataSimulator.map(
      ({ name, data, show, orderNumber, type }, index) => {
        formulas.push(...this.extractFormula(data, name, simulatorId));
        return {
          id: uuidv4() as string,
          sheetNumber: index + 1,
          name,
          data: data.map((row, i) =>
            row.map((column, j) =>
              column.value.charAt(0) === '='
                ? { ...column, value: '', position: [i, j] }
                : { ...column, position: [i, j] },
            ),
          ),
          orderNumber,
          type,
          show,
          simulator: simulatorId,
        };
      },
    );

    return new Promise((resolve, reject) => {
      this.connection.transaction(async (entityManager) => {
        try {
          await this.sheetService.deleteAllSheet(simulatorId);
          await this.formulaService.deleteAllFormula(simulatorId);

          const simulatorDataOld = await this.simulatorRepository.findOne({
            where: { id: simulatorId },
          });

          let coverSimulatorPath;

          if (coverSimulator) {
            const coverSimulatorFile = await coverSimulator;
            coverSimulatorPath = (
              await this.SaveFile(
                coverSimulatorFile,
                'public/images/simulators/',
              )
            ).replace('public/', '');

            try {
              // Elimina la imagen antigua solo si se proporciona una nueva
              await this.deleteFile(
                'public/' + simulatorDataOld.coverSimulatorPath,
              );
            } catch (e) {
              console.log('Esta imagen no existe.');
            }
          } else {
            // Si no se proporciona una nueva imagen, conserva la ruta anterior
            coverSimulatorPath = simulatorDataOld.coverSimulatorPath;
          }

          await this.simulatorRepository.update(
            {
              id: simulatorId,
              deletedAt: IsNull(),
            },
            {
              name,
              isPublic,
              chapter: { id: chapterId },
              coverSimulatorPath,
            },
          );

          await this.sheetService.saveSheets(tablesData);
          await this.formulaService.create([].concat.apply([], formulas));

          let res = await this.simulatorAdmin(simulatorId);

          resolve(res);
        } catch (e) {
          reject(e);
        }
      });
    });
  }

  async deleteSimulator(simulatorId: string): Promise<Simulator[]> {
    let simulatorData = await this.simulatorRepository.findOne({
      where: {
        id: simulatorId,
        deletedAt: IsNull(),
      },
    });
    simulatorData.deletedAt = new Date();

    await this.simulatorRepository.save(simulatorData);

    return this.findAll();
  }

  async hfCalculateTables(sheets, tables): Promise<any> {
    // console.log('hfCalculateTables');
    // console.log('sheets');
    // console.log(sheets);
    // console.log('tables');
    // console.log(tables);

    const hfInstance = HyperFormula.buildFromSheets(sheets, {
      ...optionsHf,
      decimalSeparator: ',',
      functionArgSeparator: ';',
      localeLang: 'es-ES',
      dateFormats: ['DD/MM/YYYY', 'DD/MM/YY', 'DD-MM-YYYY', 'DD-MM-YY', 'DD.MM.YYYY', 'DD.MM.YY'],
    });

    tables.forEach(({ data }, index) => {
      // console.log('data');
      // console.log(data);
      data.forEach((row, rowIndex) => { 
        row.forEach((col, colIndex) => {
          // Verificar si el valor es una fecha y formatearlo adecuadamente
          const fechaRegex = /^(?:(?:31(\/|-|\.)(0?[13578]|1[02]))\1|(?:(?:29|30)(\/|-|\.)(0?[13-9]|1[0-2])\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)0?2\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)0?2\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:0?[1-9]|1[0-2])(\/|-|\.)((?:1[6-9]|[2-9]\d)?\d{2})$/;
        const isDate = fechaRegex.test(col);
        const formattedValue = isDate ? "'" + col : col;
          hfInstance.setCellContents(
            { col: colIndex, row: rowIndex, sheet: index },
            formattedValue,
          ); 
        });
      });
    });

    return hfInstance.getAllSheetsValues();
  }

  async calculateResults(
    calculateData: CalculateInput,
  ): Promise<SimulatorOutput> {
    let { id: simulatorId, tables } = calculateData;
    let formulas = await this.formulaService.findOne(simulatorId);

    let copyTable = [...tables];

    let sheets = tables.reduce((result, { name, data }) => {
      result[name] = data.map((row) => row.map((column) => ''));
      return result;
    }, {});
    formulas.forEach(({ formula, position, name }) => {
      let filterData = copyTable.filter(
        ({ name: nameTable }) => nameTable === name,
      )[0];
      filterData.data[position[0]][position[1]] = formula;
    });

    // console.log("====================================")
    //  console.log('INSERTAR_CONSTANTES');
    // console.log(
    //   copyTable.find(item => item.name==='INSERTAR_CONSTANTES')
    // )

    let result = await this.hfCalculateTables(sheets, copyTable);

    let simulatorData = await this.simulatorRepository.findOne({
      where: { id: simulatorId, deletedAt: IsNull() },
    });
    let templateTables = await this.sheetService.getSheets_BySimulatorId(
      simulatorId,
    );

    // console.log('id 5', simulatorId);
    // const bookId = await this.simulatorRepository.query(
    //   `
    //   select b.id from simulator as si
    //   inner join chapter as ch
    //   on ch.id = si.chapterId
    //   inner join book as b
    //   on b.id = ch.bookId
    //   where si.id = ?`,
    //   [simulatorId],
    // );
    const versionAndBookIds = await this.getBookId_and_VersionId_BySimulatorId(
      simulatorId,
    );
    return {
      ...simulatorData,
      bookId: versionAndBookIds[0]?.bookId,
      versionId: versionAndBookIds[0]?.versionId,
      tables: templateTables.map(
        ({
          id,
          createdAt,
          name,
          sheetNumber,
          show,
          type,
          simulatorId,
          data,
          orderNumber,
        }: any) => {
          return {
            id: id as string,
            createdAt,
            name: name as string,
            sheetNumber: sheetNumber as number,
            type: type,
            show: show as boolean,
            simulatorId: simulatorId as string,
            orderNumber,
            data: data.map((row, rowIndex) => {
              return row.map((column, columnIndex) => {
                return {
                  ...column,
                  value: result[name][rowIndex][columnIndex],
                };
              });
            }),
          };
        },
      ),
    };
  }

  getBookId_and_VersionId_BySimulatorId(
    simulatorId: string,
  ): Promise<[{ bookId: string; versionId: string }]> {
    return this.simulatorRepository.query(
      `
      SELECT DISTINCT b.id as "bookId",v.id as "versionId"
      FROM 
          simulator AS si
          INNER JOIN chapter AS ch ON ch.id = si.chapterId AND ch.deletedAt IS NULL
          INNER JOIN version AS v ON v.id = ch.versionId
          INNER JOIN book AS b ON b.id = v.bookId
      WHERE 
          si.id = ?`,
      [simulatorId],
    );
  }

  async calculateResultsDynamic(
    calculateData: CalculateDynamicInput,
  ): Promise<SimulatorOutput> {
    let { id: simulatorId, tables } = calculateData;
    let simulatorData = await this.simulatorRepository.findOne({
      where: { id: simulatorId, deletedAt: IsNull() },
    });

    // console.log('simulator id:' + simulatorData.name);

    let templateTables: SheetOutput[] =
      await this.sheetService.getSheets_BySimulatorId(simulatorId);

    let formulas = await this.formulaService.findOne(simulatorId);

    let copyTable = [...tables];

    let sheets = tables.reduce((result, { name, data }) => {
      result[name] = data.map((row) => row.map((column) => ''));
      return result;
    }, {});
    formulas.forEach(({ formula, position, name }) => {
      let filterData = copyTable.filter(
        ({ name: nameTable }) => nameTable === name,
      )[0];
      filterData.data[position[0]][position[1]] = formula;
    });
    // console.log('antes funcionLinealINversa');
    // console.log(
    //   copyTable.find(item => item.name==='FUNCIÓN_LINEAL_DE_OFERTA_INVERSA')
    // )

    const t_datos_entrada = copyTable.find(
      (item) => item.type === 'n_rows_input_data',
    );

    let tem_t_datos_entrada = templateTables.find(
      (item) => item.type === 'n_rows_input_data',
    );

    const t_datos_entrada_filtrado_solo_formulas_y_numeros =
      this.filtrarFilasConPrimerElementoStringYNumeroDeFila(
        t_datos_entrada.data,
      );
    // console.log('t_datos_entrada_filtrado_solo_formulas_y_numeros');
    // console.log(t_datos_entrada_filtrado_solo_formulas_y_numeros);
    const nro_filas_de_datos =
      t_datos_entrada_filtrado_solo_formulas_y_numeros.length;
    // console.log('nro_filas_de_datos');
    // console.log(nro_filas_de_datos);

    const primer_elemento: any =
      t_datos_entrada_filtrado_solo_formulas_y_numeros[0];
    // console.log('primer_elemento');
    // console.log(primer_elemento);

    const tem_primer_elemento: any =
      tem_t_datos_entrada.data[primer_elemento.fila - 1];
    // console.log('tem_primer_elemento');
    // console.log(tem_primer_elemento);

    const nro_fila_primer_elemento = primer_elemento.fila;
    // console.log('nro_fila_primer_elemento');
    // console.log(nro_fila_primer_elemento);

    let tem_nueva_tabla_datos_entrada = tem_t_datos_entrada.data.slice(
      0,
      nro_fila_primer_elemento,
    );
    for (let i = 1; i < nro_filas_de_datos; i++) {
      // const nueva_fila = this.incrementarFilaEnFormulas(tem_primer_elemento.datos, nro_fila_primer_elemento , i);
      tem_nueva_tabla_datos_entrada.push(tem_primer_elemento);
    }
    tem_t_datos_entrada.data = tem_nueva_tabla_datos_entrada;

    const tablas_cal_dep = copyTable.filter(
      (item) => item.type === 'n_rows_calculations',
    );
    // console.log('tablas_cal_dep');
    // console.log(tablas_cal_dep);
    const tem_tablas_cal_dep = templateTables.filter(
      (item) => item.type === 'n_rows_calculations',
    );

    const tablas_cal = JSON.parse(JSON.stringify(tablas_cal_dep));
    const tem_tablas_cal = JSON.parse(JSON.stringify(tem_tablas_cal_dep));

    // console.log('tablas_cal');
    // console.log(tablas_cal);

    const tablas_y_primeras_filas =
      this.obtenerNrosPrimerasFilasTablasDinamicas(tablas_cal);

    const tabla_datos = {
      nombre_tabla: t_datos_entrada.name,
      nro_fila_primer_elemento_calculos: primer_elemento.fila,
    };
    // console.log('tabla_datos');
    // console.log(tabla_datos);
    tablas_y_primeras_filas.push(tabla_datos);

    //generar filas de tabla de datos de entrada
    const nueva_tabla_entrada = this.generarFilasTablaDatosDeEntrada(
      t_datos_entrada_filtrado_solo_formulas_y_numeros,
      nro_filas_de_datos,
      primer_elemento,
      t_datos_entrada,
      tablas_y_primeras_filas,
    );
    // console.log('nueva_tabla_entrada');
    // console.log(nueva_tabla_entrada);

    t_datos_entrada.data = nueva_tabla_entrada;

    // console.log('tablas_y_primeras_filas');
    // console.log(tablas_y_primeras_filas);
    // console.log("tablas_cal")
    // console.log(tablas_cal);

    for (const [index, t] of tablas_cal.entries()) {
      const t_filtrado = this.filtrarFilasSoloConFormulasYNumeros(t.data);
      const primer_elemento_calculos: any = t_filtrado[0];
      // if(t.name==='CÁLCULOS_AUXILIARES'){
      //   console.log(primer_elemento_calculos);
      // }

      const nro_fila_primer_elemento_calculos = primer_elemento_calculos.fila;

      const tem_primer_elemento_calculos: any =
        tem_tablas_cal[index].data[nro_fila_primer_elemento_calculos - 1];
      // console.log('tem_primer_elemento_calculos '+ index );
      // console.log(tem_primer_elemento_calculos );

      let nueva_tabla_calculados = t.data.slice(
        0,
        nro_fila_primer_elemento_calculos,
      );
      let tem_nueva_tabla_calculados = tem_tablas_cal[index].data.slice(
        0,
        nro_fila_primer_elemento_calculos,
      );
      for (let i = 1; i < nro_filas_de_datos; i++) {
        // console.log('========================');
        // console.log("primer_elemento_calculos.datos");
        // console.log(primer_elemento_calculos.datos);
        // console.log("nro_fila_primer_elemento_calculos");
        // console.log(nro_fila_primer_elemento_calculos);
        // console.log("i");
        // console.log(i);
        const nueva_fila = this.incrementarFilaEnFormulas(
          primer_elemento_calculos.datos,
          nro_fila_primer_elemento_calculos,
          i,
          tablas_y_primeras_filas,
          t.name,
        );
        // if(t.name==='CÁLCULOS_AUXILIARES'){
        // console.log("nro_fila_primer_elemento_calculos");
        // console.log(nro_fila_primer_elemento_calculos);
        // console.log('nueva_fila '+ i );
        // console.log(nueva_fila );
        // }

        nueva_tabla_calculados.push(nueva_fila);
        const tem_nueva_fila = this.incrementRowAndPosition(
          tem_nueva_tabla_calculados[tem_nueva_tabla_calculados.length - 1],
        );

        tem_nueva_tabla_calculados.push(tem_nueva_fila);
      }
      t.data = nueva_tabla_calculados;
      tem_tablas_cal[index].data = tem_nueva_tabla_calculados;
    }

    for (const t of tem_tablas_cal) {
      const tabla_antigua = templateTables.find((item) => item.name === t.name);
      tabla_antigua.data = t.data;
      // Your code for tem_tablas_cal goes here
    }
    // console.log('tablas_cal');
    // console.log(tablas_cal);

    for (const new_t of tablas_cal) {
      const tabla_antigua = copyTable.find((item) => item.name === new_t.name);
      tabla_antigua.data = new_t.data;
    }

    // console.log('copyTable');
    // console.log(copyTable);
    // for (const objeto of copyTable) {
    //   console.log("ID:", objeto.id);
    //   console.log("Name:", objeto.name);
    //   console.log("Type:", objeto.type);
    //   console.log("Data:");

    //   for (let i = 0; i < objeto.data.length; i++) {
    //       console.log(objeto.data[i]);
    //   }
    // }

    // console.log('copyTable')
    // console.log(copyTable);

    // console.log('FUNCIÓN_LINEAL_DE_OFERTA_NORMAL');
    // console.log(
    //   copyTable.find(item => item.name==='FUNCIÓN_LINEAL_DE_OFERTA_NORMAL')
    // )

    // console.log('APOYO3')
    // console.log(
    //   copyTable.find(item => item.name==='APOYO3')
    // )

    // console.log('OBSERVACIONES');
    // console.log(
    //   copyTable.find(item => item.name==='OBSERVACIONES')
    // )

    // console.log("====================================")
    //  console.log('INSERTAR_CONSTANTES');
    // console.log(
    //   copyTable.find(item => item.name==='INSERTAR_CONSTANTES')
    // )

    // console.log('nombres de tablas')
    // copyTable.forEach((table) => {
    //   // console.log('table.name');
    //   console.log(table.name);
    // })

    if(simulatorData.name=='Producción y Costos'){
      let corte1_copyTable:any=null;
      let corte1_sheets:any=null;
      for (const [index,t] of copyTable.entries()) {
        if(t.name=='3er._GRADO_PRODUCCION'){
          corte1_copyTable= copyTable.slice(0,index+1);


          // Convirtiendo las propiedades del objeto en un array y cortando basado en el índice
          const propiedadesCortadas = Object.entries(sheets).slice(0, index + 1);

          // Reconstruyendo el objeto con las propiedades cortadas
          corte1_sheets = propiedadesCortadas.reduce((obj, [key, value]) => {
            obj[key] = value;
            return obj;
          }, {});

          let result_temporal = await this.hfCalculateTables(corte1_sheets, corte1_copyTable);
          // console.log('result_temporal');
          // console.log(result_temporal);

          const er_grado_produccion = result_temporal['3er._GRADO_PRODUCCION'];
          // console.log('er_grado_produccion');
          // console.log(er_grado_produccion);
          let matriz_base= [er_grado_produccion[1].slice(0,4) ,er_grado_produccion[2].slice(0,4),er_grado_produccion[3].slice(0,4),er_grado_produccion[4].slice(0,4)];
          // console.log('matriz_base');
          // console.log(matriz_base);

          const determinante_calculado= this.calcularDeterminante(matriz_base);
          console.log('determinante_calculado');
          console.log(determinante_calculado);

          const matriz_inversa=inv(matriz_base);
          console.log('matriz_inversa');
          console.log(matriz_inversa);

          const valor_det= er_grado_produccion[5][1];
          console.log('valor_det');
          console.log(valor_det);
          // console.log("tabla")
          // console.log(t)
          t.data[5][1]= determinante_calculado.toString().replace('.',',');

          console.log("nuevo dato");
          console.log(t.data[5][1]);

          //=============================================================

        }
        if(t.name=='3er._GRADO_COSTOS'){
          corte1_copyTable= copyTable.slice(0,index+1);


          // Convirtiendo las propiedades del objeto en un array y cortando basado en el índice
          const propiedadesCortadas = Object.entries(sheets).slice(0, index + 1);

          // Reconstruyendo el objeto con las propiedades cortadas
          corte1_sheets = propiedadesCortadas.reduce((obj, [key, value]) => {
            obj[key] = value;
            return obj;
          }, {});

          let result_temporal = await this.hfCalculateTables(corte1_sheets, corte1_copyTable);
          // console.log('result_temporal');
          // console.log(result_temporal);

          const er_grado_produccion = result_temporal['3er._GRADO_COSTOS'];
          console.log('er_grado_produccion');
          console.log(er_grado_produccion);
          let matriz_base= [er_grado_produccion[1].slice(0,4) ,er_grado_produccion[2].slice(0,4),er_grado_produccion[3].slice(0,4),er_grado_produccion[4].slice(0,4)];
          console.log('matriz_base');
          console.log(matriz_base);

          const determinante_calculado= this.calcularDeterminante(matriz_base);
          console.log('determinante_calculado');
          console.log(determinante_calculado);



          const valor_det= er_grado_produccion[5][1];
          console.log('valor_det');
          console.log(valor_det);
          // console.log("tabla")
          // console.log(t)
          t.data[5][1]= determinante_calculado.toString().replace('.',',');

          console.log("nuevo dato");
          console.log(t.data[5][1]);

        }
      } 

      // console.log(' corte1_copyTable');
      // console.log( corte1_copyTable);
      // console.log(' corte1_sheets');
      // console.log( corte1_sheets);
    }

   

    let result = await this.hfCalculateTables(sheets, copyTable);
    // console.log('result.OBSERVACIONES');
    // console.log(result.OBSERVACIONES);

    
    // let templateTables = await this.sheetService.getSheets_BySimulatorId(simulatorId);
    // console.log('id 6', simulatorId);
    // const bookId = await this.simulatorRepository.query(
    //   `
    //   select b.id from simulator as si
    //   inner join chapter as ch
    //   on ch.id = si.chapterId
    //   inner join book as b
    //   on b.id = ch.bookId
    //   where si.id = ?`,
    //   [simulatorId],
    // );
    const versionAndBookIds = await this.getBookId_and_VersionId_BySimulatorId(
      simulatorId,
    );

    return {
      ...simulatorData,
      bookId: versionAndBookIds[0]?.bookId,
      versionId: versionAndBookIds[0]?.versionId,
      tables: templateTables.map(
        ({
          id,
          createdAt,
          name,
          sheetNumber,
          show,
          type,
          simulatorId,
          data,
          orderNumber,
        }: any) => {
          return {
            id: id as string,
            createdAt,
            name: name as string,
            sheetNumber: sheetNumber as number,
            type: type,
            show: show as boolean,
            simulatorId: simulatorId as string,
            orderNumber,
            data: data.map((row, rowIndex) => {
              return row.map((column, columnIndex) => {
                return {
                  ...column,
                  value: result[name][rowIndex][columnIndex],
                };
              });
            }),
          };
        },
      ),
    };
  }

   calcularDeterminante(matriz: number[][]): number {
    // Verificar si la matriz es cuadrada
    if (!matriz.length || matriz.some(fila => fila.length !== matriz.length)) {
      throw new Error('La matriz debe ser cuadrada.');
    }
  
    return this.math1.det(matriz);
  }

  generarFilasTablaDatosDeEntrada(
    tabla_solo_formulas_y_numeros: any,
    nro_filas_de_datos: number,
    primer_elemento: any,
    tabla_original: any,
    tablas_y_primeras_filas: any[],
  ) {
    // console.log('tabla_solo_formulas_y_numeros');
    // console.log(tabla_solo_formulas_y_numeros);

    console.log('primer_elemento');
    console.log(primer_elemento);
    // let nueva_tabla_calculados= tabla_original.data.slice(0, nro_fila_primer_elemento_calculos);
    //generando filas de tabla de datos de entrada
    let nueva_tabla_datos_entrada = tabla_original.data.slice(
      0,
      primer_elemento.fila,
    );
    for (let i = 1; i < nro_filas_de_datos; i++) {
      // const nueva_fila = this.incrementarFilaEnFormulas2(tabla_solo_formulas_y_numeros[i].datos, tablas_y_primeras_filas[ i].fila , 1, tablas_y_primeras_filas, tabla_original.name);
      const nueva_fila = this.incrementarFilaEnFormulas2(
        primer_elemento.datos,
        primer_elemento.fila,
        i, 
        tablas_y_primeras_filas,
        tabla_solo_formulas_y_numeros[i],
        tabla_original.name,
      );

      nueva_tabla_datos_entrada.push(nueva_fila);
    }
    return nueva_tabla_datos_entrada;
  }
  esFormulaExcel(parametro: any): boolean {
    // Verificar si el parámetro es una cadena y comienza con '='
    if (typeof parametro === 'string' && parametro.startsWith('=')) {
      // Verificar si la longitud de la cadena es mayor que 1 para excluir solo '='
      return parametro.length > 1;
    }
    return false;
  }

  obtenerNrosPrimerasFilasTablasDinamicas(
    tablas_dinamicas: any,
  ): { nombre_tabla: string; nro_fila_primer_elemento_calculos: number }[] {
    let res = [];
    for (const [index, t] of tablas_dinamicas.entries()) {
      const t_filtrado = this.filtrarFilasSoloConFormulasYNumeros(t.data);
      const primer_elemento_calculos: any = t_filtrado[0];
      res.push({
        nombre_tabla: t.name,
        nro_fila_primer_elemento_calculos: primer_elemento_calculos.fila,
      });
    }
    return res;
  }

  incrementRowAndPosition(cells: any[]): any[] {
    return cells.map((cell) => ({
      ...cell,
      row: cell.row + 1,
      position: [cell.position[0] + 1, cell.position[1]],
    }));
  }

  isExcelFormulaOrNumber(element: any): boolean {
    if (typeof element === 'string') {
      // Reemplaza las comas por puntos para manejar decimales
      const numberCandidate = element.replace(',', '.');
      return !isNaN(Number(numberCandidate)) || element.trim().startsWith('=');
    }
    return typeof element === 'number';
  }

  filtrarFilasSoloConFormulasYNumeros(arrays: any[][]): any[][] {
    return arrays.reduce((acc, array, index) => {
      if (array.every((element) => this.isExcelFormulaOrNumber(element))) {
        acc.push({ fila: index + 1, datos: array });
      }
      return acc;
    }, []);
  }

  filtrarFilasConPrimerElementoStringYNumeroDeFila(arrays: any[][]): any[] {
    return arrays.reduce((acc, array, index) => {
      if (
        array.slice(1).every((element) => this.isExcelFormulaOrNumber(element))
      ) {
        acc.push({ fila: index + 1, datos: array });
      }
      return acc;
    }, []);
  }

  // incrementarFilaEnFormulas(formulas: string[], filaObjetivo: number, incremento:number ): string[] {
  //   const regex = /([A-Z]+)([0-9]+)/g;

  //   return formulas.map(formula => {
  //     console.log('formula');
  //     console.log(formula);
  //       return formula.replace(regex, (match, column, row) => {
  //         console.log('match');
  //         console.log(match);
  //           if (parseInt(row) === filaObjetivo) {
  //               return `${column}${parseInt(row) + incremento}`;
  //           }
  //           return match;
  //       });

  //   });
  // }

  // incrementarFilaEnFormulas(formulas, filaObjetivo, incremento) {

  //   // const regex = /('(\w+)')?!?(\[A-Z]+)(\[0-9]+)/g;
  //   const regex=/'?\w[\w\s\._]*'!\$?[A-Z]\d+|\$?[A-Z]\d+/g;

  //   return formulas.map(formula => {
  //     let res= this.extraerReferenciasExcel(formula);
  //     // console.log(formula);
  //     // console.log('res');
  //     // console.log(res);

  //     return formula.replace(regex, (match, hoja, columna, fila) => {
  //       console.log('match');
  //       console.log(match);
  //       if(parseInt(fila) === filaObjetivo) {

  //         if(hoja) {
  //           return `${hoja}'!${columna}${parseInt(fila) + incremento}`;
  //         } else {
  //           return `${columna}${parseInt(fila) + incremento}`;
  //         }
  //       }
  //       return match;
  //     });

  //   });

  // }
  incrementarFilaEnFormulas(
    formulas: string[],
    filaObjetivo: number,
    incremento: number,
    tablas_y_primeras_filas: any,
    nombre_hoja?: string,
  ): string[] {
    // let imprimir= false;
    // if(nombre_hoja=='OFERTA'){
    //   imprimir= true;
    // }
    // Función para extraer referencias a celdas no fijas y hojas
    const extraerReferenciasExcel = (formula: string): string[] => {
      // const patron = /'?\w[\w\s\._]*'!\$?[A-Z]\d+|\$?[A-Z]\d+/g;

      const patron = /(?:'?\w[\w\s\._]*'!\$?[A-Z]\d+|\$?[A-Z]\d+)(?!')/g;
      const referencias = [];
      let match;
      while ((match = patron.exec(formula)) !== null) {
        referencias.push(match[0]);
      }
      return referencias;
    };

    // if(imprimir){
    //   console.log('OFERTA formulas')
    //   console.log('formulas');
    //   console.log(formulas);
    // }

    return formulas.map((formula) => {
      // if(imprimir){
      //   console.log('FORMULA');

      //   console.log(formula);
      // }
      // Extraer referencias de celdas no fijas
      const referencias = extraerReferenciasExcel(formula);
      // if(imprimir){
      //   console.log('OFERTA')
      //   console.log('referencias');
      //   console.log(referencias);
      // }

      // Procesar cada fórmula
      referencias.forEach((ref) => {
        // console.log('ref');
        // console.log(ref);
        const [column, row] = ref.split(/(\d+)/).filter(Boolean);
        let nombre_hoja = this.extraerNombreHoja(column);
        if (nombre_hoja) {
          // console.log('column');
          // console.log(column);
          // console.log(nombre_hoja);

          // console.log('tablas_y_primeras_filas');
          // console.log(tablas_y_primeras_filas);
          const tabla = tablas_y_primeras_filas.find(
            (item) => item.nombre_tabla === nombre_hoja,
          );
          // console.log('tabla');
          // console.log(tabla);
          const nro_fila_primer_elemento_calculos =
            tabla.nro_fila_primer_elemento_calculos;
          if (parseInt(row) === nro_fila_primer_elemento_calculos) {
            // console.log('column');
            // console.log(column);
            // console.log('row');
            // console.log(row);

            // Reemplazar la referencia en la fórmula
            formula = formula.replace(
              ref,
              `${column}${parseInt(row) + incremento}`,
            );
          }
        }
        // if(this.contieneReferenciaHoja(column)){
        //   const [hoja, columna] = column.split(/(\d+)/).filter(Boolean);
        //   console.log('hoja');
        //   console.log(hoja);
        //   console.log('columna');
        //   console.log(columna);
        //   // const tabla= tablas_y_primeras_filas.find(item => item.nombre_tabla===hoja);

        // }
        else {
          // if(imprimir){
          //   console.log('calculando OFERTA');
          //   console.log('column');
          //   console.log(column);
          //   console.log('row');
          //   console.log(row);
          //   console.log('filaObjetivo');
          //   console.log(filaObjetivo);
          //   console.log(parseInt(row) === filaObjetivo);
          // }
          // formula = formula.replace(ref, `${column}${parseInt(row) + incremento}`);
          if (parseInt(row) === filaObjetivo) {
            // Reemplazar la referencia en la fórmula
            formula = formula.replace(
              ref,
              `${column}${parseInt(row) + incremento}`,
            );
            if (nombre_hoja == 'OFERTA') {
              console.log('formula');
              console.log(formula);
            }
          }
        }
      });

      return formula;
    });
  }

  incrementarFilaEnFormulas2(
    formulas: string[],
    filaObjetivo: number,
    incremento: number,
    tablas_y_primeras_filas: any,
    fila_actual: any,
    nombre_hoja?: string,
  ): string[] {
    let imprimir = false;
    if (nombre_hoja == 'OBSERVACIONES') {
      imprimir = true;
      console.log('fila_actual');
      console.log(fila_actual);
    }
    // Función para extraer referencias a celdas no fijas y hojas
    const extraerReferenciasExcel = (formula: string): string[] => {
      // const patron = /'?\w[\w\s\._]*'!\$?[A-Z]\d+|\$?[A-Z]\d+/g;

      const patron = /(?:'?\w[\w\s\._]*'!\$?[A-Z]\d+|\$?[A-Z]\d+)(?!')/g;
      const referencias = [];
      let match;
      while ((match = patron.exec(formula)) !== null) {
        referencias.push(match[0]);
      }
      return referencias;
    };

    // if(imprimir){
    //   console.log('OBSERVACIONES formulas')
    //   console.log('formulas');
    //   console.log(formulas);
    // }

    return formulas.map((formula, index) => {
      // if(imprimir){
      //   console.log('FORMULA');
      //   console.log(formula);

      //   console.log('index');
      //   console.log(index);
      // }
      if (!this.esFormulaExcel(formula)) {
        // console.log('no es formula excel');
        // console.log(formula);
        // console.log(fila_actual);
        formula = fila_actual.datos[index];
        return formula;
      }
      // Extraer referencias de celdas no fijas
      const referencias = extraerReferenciasExcel(formula);
      // if(imprimir){
      //   console.log('OFERTA')
      //   console.log('referencias');
      //   console.log(referencias);
      // }

      // Procesar cada fórmula
      referencias.forEach((ref) => {
        // console.log('ref');
        // console.log(ref);
        const [column, row] = ref.split(/(\d+)/).filter(Boolean);
        let nombre_hoja = this.extraerNombreHoja(column);
        if (nombre_hoja) {
          // console.log('column');
          // console.log(column);
          // console.log(nombre_hoja);

          // console.log('tablas_y_primeras_filas');
          // console.log(tablas_y_primeras_filas);
          const tabla = tablas_y_primeras_filas.find(
            (item) => item.nombre_tabla === nombre_hoja,
          );
          // console.log('tabla');
          // console.log(tabla);
          const nro_fila_primer_elemento_calculos =
            tabla.nro_fila_primer_elemento_calculos;
          if (parseInt(row) === nro_fila_primer_elemento_calculos) {
            // console.log('column');
            // console.log(column);
            // console.log('row');
            // console.log(row);

            // Reemplazar la referencia en la fórmula
            formula = formula.replace(
              ref,
              `${column}${parseInt(row) + incremento}`,
            );
          }
        }
        // if(this.contieneReferenciaHoja(column)){
        //   const [hoja, columna] = column.split(/(\d+)/).filter(Boolean);
        //   console.log('hoja');
        //   console.log(hoja);
        //   console.log('columna');
        //   console.log(columna);
        //   // const tabla= tablas_y_primeras_filas.find(item => item.nombre_tabla===hoja);

        // }
        else {
          // if(imprimir){
          //   console.log('calculando OFERTA');
          //   console.log('column');
          //   console.log(column);
          //   console.log('row');
          //   console.log(row);
          //   console.log('filaObjetivo');
          //   console.log(filaObjetivo);
          //   console.log(parseInt(row) === filaObjetivo);
          // }
          // formula = formula.replace(ref, `${column}${parseInt(row) + incremento}`);
          if (parseInt(row) === filaObjetivo) {
            // Reemplazar la referencia en la fórmula
            formula = formula.replace(
              ref,
              `${column}${parseInt(row) + incremento}`,
            );
            if (nombre_hoja == 'OFERTA') {
              console.log('formula');
              console.log(formula);
            }
          }
        }
      });

      return formula;
    });
  }

  contieneReferenciaHoja(cadena: string): boolean {
    const patron = /'[^']+'![A-Z]+(\d+)?/;
    return patron.test(cadena);
  }

  extraerNombreHoja(cadena: string): string | null {
    // Expresión regular para extraer el nombre de la hoja
    const patron = /'([^']+)'\![A-Z]+(\d+)?/;
    const coincidencia = cadena.match(patron);

    if (coincidencia && coincidencia[1]) {
      // Devolver el nombre de la hoja
      return coincidencia[1];
    }
    return null;
  }

  extraerReferenciasExcel(formula: string): string[] {
    // Patrón para identificar referencias a celdas no fijas y hojas
    const patron = /'?\w[\w\s\._]*'!\$?[A-Z]\d+|\$?[A-Z]\d+/g;

    // Crear un arreglo para almacenar las referencias
    const referencias = [];

    // Buscar todas las coincidencias en la fórmula
    let match;
    while ((match = patron.exec(formula)) !== null) {
      // Agregar la referencia al arreglo
      referencias.push(match[0]);
    }

    // Retornar el arreglo de referencias
    return referencias;
  }

  //continuar desde aqui

  async simulatorAdmin(simulatorId: string): Promise<SimulatorOutput> {
    let formulas = await this.formulaService.findOne(simulatorId);

    let templateTables = await this.sheetService.getSheets_BySimulatorId(
      simulatorId,
    );

    let copyTable = [...templateTables];

    formulas.forEach(({ formula, position, name }) => {
      let filterData = copyTable.filter(
        ({ name: nameTable }) => nameTable === name,
      )[0];
      filterData.data[position[0]][position[1]].value = formula;
    });

    let simulatorData = await this.simulatorRepository.findOne({
      where: { id: simulatorId, deletedAt: IsNull() },
    });

    console.log('id 7', simulatorId);

    // const bookId = await this.simulatorRepository.query(
    //   `
    //   select b.id from simulator as si
    //   inner join chapter as ch
    //   on ch.id = si.chapterId
    //   inner join book as b
    //   on b.id = ch.bookId
    //   where si.id = ?`,
    //   [simulatorId],
    // );
    const versionAndBookIds = await this.getBookId_and_VersionId_BySimulatorId(
      simulatorId,
    );

    return {
      ...simulatorData,
      coverSimulatorPath: simulatorData?.coverSimulatorPath
        ? process.env.HOST_ADMIN + '/' + simulatorData.coverSimulatorPath
        : null,
      bookId: versionAndBookIds[0]?.bookId,
      versionId: versionAndBookIds[0]?.versionId,
      tables: copyTable
        .map(
          ({
            id,
            simulatorId,
            name,
            data,
            sheetNumber,
            type,
            show,
            orderNumber,
          }) => ({
            id,
            simulatorId,
            name,
            data,
            sheetNumber,
            type,
            orderNumber,
            show,
          }),
        )
        .sort((a, b) => a.sheetNumber - b.sheetNumber) as TableOutput[],
    };
  }

  async getSimulators_ByChapterId(chapterId: string): Promise<Simulator[]> {
    return await this.simulatorRepository.find({
      where: {
        chapter: { id: chapterId },
        // deletedAt: IsNull(),
      },
      order: { simulatorNumber: 'ASC' },
    });
  }
  async reassignChapterToSimulator(
    id: string,
    chapterId: string,
  ): Promise<boolean> {
    const result = await this.simulatorRepository.update(
      { id: id, deletedAt: IsNull() },
      { chapter: { id: chapterId } },
    );
    if (result.affected === 0)
      throw new Error('No se pudo reasignar el simulador al capítulo');
    return true;
  }
  async getSimulatorById(id: string): Promise<Simulator> {
    return await this.simulatorRepository.findOne({
      where: { id: id, deletedAt: IsNull() },
    });
  }

  async saveSimulators(simulatorsData: Simulator[]) {
    // const simulatorE= this.simulatorRepository.create(simulatorData);
    return this.simulatorRepository.save(simulatorsData);
  }
  async saveNumberSimulator(simulatorsData: SimulatorOrder[]) {
    return this.simulatorRepository.save(simulatorsData);
  }

  async getCantidadDeSimuladoresPorVersion_ByVersionId(
    versionId: string,
  ): Promise<number> {
    const query = `
      select count(*) as count from simulator as si
      inner join chapter as ch on ch.id = si.chapterId and ch.deletedAt is null
      inner join version as v on v.id = ch.versionId and v.deletedAt is null
      where v.id = ? and si.deletedAt is null
    `;
    const values = [versionId];
    const res= await this.simulatorRepository.query(query, values);
    return res[0].count;

  }
}
