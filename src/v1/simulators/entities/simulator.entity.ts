import { ObjectType, Field, Int } from '@nestjs/graphql';
import { IsBoolean, IsString, IsUUID, Length } from 'class-validator';
import {
  Before<PERSON><PERSON>rt,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

//entities
import { Sheet } from '../../sheets/entities/sheet.entity';
import { Formula } from '../../formulas/entities/formula.entity';
import { AuditableEntity } from 'src/config/auditable-entity.config';

import { Book } from './../../books/entities/book.entity';
import { Chapter } from 'src/v1/chapters/entities/chapter.entity';

@Entity()
@ObjectType()
export class Simulator extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @Length(1, 255, {
    message: 'Name length must be between 1 and 255 characters',
  })
  @Column({ unique: false })
  @Field()
  name: string;

  @IsBoolean({ message: 'isPublic must be a boolean value' })
  @Column({ type: 'boolean' })
  @Field(() => Boolean)
  isPublic: boolean;

  @IsString()
  @Column({ default: null })
  @Field({ nullable: true })
  coverSimulatorPath?: string;

  @Column({ default: null })
  @Field(() => Int, { nullable: true })
  simulatorNumber: number;

  @OneToMany(() => Sheet, (sheet) => sheet.simulator, { cascade: true })
  sheets: Sheet[];

  @OneToMany(() => Formula, (formula) => formula.simulator, { cascade: true })
  formulas: Formula[];

  @Field(() => Chapter)
  @ManyToOne(() => Chapter, (chapter) => chapter.simulators, { eager: true })
  chapter: Chapter;

  @Field(() => String)
  @RelationId((simulator: Simulator) => simulator.chapter)
  chapterId: string;
}
