import { ObjectType, InputType, Int, Field } from '@nestjs/graphql';
import { IsString, IsUUID, ArrayMinSize } from 'class-validator';
import { EnumSheetType } from 'src/v1/sheets/entities/sheet.entity';

@InputType()
export class TableCalculate {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  id: string;

  @IsString({ message: 'Name must be a string' })
  @Field()
  name: string;

  // @Field()
  // type: EnumSheetType;

  @ArrayMinSize(1, { message: 'Data must have at least one row' })
  @Field(() => [[String]])
  data: string[][];
}

@InputType()
export class TableDynamicCalculate {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  id: string;

  @IsString({ message: 'Name must be a string' })
  @Field()
  name: string;

  @Field()
  type: EnumSheetType;

  @ArrayMinSize(1, { message: 'Data must have at least one row' })
  @Field(() => [[String]])
  data: string[][];
}

@InputType()
export class CalculateInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  id: string;

  @ArrayMinSize(1, { message: 'Tables must have at least one entry' })
  @Field(() => [TableCalculate])
  tables: TableCalculate[];
}

@InputType()
export class CalculateDynamicInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  id: string;

  @ArrayMinSize(1, { message: 'Tables must have at least one entry' })
  @Field(() => [TableDynamicCalculate])
  tables: TableDynamicCalculate[];
}
