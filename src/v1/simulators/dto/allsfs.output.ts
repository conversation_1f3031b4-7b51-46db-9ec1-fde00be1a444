import { ObjectType, Field, Int } from '@nestjs/graphql';
import { IsBoolean, IsInt, IsString } from 'class-validator';
import { EnumSheetType } from 'src/v1/sheets/entities/sheet.entity';

@ObjectType()
export class ItemOutput {
  @IsString({ message: 'Value must be a string' })
  @Field()
  value: string;

  @IsBoolean({ message: 'Editable must be a boolean' })
  @Field()
  editable: boolean;

  @IsString({ message: 'ColumnName must be a string' })
  @Field()
  columnName: string;

  @IsInt({ message: 'Row must be an integer' })
  @Field(() => Int)
  row: number;

  @IsInt({ each: true, message: 'Position must be an array of integers' })
  @Field(() => [Int], { nullable: true })
  position: number[];

  @IsBoolean({ message: 'Bold must be a boolean' })
  @Field(() => Boolean, { nullable: true })
  bold?: boolean;

  @IsBoolean({ message: 'Show must be a boolean' })
  @Field()
  show: boolean;
}

@ObjectType()
export class TableOutput {
  @Field()
  id: string;

  @IsString({ message: 'Name must be a string' })
  @Field()
  name: string;

  @IsInt({ message: 'SheetNumber must be an integer' })
  @Field(() => Int)
  sheetNumber: number;

  @IsInt({ message: 'OrderNumber must be an integer' })
  @Field(() => Int, { nullable: true })
  orderNumber?: number;

  @Field()
  type: EnumSheetType;

  @Field(() => [[ItemOutput]])
  data: ItemOutput[][];

  @IsBoolean({ message: 'Show must be a boolean' })
  @Field()
  show: boolean;

  @IsString({ message: 'SimulatorId must be a string' })
  @Field()
  simulatorId: string;
}

@ObjectType()
export class SimulatorOutput {
  @IsString({ message: 'ID must be a string' })
  @Field(() => String, { nullable: true })
  id: string;

  @IsString({ message: 'Name must be a string' })
  @Field()
  name: string;

  @IsBoolean({ message: 'IsPublic must be a boolean' })
  @Field(() => Boolean)
  isPublic: boolean;

  @Field(() => Int)
  simulatorNumber: number;

  @Field(() => [TableOutput])
  tables: TableOutput[];

  @IsString()
  @Field({ nullable: true })
  coverSimulatorPath?: string;

  @IsString({ message: 'ChapterId must be a string' })
  @Field()
  chapterId: string;

  @IsString({ message: 'bookId must be a string' }) // esto es nuevo
  @Field()
  bookId: string;

  @IsString({ message: 'VersionId must be a string' })
  @Field()
  versionId: string;
}
