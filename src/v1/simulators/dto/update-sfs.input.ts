import { ObjectType, Field, Int, InputType } from '@nestjs/graphql';
import {
  IsString,
  IsBoolean,
  IsInt,
  IsUUID,
  ArrayMinSize,
} from 'class-validator';

import { Upload } from 'graphql-upload/Upload.js';
import * as GraphQLUpload from 'graphql-upload/GraphQLUpload.js';
import { EnumSheetType } from 'src/v1/sheets/entities/sheet.entity';

@InputType()
export class ItemUpdateInput {
  @IsString({ message: 'Value must be a string' })
  @Field()
  value: string;

  @IsBoolean({ message: 'Editable must be a boolean' })
  @Field()
  editable: boolean;

  @IsString({ message: 'ColumnName must be a string' })
  @Field()
  columnName: string;

  @IsInt({ message: 'Row must be an integer' })
  @Field(() => Int)
  row: number;

  @Field(() => [Int], { nullable: true })
  position: number[];

  @IsBoolean({ message: 'Show must be a boolean' })
  @Field()
  show: boolean;

  @Field(() => Boolean, { nullable: true })
  bold?: boolean;
}

@InputType()
export class TableUpdateInput {
  @IsString({ message: 'Name must be a string' })
  @Field()
  name: string;

  @ArrayMinSize(1, { message: 'Data must have at least one row' })
  @Field(() => [[ItemUpdateInput]])
  data: ItemUpdateInput[][];

  @IsBoolean({ message: 'Show must be a boolean' })
  @Field(() => Boolean)
  show: boolean;

  @Field()
  type: EnumSheetType;

  @IsInt({ message: 'OrderNumber must be an integer' })
  @Field(() => Int, { nullable: true })
  orderNumber?: number;
}

@InputType()
export class SimulatorUpdateInput {
  @IsUUID('4', { message: 'Invalid UUID format for id' })
  @Field(() => String, { nullable: true })
  id: string;

  @IsString({ message: 'Name must be a string' })
  @Field()
  name: string;

  @IsBoolean({ message: 'IsPublic must be a boolean' })
  @Field(() => Boolean)
  isPublic: boolean;

  @Field(() => GraphQLUpload, { nullable: true })
  coverSimulator?: Upload;

  @IsUUID('4', { message: 'Invalid UUID format for chapterId' })
  @Field(() => String, { nullable: true })
  chapterId?: string;

  @ArrayMinSize(1, { message: 'Tables must have at least one entry' })
  @Field(() => [TableUpdateInput])
  tables: TableUpdateInput[];
}
