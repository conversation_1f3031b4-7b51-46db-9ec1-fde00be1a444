import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { VersionSubscriptionPlan } from './entities/version-subscription-plan.entity';
import { IsNull, Repository } from 'typeorm';
import { InsertVersionSubscriptionPlanInput } from './dto/version-subscription-plan.input';

@Injectable()
export class VersionSubscriptionPlansService {
  constructor(
    @InjectRepository(VersionSubscriptionPlan)
    private readonly version_subscription_planRepository: Repository<VersionSubscriptionPlan>,
  ) {}

  async getVersionSubscriptionPlans_ByVersionId(
    versionId: string,
  ): Promise<VersionSubscriptionPlan[]> {
    return await this.version_subscription_planRepository.find({
      where: { versionId: versionId, deletedAt: IsNull() },
    });
  }

  async getVersionSubscriptionPlan_ById(
    id: string,
  ): Promise<VersionSubscriptionPlan> {
    const version_subscription_plan =
      await this.version_subscription_planRepository.findOne({
        where: { id, deletedAt: IsNull() },
      });
    if (!version_subscription_plan)
      throw new Error('Plan de subscripción no existe');
    return version_subscription_plan;
  }

  async insertVersionSubscriptionPlan(
    data: InsertVersionSubscriptionPlanInput,
  ): Promise<VersionSubscriptionPlan> {
    const version_subscription_plan =
      this.version_subscription_planRepository.create(data);
    if (version_subscription_plan.price < 0)
      throw new Error('El precio no puede ser menor a 0');
    return await this.version_subscription_planRepository.save(
      version_subscription_plan,
    );
  }

  async deleteVersionSubscriptionPlan_ById(id: string): Promise<boolean> {
    const version_subscription_plan =
      await this.getVersionSubscriptionPlan_ById(id);
    const delete_result = await this.version_subscription_planRepository.update(
      { id, deletedAt: IsNull() },
      { deletedAt: new Date() },
    );
    if (delete_result.affected === 0)
      throw new Error('No se pudo eliminar el plan de subscripción');
    return true;
  }
}
