import { Field, InputType } from '@nestjs/graphql';
import { IsUUID, IsNumber } from 'class-validator';

@InputType()
export class InsertVersionSubscriptionPlanInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  versionId: string;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  subscriptionPlanId: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Field()
  price: number;
}

@InputType()
export class UpdateVersionSubscriptionPlanInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  id: string;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  versionId: string;

  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  subscriptionPlanId: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Field()
  price: number;
}
