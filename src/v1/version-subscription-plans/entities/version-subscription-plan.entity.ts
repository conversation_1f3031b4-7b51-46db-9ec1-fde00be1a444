import { ObjectType, Field } from '@nestjs/graphql';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { IsUUID, IsNumber } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Version } from 'src/v1/versions/entities/version.entity';
import { SubscriptionPlan } from 'src/v1/subscription-plans/entities/subscription-plan.entity';
import { Products } from 'src/v1/products/entities/products.entity';

@Entity({ name: 'version_subscription_plans' })
@ObjectType()
export class VersionSubscriptionPlan extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @Field()
  price: number;

  @Field(() => String)
  @Column({ name: 'versionId' })
  versionId: string;

  @Field((type) => Version)
  @ManyToOne((type) => Version, (version) => version.versionSubscriptionPlans)
  @JoinColumn({ name: 'versionId' })
  version: Promise<Version>;

  @OneToMany(() => Products, (products) => products.versionSubscriptionsPlans)
  product: Products;

  @Field(() => String)
  @Column({ name: 'subscriptionPlanId' })
  subscriptionPlanId: string;

  @Field((type) => SubscriptionPlan)
  @ManyToOne(
    () => SubscriptionPlan,
    (subscriptionPlan) => subscriptionPlan.versionSubscriptionPlans,
  )
  @JoinColumn({ name: 'subscriptionPlanId' })
  subscriptionPlan: Promise<SubscriptionPlan>;
}
