import { Module } from '@nestjs/common';
import { VersionSubscriptionPlansResolver } from './version-subscription-plans.resolver';
import { VersionSubscriptionPlansService } from './version-subscription-plans.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VersionSubscriptionPlan } from './entities/version-subscription-plan.entity';

@Module({
  imports: [TypeOrmModule.forFeature([VersionSubscriptionPlan])],
  exports: [VersionSubscriptionPlansService],
  providers: [
    VersionSubscriptionPlansResolver,
    VersionSubscriptionPlansService,
  ],
})
export class VersionSubscriptionPlansModule {}
