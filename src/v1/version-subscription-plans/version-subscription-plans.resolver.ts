import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { VersionSubscriptionPlansService } from './version-subscription-plans.service';
import { VersionSubscriptionPlan } from './entities/version-subscription-plan.entity';
import {
  InsertVersionSubscriptionPlanInput,
  UpdateVersionSubscriptionPlanInput,
} from './dto/version-subscription-plan.input';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/jwt/guards/auth.guard';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';

@Resolver()
export class VersionSubscriptionPlansResolver {
  constructor(
    private readonly version_s_planService: VersionSubscriptionPlansService,
  ) {}

  // @UseGuards(JwtAuthGuard)
  @Query(() => [VersionSubscriptionPlan])
  public async getVersionSubscriptionPlans_ByVersionId(
    @Args('id', { type: () => String }) id: string,
  ): Promise<VersionSubscriptionPlan[]> {
    return await this.version_s_planService.getVersionSubscriptionPlans_ByVersionId(
      id,
    );
  }

  // @UseGuards(JwtAuthGuard)
  @Query((returns) => VersionSubscriptionPlan)
  async getVersionSubscriptionPlan_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<VersionSubscriptionPlan> {
    return await this.version_s_planService.getVersionSubscriptionPlan_ById(id);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation((returns) => VersionSubscriptionPlan)
  async insertVersionSubscriptionPlan(
    @Args('version_subscription_planData')
    version_subscription_planData: InsertVersionSubscriptionPlanInput,
  ): Promise<VersionSubscriptionPlan> {
    // console.log(version_subscription_planData);
    return await this.version_s_planService.insertVersionSubscriptionPlan(
      version_subscription_planData,
    );
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation((returns) => Boolean)
  async deleteVersionSubscriptionPlan_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<boolean> {
    return await this.version_s_planService.deleteVersionSubscriptionPlan_ById(
      id,
    );
  }
}
