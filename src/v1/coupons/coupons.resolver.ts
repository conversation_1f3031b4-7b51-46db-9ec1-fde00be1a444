import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { CouponsService } from './coupons.service';
import { Coupon, IsAllBooks } from './entities/coupon.entity';
import { CreateCouponInput } from './dto/create-coupon.input';
import { UpdateCouponInput } from './dto/update-coupon.input';

import { UseGuards } from '@nestjs/common';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';

@Resolver(() => Coupon)
export class CouponsResolver {
  constructor(private readonly couponsService: CouponsService) {}

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Coupon)
  createCoupon(
    @Args('createCouponInput') createCouponInput: CreateCouponInput,
  ) {
    return this.couponsService.create(createCouponInput);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Coupon, { name: 'coupon' })
  async findOne(@Args('id', { type: () => String }) id: string): Promise<Coupon> {
    const coupon = await this.couponsService.findOne(id);
    if (coupon.isAllBooks === IsAllBooks.NO) {
      const personalizedBooks = await this.couponsService.findPersonalizedBooks(id);
      const selectedBookIds = personalizedBooks.map(pb => pb.book.id);
      return { ...coupon, selectedBookIds } as Coupon;
    }
    return { ...coupon, selectedBookIds: [] } as Coupon;
  }
  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Coupon], { name: 'couponsList' })
  async findAll(): Promise<Coupon[]> {
    return await this.couponsService.findAll();
  }
  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean)
  async updateCoupon(
    @Args('id') id: string,
    @Args('updateCouponInput') updateCouponInput: UpdateCouponInput,
  ): Promise<boolean> {
    return await this.couponsService.update(id, updateCouponInput);
  }
  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation(() => Boolean)
  async removeCoupon(
    @Args('id', { type: () => String }) id: string,
  ): Promise<boolean> {
    return await this.couponsService.remove(id);
  }

  @Mutation(() => String || null)
  async verifyCoupon(
    @Args('nameCoupon', { type: () => String }) name: string,
    @Args('bookId', { type: () => String }) bookId: string,
  ): Promise<string | null> {
    return await this.couponsService.validateCoupon(name, bookId);
  }
}
