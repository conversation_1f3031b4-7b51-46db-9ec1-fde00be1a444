import { Injectable } from '@nestjs/common';
import { CreateCouponInput } from './dto/create-coupon.input';
import { UpdateCouponInput } from './dto/update-coupon.input';
import { Coupon, IsAllBooks } from './entities/coupon.entity';
import {
  <PERSON><PERSON>ull,
  LessThanOrEqual,
  MoreThan,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

import { v4 as uuidv4 } from 'uuid';
import { Book } from '../books/entities/book.entity';
import { CouponBookPersonalized } from '../coupon-book-personalizeds/entities/coupon-book-personalized.entity';

@Injectable()
export class CouponsService {
  constructor(
    @InjectRepository(Coupon) private couponRepository: Repository<Coupon>,
    @InjectRepository(CouponBookPersonalized)
    private coupoBookPersonalizedRepository: Repository<CouponBookPersonalized>,
    @InjectRepository(Book) private bookRepository: Repository<Book>,
  ) {}

  async create(createCouponInput: CreateCouponInput): Promise<Coupon> {
    const { selectedBookIds, isAllBooks, ...couponData } = createCouponInput;

    // Validación adicional
    if (
      isAllBooks === IsAllBooks.SI &&
      selectedBookIds &&
      selectedBookIds.length > 0
    ) {
      throw new Error(
        'It is not allowed to select specific books when isAllBooks is YES.',
      );
    }

    // Validación adicional para isAllBooks === NO y selectedBookIds vacío
    if (
      isAllBooks === IsAllBooks.NO &&
      (!selectedBookIds || selectedBookIds.length === 0)
    ) {
      throw new Error(
        'You must select at least one book when isAllBooks is NO.',
      );
    }

    const coupon = await this.couponRepository.save({
      id: uuidv4(),
      ...couponData,
      isAllBooks,
    });

    // Si isAllBooks es No, Guarda los libros personalizados
    if (isAllBooks === IsAllBooks.NO && selectedBookIds.length > 0) {
      const books = await this.bookRepository.findByIds(selectedBookIds);
      if (books.length !== selectedBookIds.length) {
        throw new Error('Some selected books do not exist.');
      }

      const couponBookPersonalizeds = selectedBookIds.map((bookId) => ({
        id: uuidv4(),
        book: { id: bookId } as Book,
        coupon: { id: coupon.id } as Coupon,
      }));

      console.log({ couponBookPersonalizeds });

      await this.coupoBookPersonalizedRepository.save(couponBookPersonalizeds);
    }
    return coupon;

    // return await this.couponRepository.save({
    //   id: uuidv4(),
    //   ...createCouponInput,
    // });
  }

  async findOne(id: string): Promise<Coupon> {
    return this.couponRepository.findOne({
      where: {
        id,
        deletedAt: IsNull(),
      },
    });
  }

  async findPersonalizedBooks(couponId: string): Promise<CouponBookPersonalized[]> {
    return this.coupoBookPersonalizedRepository.find({
      where: { coupon: { id: couponId } },
      relations: ['book'],
    });
  }

  async update(
    id: string,
    updateCouponInput: UpdateCouponInput,
  ): Promise<boolean> {
    const { selectedBookIds, isAllBooks, ...data } = updateCouponInput;

    // Verificar si el cupón existe
    const coupon = await this.couponRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });

    if (!coupon) {
      throw new Error('Coupon not found');
    }

    // Validación adicional
    if (
      isAllBooks === IsAllBooks.SI &&
      selectedBookIds &&
      selectedBookIds.length > 0
    ) {
      throw new Error(
        'It is not allowed to select specific books when isAllBooks is YES.',
      );
    }

    // Validación adicional para isAllBooks === NO y selectedBookIds vacío
    if (
      isAllBooks === IsAllBooks.NO &&
      (!selectedBookIds || selectedBookIds.length === 0)
    ) {
      throw new Error(
        'You must select at least one book when isAllBooks is NO.',
      );
    }

    // console.log(updateCouponInput);
    await this.couponRepository.update(
      { id, deletedAt: IsNull() },
      { ...data, isAllBooks },
    );

    // Si isAllBooks es NO, actualizar los libros personalizados
    if (isAllBooks === IsAllBooks.NO) {
      // Eliminar los libros personalizados existentes
      await this.coupoBookPersonalizedRepository.delete({ coupon: { id } });

      // Agregar los nuevos libros personalizados
      if (selectedBookIds.length > 0) {
        const books = await this.bookRepository.findByIds(selectedBookIds);
        if (books.length !== selectedBookIds.length) {
          throw new Error('Some selected books do not exist.');
        }

        const couponBookPersonalizeds = selectedBookIds.map((bookId) => ({
          id: uuidv4(),
          book: { id: bookId } as Book,
          coupon: { id } as Coupon,
        }));

        await this.coupoBookPersonalizedRepository.save(couponBookPersonalizeds);
      }
    }else {
      // Si isAllBooks es SI, eliminar los libros personalizados existentes
      await this.coupoBookPersonalizedRepository.delete({ coupon: { id } });
    }

    return true;
  }

  async findAll(): Promise<Coupon[]> {
    return await this.couponRepository.find({
      where: {
        deletedAt: IsNull(),
      },
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async remove(id: string): Promise<boolean> {
    const result = await this.couponRepository.update(
      { id, deletedAt: IsNull() },
      {
        deletedAt: new Date(),
      },
    );

    return result.affected === 1;
  }

  async validateCoupon(name: string, bookId: string): Promise<string | null> {
    const now = new Date();

    const coupon = await this.couponRepository.findOne({
      where: [
        {
          name,
          deletedAt: IsNull(),
          dateStart: LessThanOrEqual(now),
          dateEnd: MoreThanOrEqual(now),
          isEnable: true,
          unlimited: true,
          isAllBooks: IsAllBooks.SI,
        },
        {
          name,
          deletedAt: IsNull(),
          dateStart: LessThanOrEqual(now),
          quantity: MoreThan(0),
          dateEnd: MoreThanOrEqual(now),
          isEnable: true,
          isAllBooks: IsAllBooks.SI,
        },
      ],
    });

    if (coupon) {
      return coupon.id;
    }

    // Buscamos en la Tabla coupon_book_personalized
    const personalizedCoupon =
      await this.coupoBookPersonalizedRepository.findOne({
        where: {
          coupon: {
            name,
            deletedAt: IsNull(),
            dateStart: LessThanOrEqual(now),
            dateEnd: MoreThanOrEqual(now),
            isEnable: true,
            isAllBooks: IsAllBooks.NO,
          },
          book:{
            id: bookId,
          }
        },
        relations: ['coupon', 'book'],
      });

      if (personalizedCoupon && personalizedCoupon.coupon) {
        const { coupon } = personalizedCoupon;
        if (
          coupon.dateStart <= now &&
          coupon.dateEnd >= now &&
          coupon.isEnable &&
          (coupon.unlimited || coupon.quantity > 0)
        ) {
          return coupon.id;
        }
      }

    // Lanzar error si no se encuentra un cupón válido
    throw new Error('Coupon is not valid.');

    // if (coupon === null) throw new Error('Coupon is not valid.');

    // return coupon?.id ? coupon.id : null;
  }

  async validateCoupon2(coupon: Coupon): Promise<boolean> {
    if (!coupon) {
      throw new Error('El cupón no es válido.');
    }

    if (!coupon.isEnable) {
      throw new Error('El cupón no está habilitado.');
    }

    const now = new Date();
    if (coupon.dateStart > now || coupon.dateEnd < now) {
      throw new Error('El cupón no está en el período de validez.');
    }

    if (!coupon.unlimited && coupon.usedQuantity >= coupon.quantity) {
      throw new Error(
        'El cupón ya ha sido utilizado el máximo de veces permitidas.',
      );
    }
    return true;
  }

  async useCoupon(coupon: Coupon): Promise<boolean> {
    const couponValid = await this.validateCoupon2(coupon);
    if (couponValid) {
      await this.couponRepository.update(
        { id: coupon.id },
        {
          usedQuantity: coupon.usedQuantity + 1,
        },
      );
      return true;
    }
  }

  async cancelCouponUse(coupon: Coupon): Promise<void> {
    await this.couponRepository.update(
      { id: coupon.id },
      {
        usedQuantity: coupon.usedQuantity - 1,
      },
    );
  }

  async getCoupon_ById(id: string): Promise<Coupon | null> {
    const coupon = await this.couponRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });
    if (!coupon) {
      throw new Error('El cupón no es válido.');
    }
    return coupon;
  }
}
