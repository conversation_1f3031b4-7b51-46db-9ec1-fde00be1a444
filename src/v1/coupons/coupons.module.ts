import { Module } from '@nestjs/common';
import { CouponsService } from './coupons.service';
import { CouponsResolver } from './coupons.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Coupon } from './entities/coupon.entity';

import { AuthModule } from 'src/jwt/auth.module';
import { CouponBookPersonalized } from '../coupon-book-personalizeds/entities/coupon-book-personalized.entity';
import { Book } from '../books/entities/book.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Coupon, CouponBookPersonalized, Book],), AuthModule],
  providers: [CouponsResolver, CouponsService],
  exports: [CouponsService],
})
export class CouponsModule {}
