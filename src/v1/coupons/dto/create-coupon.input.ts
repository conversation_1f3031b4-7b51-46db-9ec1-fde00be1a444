import { InputType, Int, Field, Float } from '@nestjs/graphql';
import { IsNotEmpty, IsDate, IsNumber, Min, IsBoolean, IsEnum, IsArray, IsUUID } from 'class-validator';
import { IsAllBooks } from '../entities/coupon.entity';

@InputType()
export class CreateCouponInput {
  @Field()
  @IsNotEmpty({ message: 'Name cannot be empty' })
  name: string;

  @Field(() => Date)
  @IsDate({ message: 'DateStart must be a valid date' })
  dateStart: Date;

  @Field(() => Date)
  @IsDate({ message: 'DateEnd must be a valid date' })
  dateEnd: Date;

  @Field(() => Int, { nullable: true })
  @IsNumber({}, { message: 'Quantity must be a number' })
  @Min(0, { message: 'Quantity must be at least 0' })
  quantity?: number;

  @Field(() => Float)
  @IsNumber({}, { message: 'DiscountPercentage must be a number' })
  @Min(0, { message: 'DiscountPercentage must be at least 0' })
  discountPercentage: number;

  @Field(() => Boolean)
  @IsBoolean({ message: 'Unlimited must be a boolean value' })
  unlimited: boolean;

  @Field(() => Boolean)
  @IsBoolean({ message: 'IsEnable must be a boolean value' })
  isEnable: boolean;
  
  @Field(() => IsAllBooks)
  @IsEnum(IsAllBooks, {message: ' isAllBooks must be either Si or No'})
  isAllBooks: IsAllBooks

  @Field(() => [String])
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each selectedBookId must be a valid UUID' })
  selectedBookIds: string[];
}
