import { ObjectType, Field, Int, Float, registerEnumType } from '@nestjs/graphql';
import {
  IsUUID,
  IsNotEmpty,
  IsBoolean,
  IsNumber,
  Min,
  IsDate,
} from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Book } from 'src/v1/books/entities/book.entity';
import { CouponBookPersonalized } from 'src/v1/coupon-book-personalizeds/entities/coupon-book-personalized.entity';
import { Shopping } from 'src/v1/shopping/entities/shopping.entity';
import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
  Timestamp,
} from 'typeorm';

export enum IsAllBooks {
  SI = 'SI',
  NO = 'NO'
}

registerEnumType(IsAllBooks, {
  name: 'IsAllBooks', 
});

@Entity()
@ObjectType()
export class Coupon extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @Column()
  @Field()
  @IsNotEmpty({ message: 'Name cannot be empty' })
  name: string;

  @Column({ type: 'timestamp', default: null })
  @Field(() => Date)
  @IsDate({ message: 'DateStart must be a valid date' })
  dateStart: Date;

  @Column({ type: 'timestamp', default: null })
  @Field(() => Date)
  @IsDate({ message: 'DateEnd must be a valid date' })
  dateEnd: Date;

  @Column({ type: 'integer', default: 0 })
  @Field(() => Int)
  @IsNumber({}, { message: 'Quantity must be a number' })
  @Min(0, { message: 'Quantity must be at least 0' })
  quantity: number;

  @Column({ type: 'integer', default: 0 })
  @Field(() => Int)
  usedQuantity: number;

  @Column({ type: 'boolean', default: false })
  @Field(() => Boolean)
  @IsBoolean({ message: 'Unlimited must be a boolean value' })
  unlimited: boolean;

  @Column({ type: 'double' })
  @Field(() => Float)
  @IsNumber({}, { message: 'DiscountPercentage must be a number' })
  @Min(0, { message: 'DiscountPercentage must be at least 0' })
  discountPercentage: number;

  @Column({ type: 'bool' })
  @Field(() => Boolean)
  @IsBoolean({ message: 'IsEnable must be a boolean value' })
  isEnable: boolean;

  // @ManyToOne(() => Book, (book) => book.coupon)
  // book: Book;
  @Field({nullable: true})
  @Column({
    type: 'enum',
    enum: IsAllBooks,
    default: IsAllBooks.NO,
  })
  isAllBooks: IsAllBooks

  @OneToMany(() => Shopping, (shopping) => shopping.coupon)
  shopping: Shopping[];

  @OneToMany(() => CouponBookPersonalized, couponBookPersonalized => couponBookPersonalized.coupon)
couponBookPersonalizeds: CouponBookPersonalized[];

@Field(() => [String], { nullable: true })
  selectedBookIds?: string[];

  // @Field(() => String)
  // @RelationId((coupon: Coupon) => coupon.book)
  // bookId: string;
}
