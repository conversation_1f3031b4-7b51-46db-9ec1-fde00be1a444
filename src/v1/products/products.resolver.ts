import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { ProductsService } from './products.service';
import { Products } from './entities/products.entity';
import { CreateProductsInput } from './dto/create-products.input';
import { UpdateProductsInput } from './dto/update-products.input';

@Resolver(() => Products)
export class ProductResolver {
  constructor(private readonly productsService: ProductsService) {}

  // @Mutation(() => Item)
  // createItem(@Args('createItemInput') createItemInput: CreateItemInput) {
  //   return this.itemService.create(createItemInput);
  // }

  // @Query(() => [Item], { name: 'item' })
  // findAll() {
  //   return this.itemService.findAll();
  // }

  // @Query(() => Item, { name: 'item' })
  // findOne(@Args('id', { type: () => Int }) id: number) {
  //   return this.itemService.findOne(id);
  // }

  // @Mutation(() => Item)
  // updateItem(@Args('updateItemInput') updateItemInput: UpdateItemInput) {
  //   return this.itemService.update(updateItemInput.id, updateItemInput);
  // }

  // @Mutation(() => Item)
  // removeItem(@Args('id', { type: () => Int }) id: number) {
  //   return this.itemService.remove(id);
  // }
}
