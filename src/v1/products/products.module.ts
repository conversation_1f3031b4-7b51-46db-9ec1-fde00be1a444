import { Module } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductResolver } from './products.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Products } from './entities/products.entity';
import { PeoplesModule } from '../peoples/peoples.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Products]),
  ],
  providers: [ProductResolver, ProductsService],
  exports: [ProductsService],
})
export class ProductModule {}
