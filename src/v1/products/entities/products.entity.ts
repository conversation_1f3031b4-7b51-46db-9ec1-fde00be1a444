import { ObjectType, Field, Int, Float } from '@nestjs/graphql';
import { IsUUID, IsOptional, IsNumber, IsDate } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Shopping } from 'src/v1/shopping/entities/shopping.entity';
import { VersionSubscriptionPlan } from 'src/v1/version-subscription-plans/entities/version-subscription-plan.entity';
import { Version } from 'src/v1/versions/entities/version.entity';
import {
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

@Entity()
@ObjectType()
export class Products extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsOptional()
  @IsNumber({}, { message: 'Price must be a number' })
  @Column({type:'decimal', precision:19, scale:2, default: null })
  @Field( { nullable: true })
  price?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Price with discount must be a number' })
  @Column({ type:'decimal', precision:19, scale:2, default: null })
  @Field( { defaultValue: null, nullable: true })
  priceWithDiscount?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Amount must be a number' })
  @Column({ type: 'float', default: null })
  @Field(() => Int, { defaultValue: null, nullable: true })
  amount?: number;

  @IsDate({ message: 'Date start must be a valid date' })
  @Column({ type: 'timestamp', default: null })
  @Field(() => Date)
  dateStart: Date;

  @IsDate({ message: 'Date end must be a valid date' })
  @Column({ type: 'timestamp', default: null })
  @Field(() => Date)
  dateEnd: Date;

  @ManyToOne(() => Shopping, (shopping) => shopping.product)
  shopping: Shopping;

  @Field(() => Version, { nullable: true })
  @ManyToOne(() => Version, (version) => version.product, { eager: true })
  version: Version;

  @Field(() => VersionSubscriptionPlan, { nullable: true })
  @ManyToOne(
    () => VersionSubscriptionPlan,
    (versionSubscriptionPlan) => versionSubscriptionPlan.product,
    { eager: true },
  )
  versionSubscriptionsPlans: VersionSubscriptionPlan;

  @RelationId((products: Products) => products.version)
  versionId: string;

  @RelationId((products: Products) => products.versionSubscriptionsPlans)
  versionSubscriptionsPlansId: string;

  @RelationId((products: Products) => products.shopping)
  shoppingId: string;
}
