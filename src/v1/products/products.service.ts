import { Injectable } from '@nestjs/common';
import { CreateProductsInput } from './dto/create-products.input';
import { UpdateProductsInput } from './dto/update-products.input';
import { InjectRepository } from '@nestjs/typeorm';
import { Products } from './entities/products.entity';
import { IsNull, Repository } from 'typeorm';
import { EnumPaymentStatus, Shopping } from '../shopping/entities/shopping.entity';
import { Version } from '../versions/entities/version.entity';

export interface IDataVersionBookPurchase{
  version: Version;
  shopping:Shopping;
  product:Products
}
@Injectable()
export class ProductsService {

  constructor(
    @InjectRepository(Products)
    private readonly productsRepository: Repository<Products>,
  ){}

  create(createItemInput: CreateProductsInput) {
    return 'This action adds a new item';
  }

  findAll() {
    return `This action returns all item`;
  }

  findOne(id: number) {
    return `This action returns a #${id} item`;
  }

  update(id: number, updateItemInput: UpdateProductsInput) {
    return `This action updates a #${id} item`;
  }

  remove(id: number) {
    return `This action removes a #${id} item`;
  }

  async getProducts_ByPeopleId(peopleId: string):Promise<Products[]> {
    return await this.productsRepository
        .createQueryBuilder('products')
        .innerJoin('products.shopping', 'shopping')
        .where('shopping.peopleId=:peopleId', { peopleId })
        .andWhere('products.dateStart<=:now', { now: new Date() })
        .andWhere('products.dateEnd>=:now', { now: new Date() })
        .getMany();
  }

  async getProducts_ByShoppingId(shoppingId: string):Promise<Products[]> {
    return await this.productsRepository.find({
      where: { shopping: { id: shoppingId }, deletedAt: IsNull() },
    });
  }

  async getVersionsBookPurchases_ByPeopleId_BookId( peopleId: string, bookId: string): Promise<IDataVersionBookPurchase[]> {
    const res = await this.productsRepository
      .createQueryBuilder('products')
      .leftJoin('products.shopping', 'shopping')
      .leftJoin('products.version', 'version')
      .select('version.id','version_id')
      .addSelect('version.coverPath','version_coverPath')
      .addSelect('version.bookPath','version_bookPath')
      .addSelect('version.versionActive','version_versionActive')
      .addSelect('version.is_books_pending_send','version_is_books_pending_send')
      .addSelect('shopping.paymentStatus', 'shopping_paymentStatus')
      .addSelect('shopping.peopleId','shopping_peopleId')
      .addSelect('products.dateStart','products_dateStart')
      .addSelect('products.dateEnd','products_dateEnd')
      .where('shopping.paymentStatus = :status', {
        status: EnumPaymentStatus.PAGO_CONFIRMADO,
      })
      .andWhere('shopping.peopleId = :peopleId', { peopleId: peopleId })
      .andWhere('version.bookId = :bookId', { bookId: bookId })
      .orderBy('version.createdAt', 'DESC')
      .getRawMany();
      console.log('getVersionsBoolPurchases_ByPeopleId_BookId');
      console.log(res);
      
      const resI:IDataVersionBookPurchase[] = res.map((item)=>{
        return {
          version: {
            id: item.version_id,
            coverPath: item.version_coverPath,
            bookPath: item.version_bookPath,
            versionActive: item.version_versionActive,
            is_books_pending_send: item.version_is_books_pending_send,
          } as Version,
          shopping:{
            paymentStatus: item.shopping_paymentStatus,
            peopleId: item.shopping_peopleId
          } as Shopping,
          product: {
            dateStart: item.products_dateStart,
            dateEnd: item.products_dateEnd
          } as Products
        }
      });


    return resI;

  }
}
