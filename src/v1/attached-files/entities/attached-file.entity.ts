


import { Field, ObjectType } from '@nestjs/graphql';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import File from 'src/v1/files/entities/file.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum EnumNombreTabla_AttachedFile {
  VERSIONES = 'versiones',
}

@ObjectType()
@Entity({ name: 'attached_files'})
export default class AttachedFile extends AuditableEntity {
    @Field()
    @PrimaryGeneratedColumn()
    id: number;

    @Field({nullable: true})
    @Column({ name: 'nombre_tabla', type:'enum', enum:EnumNombreTabla_AttachedFile, nullable:false})
    nombre_tabla: EnumNombreTabla_AttachedFile;

    @Field({nullable: true})
    @Column({ name: 'id_registro'})
    registro_id: string;

    @Field({nullable: true})
    @Column({ name: 'file_id'})
    file_id: string;
    @Field( type=>File)
    @ManyToOne( type=> File, file=>file.attached_files )
    @JoinColumn({name: 'file_id' })
    file:Promise<File>
}