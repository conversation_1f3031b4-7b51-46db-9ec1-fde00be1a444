import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import AttachedFile, { EnumNombreTabla_AttachedFile } from './entities/attached-file.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AttachedFilesService {
    constructor(
        @InjectRepository(AttachedFile)
        private attachedFileRepository: Repository<AttachedFile>,
    ){}

    async getArchivosAdjuntos_Registro(nombre_tabla: EnumNombreTabla_AttachedFile, registro_id:string ):Promise<AttachedFile[]>{
        console.log("getArchivosAdjuntos_Registro")
        console.log(nombre_tabla);
        console.log(registro_id)
        const res= await this.attachedFileRepository.find({where:{nombre_tabla: nombre_tabla, registro_id: registro_id}});
        console.log("res getArchivosAdjuntos_Registro")
        console.log(res);
        return res;
    }

    async saveArchivosAdjuntos( nombre_tabla:EnumNombreTabla_AttachedFile , registro_id:string, archivos_ids:string[]):Promise<AttachedFile[]>{

        let registro_archivos= await this.attachedFileRepository.find({where:{nombre_tabla: nombre_tabla, registro_id:registro_id }});
        let ids_para_eliminar= [];
        for (const l_a of registro_archivos) {
            let r= archivos_ids.findIndex(aid=> aid==l_a.file_id);
            if(r>=0){
                //registro encontrado entonces se mantiene 
                archivos_ids.splice(r,1);
            }else{
                //el archivo ya no pertenece a la leccion 
                ids_para_eliminar.push( l_a.id );
            }
        }
        if(ids_para_eliminar.length>0){
            let r_archivos_eliminados= await this.attachedFileRepository.delete(ids_para_eliminar);
            console.log( r_archivos_eliminados);
        }

        let r_a_para_insertar: Partial<AttachedFile>[]=[] ;
        for (const a_id of archivos_ids) {
            r_a_para_insertar.push({file_id:a_id, registro_id: registro_id, nombre_tabla:nombre_tabla});
        }
        let r_a_insertados= await this.attachedFileRepository.save(r_a_para_insertar);
        let nuevos_r_a = await this.attachedFileRepository.find({where:{ registro_id:registro_id, nombre_tabla:nombre_tabla  }});
        
        return nuevos_r_a;
    }
}
