import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import { AttachedFilesService } from './attached-files.service';
import AttachedFile, { EnumNombreTabla_AttachedFile } from './entities/attached-file.entity';
import { Public } from 'src/common/decorators/public.decorator';

@Resolver()
export class AttachedFilesResolver {

    constructor(private archivos_adjuntosService: AttachedFilesService) {}
  
    @Mutation(() => [AttachedFile])
    async saveFiles_DeRegistro(
      @Args('registro_id', { type: () => String }) registro_id: string,  
      @Args('nombre_tabla',) nombre_tabla: EnumNombreTabla_AttachedFile,
      @Args('files_ids', { type: () => [String] }) files_ids: string[],
    ): Promise<AttachedFile[]> {
      return await this.archivos_adjuntosService.saveArchivosAdjuntos(nombre_tabla, registro_id, files_ids);
    }
  
    @Public()
    @Query(() => [AttachedFile],)
    public async getFiles_DeRegistro(
      @Args('nombre_tabla') nombre_tabla:EnumNombreTabla_AttachedFile,  
      @Args('registro_id',{type:()=>String}) registro_id: string,
    ):Promise<AttachedFile[]>{
        return await this.archivos_adjuntosService.getArchivosAdjuntos_Registro(nombre_tabla, registro_id);
    }
}
