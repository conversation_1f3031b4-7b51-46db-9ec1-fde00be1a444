import { Module } from '@nestjs/common';
import { AttachedFilesService } from './attached-files.service';
import { AttachedFilesResolver } from './attached-files.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import AttachedFile from './entities/attached-file.entity';

@Module({
  providers: [AttachedFilesService, AttachedFilesResolver],
  imports: [ 
    TypeOrmModule.forFeature([AttachedFile]),
    AttachedFilesModule
  ],
  exports: [AttachedFilesService]
})
export class AttachedFilesModule {}
