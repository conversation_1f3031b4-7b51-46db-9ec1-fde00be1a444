import { InputType, Int, Field, Float, ObjectType } from '@nestjs/graphql';
import {
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsPositive,
  IsString,
} from 'class-validator';

@ObjectType()
export class SumasTotalesDto {
  @Field()
  @IsNumber()
  cuotaTotal: number;

  @Field()
  @IsNumber()
  capitalTotal: number;
}

@ObjectType()
export class EconomixReturnDto {
  @Field()
  @IsNumber()
  nroCuota: number;

  @Field()
  @IsNumber()
  capital: number;

  @Field()
  @IsNumber()
  interes: number;

  @Field()
  @IsNumber()
  cuotaSinSeguro: number;

  @Field()
  @IsNumber()
  seguroDesgravement: number;

  @Field()
  @IsNumber()
  seguroGarantiaInmueble: number;

  @Field()
  @IsNumber()
  seguroGarantiaVehiculo: number;

  @Field()
  @IsNumber()
  tre: number;

  @Field()
  @IsNumber()
  cuotaTotal: number;

  @Field()
  @IsNumber()
  saldoCapital: number;

  @Field()
  @IsNumber()
  tiempoDias: number;
}

@ObjectType()
class CreditInsuranceR {
  @Field()
  data: string;

  @Field(() => Float)
  value: number;
}

@ObjectType()
class TreR {
  @Field()
  data: string;

  @Field(() => Float)
  value: number;
}

@ObjectType()
class RealEstateMortgageR {
  @Field()
  data: string;

  @Field(() => Float)
  constructionValue: number;

  @Field(() => Float)
  fireInsuranceRate: number;
}

@ObjectType()
class VehicleMortgageR {
  @Field()
  data: string;

  @Field(() => Float)
  vehicleValue: number;

  @Field(() => Float)
  vehicleInsuranceRate: number;
}

@ObjectType()
class LoanDetailsR {
  @Field(() => Float)
  loadAmount: number;

  @Field(() => Int)
  creditTermValue: number;

  @Field()
  creditTerm: string;

  @Field()
  feeType: string;

  @Field()
  paymentFrequency: string;

  @Field(() => Float)
  interestRate: number;

  @Field()
  typeIncome: string;

  @Field()
  typeCredit: string;

  @Field()
  typeGuarante: string;

  @Field(() => Number)
  creditInsurance: number;

  @Field(() => TreR)
  tre: TreR;

  @Field(() => Number)
  realEstateMortgage: Number;

  @Field(() => Number)
  vehicleMortgage: Number;
}

@ObjectType()
export class MainDataDto {
  @Field(() => [EconomixReturnDto])
  data: EconomixReturnDto[];

  @Field(() => Float)
  totalCapital: number;
  @Field(() => Float)
  totalInteres: number;
  @Field(() => Float)
  totalCuotaSinSeguro: number;
  @Field(() => Float)
  totalSeguroDesgravement: number;
  @Field(() => Float)
  totalSeguroGarantiaInmueble: number;
  @Field(() => Float)
  totalSeguroGarantiaVehiculo: number;
  @Field(() => Float)
  totalTre: number;
  @Field(() => Float)
  totalCuotaTotal: number;
  @Field(() => Float)
  totalSaldoCapital: number;
  @Field(() => LoanDetailsR)
  body: LoanDetailsR;

  @Field(() => Boolean, {
    description: 'Indica si la garantía vehicular está activa.',
  })
  garantia_vehicular_activa: boolean;
}
