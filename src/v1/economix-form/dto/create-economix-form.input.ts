import {
  InputType,
  Int,
  Field,
  Float,
  registerEnumType,
} from '@nestjs/graphql';
import {
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsPositive,
  IsString,
  Min,
} from 'class-validator';
import {
  CreditTermOptions,
  FeeTypeOptions,
  PaymentFrecuencyOptions,
  TypeCreditOptions,
  TypeGuaranteeOptions,
  TypeIncomeOptions,
  YesNoOptions,
} from '../entities/economix-form.entity';

registerEnumType(CreditTermOptions, {
  name: 'CreditTermOptions',
  description: 'Opciones de plazo de crédito',
});

registerEnumType(FeeTypeOptions, {
  name: 'FeeTypeOptions',
  description: 'Opciones de tipo de credito',
});

registerEnumType(PaymentFrecuencyOptions, {
  name: 'PaymentFrecuencyOptions',
  description: 'Opciones de frecuencia de pago',
});

registerEnumType(TypeIncomeOptions, {
  name: 'TypeIncomeOptions',
  description: 'Opciones de tipo de ingreso',
});

registerEnumType(TypeCreditOptions, {
  name: 'TypeCreditOptionsIndep',
  description: 'Opciones de tipo de credito independiente',
});

registerEnumType(TypeGuaranteeOptions, {
  name: 'TypeGuaranteeOptions',
  description: 'Opciones de tipo de credito independiente',
});

registerEnumType(YesNoOptions, {
  name: 'YesNoOptions',
  description: 'Opciones solo puede ser SI o NO',
});

// Tipar datos json  : Seguro Desgravamen
@InputType()
class CreditInsurance {
  @Field((type) => YesNoOptions)
  @IsString()
  @IsEnum(YesNoOptions)
  data: YesNoOptions;

  @Field()
  @IsNumber()
  value: number;
}
// Tipar datos json  : TRE tasa de interes sumado a intereses variables
@InputType()
class TRE {
  @Field((type) => YesNoOptions)
  @IsString()
  @IsEnum(YesNoOptions)
  data: YesNoOptions;

  @Field()
  @IsNumber()
  value: number;
}
// Tipar datos json  : Hipoteca de inmueble
@InputType()
class RealEstateMortgage {
  @Field((type) => YesNoOptions)
  @IsString()
  @IsEnum(YesNoOptions)
  data: YesNoOptions;

  @Field()
  @IsNumber()
  constructionValue: number;

  @Field()
  @IsNumber()
  fireInsuranceRate: number;
}
// Tipar datos json  : Hipoteca Vehiculo
@InputType()
class VehicleMortgage {
  @Field((type) => YesNoOptions)
  @IsString()
  @IsEnum(YesNoOptions)
  data: YesNoOptions;

  @Field()
  @IsNumber()
  vehicleValue: number;

  @Field()
  @IsNumber()
  vehicleInsuranceRate: number;
}

@InputType()
export class CreateEconomixFormInputDto {
  @Field(() => Int, { description: 'El campo es requerido' })
  @IsInt()
  @Min(0)
  @IsPositive()
  loadAmount: number; // Monto prestado

  @Field(() => Int, { description: 'El campo es requerido' })
  @IsInt()
  @IsPositive()
  @Min(0)
  creditTermValue: number; // Plazo de credito valor

  @Field((type) => CreditTermOptions)
  @IsString()
  @IsNotEmpty()
  @IsEnum(CreditTermOptions)
  creditTerm: CreditTermOptions; // Plazo de credito meses | años

  @Field((type) => FeeTypeOptions)
  @IsString()
  @IsNotEmpty()
  @IsEnum(FeeTypeOptions)
  feeType: FeeTypeOptions; // Tipo de cuota

  @Field((type) => PaymentFrecuencyOptions)
  @IsString()
  @IsNotEmpty()
  @IsEnum(PaymentFrecuencyOptions)
  paymentFrequency: PaymentFrecuencyOptions; // Frecuencia de pago

  @Field(() => Float)
  @Min(0)
  @IsNotEmpty()
  interestRate: number; // Tasa de interes

  @Field((type) => TypeIncomeOptions)
  @IsString()
  @IsNotEmpty()
  @IsEnum(TypeIncomeOptions)
  typeIncome: TypeIncomeOptions; // Tipo de ingreso

  @Field((type) => TypeCreditOptions)
  @IsString()
  @IsEnum(TypeCreditOptions)
  typeCredit: TypeCreditOptions;

  @Field((type) => TypeGuaranteeOptions)
  @IsString()
  @IsNotEmpty()
  @IsEnum(TypeGuaranteeOptions)
  typeGuarante: TypeGuaranteeOptions;

  @Field((type) => CreditInsurance)
  // @Type((type) => CreditInsurance)
  @IsObject()
  creditInsurance: CreditInsurance;

  @Field((type) => TRE)
  // @Type((type) => TRE)
  @IsObject()
  tre: TRE;

  @Field((type) => RealEstateMortgage) // Hipoteca de inmueble
  @IsObject()
  realEstateMortgage: RealEstateMortgage;

  @Field((type) => VehicleMortgage) // Hipoteca de vehiculo
  @IsObject()
  vehicleMortgage: VehicleMortgage;
}
