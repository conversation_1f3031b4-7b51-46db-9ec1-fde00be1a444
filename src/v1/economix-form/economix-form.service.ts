import { Injectable } from '@nestjs/common';
import { CreateEconomixFormInputDto } from './dto/create-economix-form.input';
import { UpdateEconomixFormInput } from './dto/update-economix-form.input';
import { CreateAdminInput } from '../admins/dto/create-admin.input';
import { ValidatorInputs } from './entities/validatorEconomix';
import { EconomixReturnDto, MainDataDto } from './dto/economix-return.input';

@Injectable()
export class EconomixFormService {
  private dataValues: object = {};
  private data: object[] = [];
  // Datos recibidos del cliente
  private loadAmount: number = 0; // 0 ... n
  private plazoMeses: number = 0; // siempre meses, si envia años => meses
  private paymentFrequency: string = ''; // mensual, bimestral, trmestral, etc
  private interestRate: number = 0; // 0 ... 1
  private periodoGracia: number = 0; // periodo de gracia ?
  private feeType: string = ''; // tipo de cuenta: Cuota Fija | Cuota Variable
  private typeIncome: string = ''; // Tipo de ingreso  Asalariado | Independiente
  private typeCredit: string = ''; // Tipo de ingreso  Asalariado | Independiente
  private creditInsurance: string = 'si'; // seguro de desgravamen   SI | NO
  private creditInsuranceValue: number = 0; // tasa de seguro desgravamen 1% => 100%
  private tre: string = ''; // TRE:  SI | NO
  private treValue: number = 0; // TRE: Interes %  tre
  private realEstateMortgage: string = ''; // Hipoteca inmueble: SI | NO
  private realEstateMortgageContruction: number = 0; // Hipoteca inmueble -> Valor de construcion
  private realEstateMortgageFireInsurance: number = 0; // Hipoteca inmueble -> % Seguro incendio
  private vehicleMortgage: string = ''; // Hipoteca vehiculo: SI | NO
  private vehicleMortgageValue: number = 0; // Hipoteca vehiculo -> Valor Vehiculo
  private vehicleMortgageInsurance: number = 0; // Hipoteca vehiculo -> % Seguro de vehiculo
  // Datos auxiliares que se calculan
  private numeroDias: number = 0; // mensual 30, bimestral 60, trmestral 90, etc
  private interesAux: number = 0; // Calculo de interes auxiliar para cada fila
  private cuotaSinSeguroAux: number = 0; // Cuota sin seguro columna oculta
  private constructionInsurance: number = 0; // d11 Seguro construción: principalB29
  private vehicleInsurance: number = 0; // Seguro Vehicular: principalD29
  private columnV: number = 0; // V14 Columna V
  private validatorInput = new ValidatorInputs();
  // Datos a devolver al cliente
  private nroCuota: number = 1;
  private capital: number = 0;
  private interes: number = 0;
  private cuotaSinSeguro: number = 0;
  private seguroDesgravamen: number = 0;
  private seguroGarantiaInmueble: number = 0;
  private seguroGarantiaVehiculo: number = 0;
  private treR: number = 0;
  private cuotaTotal: number = 0;
  private saldoCapital: number = 0;
  private filas: number = 0;
  constructor() {}
  // ========== CREAR CALCULADOR DE PRESTAMOS ==========
  create(createEconomixFormInputDto: CreateEconomixFormInputDto): MainDataDto {
    const garantia_vehicular_activa =
      createEconomixFormInputDto.vehicleMortgage.data.toLocaleLowerCase() ===
      'si';

    // Cargar valores en las variables declaradas
    this.loadAmount = createEconomixFormInputDto.loadAmount; // agregar monto
    this.interestRate = createEconomixFormInputDto.interestRate / 100; // agregar interes
    // plazo en meses, si envia en años => meses
    if (createEconomixFormInputDto.creditTerm.toLocaleLowerCase() === 'anios') {
      this.plazoMeses = createEconomixFormInputDto.creditTermValue * 12; // agregar años * 12
    } else this.plazoMeses = createEconomixFormInputDto.creditTermValue; // agregar meses
    this.paymentFrequency =
      createEconomixFormInputDto.paymentFrequency.toLocaleLowerCase(); // agregar frecuencia de pago
    this.periodoGracia = 0; // Aun no se que es
    this.feeType = createEconomixFormInputDto.feeType.toLocaleLowerCase(); // agregar Tipo de cuota
    this.typeIncome = createEconomixFormInputDto.typeIncome.toLocaleLowerCase(); // Tipo de ingreso
    this.typeCredit = createEconomixFormInputDto.typeCredit; // Tipo de Crédito
    this.tre = createEconomixFormInputDto.tre.data; // Dato del TRE : SI | NO
    this.treValue = createEconomixFormInputDto.tre.value / 100; // Valor en % del TRE
    this.creditInsurance = createEconomixFormInputDto.creditInsurance.data; // Seguro desgravamen SI | NO
    this.creditInsuranceValue =
      createEconomixFormInputDto.creditInsurance.value / 12 / 100; // Seguro desgravamen 0 => 1 %
    this.realEstateMortgage =
      createEconomixFormInputDto.realEstateMortgage.data; // Hipoteca Inmueble SI | NO
    this.realEstateMortgageContruction =
      createEconomixFormInputDto.realEstateMortgage.constructionValue; // Hipoteca Inmueble -> Valor contruccion
    this.realEstateMortgageFireInsurance =
      createEconomixFormInputDto.realEstateMortgage.fireInsuranceRate; // Hipoteca Inmueble -> Seguro Insendio
    this.constructionInsurance =
      (this.realEstateMortgageContruction *
        this.realEstateMortgageFireInsurance) /
      100 /
      12; // Seguro de contrucción Calculo auxiliar desde Hipoteca de inmueble
    this.vehicleMortgage = createEconomixFormInputDto.vehicleMortgage.data; // Hipoteca vehiculo SI | NO
    this.vehicleMortgageValue =
      createEconomixFormInputDto.vehicleMortgage.vehicleValue; // Valor del vehiculo
    this.vehicleMortgageInsurance =
      createEconomixFormInputDto.vehicleMortgage.vehicleInsuranceRate / 100; // Tasa de seguro vehicular
    this.vehicleInsurance =
      (this.vehicleMortgageValue * this.vehicleMortgageInsurance) / 12; // Seguro Vehicular calculado

    // numeroDias => mensual30, bimestral60, trimestral90
    switch (this.paymentFrequency) {
      case 'mensual':
        this.numeroDias = 30; // mensual30, bimestral60, trimestral90
        this.filas = this.plazoMeses / 1;
        break;
      case 'bimestral':
        this.numeroDias = 60; // mensual30, bimestral60, trimestral90
        this.filas = this.plazoMeses / 2;
        break;
      case 'trimestral':
        this.numeroDias = 90; // mensual30, bimestral60, trimestral90
        this.filas = this.plazoMeses / 3;
        break;
      case 'semestral':
        this.numeroDias = 180; // mensual30, bimestral60, trimestral90
        this.filas = this.plazoMeses / 6;
        break;
    }
    //  ================  Aplicando validadores
    this.validatorInput.validarTipoCreadito(
      this.typeIncome,
      this.typeCredit,
      this.tre,
    );

    // =====> COLUMNA INTERES
    this.interesAux = this.calcularInteres(
      this.loadAmount, // d4 monto prestado
      this.interestRate, // d9 tasa de interes
      this.numeroDias, // h4 mensual30, bimestral60, trimestral90
    );
    this.interes = this.interesAux > 1 ? this.interesAux : 0;
    // =====> COLUMNA CAPITAL PARA CUOTA VARIABLE
    if (this.feeType === 'cuota_variable') {
      const capitalAux = this.calcularCapital(
        this.feeType, // d7 tipo de cuota
        this.interesAux, // e14  interes por pago
        0, // c14 = 0, capital para Cuota Variable aun no existe
        this.numeroDias, // h4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
        this.numeroDias, // u14 30 parece fijo
        1, // 1 contador de pagos incremental
        this.loadAmount, // 1000 Monto prestado
        this.plazoMeses, // 6 Plazo en meses
        this.periodoGracia, // 0 Periodo de gracia
        this.interestRate, // 0.1 tasa de interes, // 0.1 tasa de interes
        this.cuotaSinSeguro, // g14 171.56 cuota_sin_seguro
      );
      this.capital = capitalAux > 1 ? capitalAux : 0;
    }
    // =====> COLUMNA CUOTA SIN SEGURO PARA CUOTA VARIABLE
    if (this.feeType === 'cuota_variable') {
      this.cuotaSinSeguroAux = this.calcularCuotaSinSeguro(
        this.feeType, // d7
        this.interesAux, // e14 interes calculado para la frecuencia de pago
        this.capital, // c14 : capital calculado -> Si es "Cuota Variable"
        this.numeroDias, //h4 = mensual 30, bimestral 60, trimestral 180, etc.
        this.numeroDias, // u14 = mensual 30, bimestral 60, trimestral 180, etc
        1, // b14 = 1, contador incremental
        this.loadAmount, // d4 = monto prestado
        this.plazoMeses, // d5 = plazo en meses
        this.periodoGracia, // 0 Periodo de gracia no se que es aun
        this.interestRate, // d9 = 0.1 -> 1  tasa de interes
      );

      this.cuotaSinSeguro =
        this.cuotaSinSeguroAux > 1 ? this.cuotaSinSeguroAux : 0;
    }
    // =====> COLUMNA CUOTA SIN SEGURO PARA CUOTA FIJA
    if (this.feeType === 'cuota_fija') {
      this.cuotaSinSeguroAux = this.calcularCuotaSinSeguro(
        this.feeType, // d7
        this.interesAux, // e14 interes calculado para la frecuencia de pago
        0, // c14 = 163.23, capital calculado -> Si es "Cuota Fija" aun no existe capital
        this.numeroDias, //h4 = mensual 30, bimestral 60, trimestral 180, etc.
        this.numeroDias, // u14 = mensual 30, bimestral 60, trimestral 180, etc
        1, // b14 = 1, contador incremental
        this.loadAmount, // d4 = monto prestado
        this.plazoMeses, // d5 = plazo en meses
        this.periodoGracia, // 0 Periodo de gracia no se que es aun
        this.interestRate, // d9 = 0.1 -> 1  tasa de interes
      );
      this.cuotaSinSeguro =
        this.cuotaSinSeguroAux > 1 ? this.cuotaSinSeguroAux : 0;
    }
    // =====> COLUMNA CAPITAL PARA CUOTA FIJA
    if (this.feeType === 'cuota_fija') {
      const capitalAux = this.calcularCapital(
        this.feeType, // d7 tipo de cuota
        this.interesAux, // e14  interes por pago
        0, // c14 = 0, capital para cuota_variable aun no existe Si es "Cuota Fija" aun no existe capital
        this.numeroDias, // h4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
        this.numeroDias, // u14 30 parece fijo
        1, // 1 contador de pagos incremental
        this.loadAmount, // 1000 Monto prestado
        this.plazoMeses, // 6 Plazo en meses
        this.periodoGracia, // 0 Periodo de gracia
        this.interestRate, // 0.1 tasa de interes, // 0.1 tasa de interes
        this.cuotaSinSeguro, // g14 171.56 cuota_sin_seguro
      );
      this.capital = capitalAux > 1 ? capitalAux : 0;
    }
    // =====> COLUMNA SEGURO GARANTIA INMUEBLE
    const seguroGarantiaInmuebleAux = this.calculateSeguGarantiaInmueble(
      this.realEstateMortgage,
      this.capital,
      this.constructionInsurance,
    );
    this.seguroGarantiaInmueble =
      seguroGarantiaInmuebleAux > 1 ? seguroGarantiaInmuebleAux : 0;
    // =====> COLUMNA TRE
    this.columnV = this.calculateColumV(
      this.paymentFrequency, // D8
      this.interestRate, // D9 Tasa de interes 0 -> 1
      this.treValue, // E24 de la pantalla principal
      this.loadAmount, // Monto prestado
      this.numeroDias, // H4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
    );
    const treAux = this.calculateTRE(
      this.nroCuota, // Contador incremental
      this.paymentFrequency, // Frecuencia de pago
      this.interesAux, // interes calculado
      this.columnV, // colum V
    );
    this.treR = treAux > 1 ? treAux : 0;
    // =====> COLUMNA SALDO CAPITAL
    const saldoCapitalAux = this.loadAmount - this.capital;
    this.saldoCapital = saldoCapitalAux > 1 ? saldoCapitalAux : 0;
    // =====> COLUMNA SEGURO DESGRAVAMEN
    const seguroDesgravamenAux =
      this.creditInsurance === 'si'
        ? this.creditInsuranceValue * this.loadAmount
        : 0;
    this.seguroDesgravamen = Math.max(seguroDesgravamenAux ?? 0, 0);

    // =====> COLUMNA SEGURO GARANTIA VEHICULO ============
    const seguroGarantiaVehiculoAux = this.calculateSeguGarantiaVehiculo(
      this.vehicleMortgage,
      this.interes,
      this.vehicleInsurance,
    );
    this.seguroGarantiaVehiculo =
      seguroGarantiaVehiculoAux > 1 ? seguroGarantiaVehiculoAux : 0;
    // =====> COLUMNA CUOTA TOTAL
    const seguroGarantiaAplicable = garantia_vehicular_activa
      ? 0
      : seguroGarantiaInmuebleAux;
    const cuotaTotalAux =
      this.cuotaSinSeguroAux +
      seguroDesgravamenAux +
      seguroGarantiaAplicable +
      treAux;
    this.cuotaTotal = cuotaTotalAux > 1 ? cuotaTotalAux : 0;

    this.data = [];
    // ========== BUCLE REPETITIVO ==========
    for (let index = 1; index <= this.filas; index++) {
      this.data.push({
        nroCuota: this.nroCuota,
        capital: this.capital,
        interes: this.interes,
        cuotaSinSeguro: this.cuotaSinSeguro,
        seguroDesgravement: this.seguroDesgravamen,
        seguroGarantiaInmueble: this.seguroGarantiaInmueble,
        seguroGarantiaVehiculo: this.seguroGarantiaVehiculo,
        tre: this.treR,
        cuotaTotal: this.cuotaTotal,
        saldoCapital: this.saldoCapital,
        tiempoDias: this.numeroDias,
      });
      this.nroCuota += 1; // incremetar el contador de meses
      // =====> COLUMNA INTERES
      this.interesAux = this.calcularInteres(
        this.saldoCapital, // c14 capital anterior
        this.interestRate, // d9 Tasa de interes
        this.numeroDias, // h4
      );
      this.interes = this.interesAux > 1 ? this.interesAux : 0;
      // =====> COLUMNA CAPITAL PARA CUOTA VARIABLE
      if (this.feeType === 'cuota_variable') {
        const capitalAux = this.calcularCapital(
          this.feeType, // d7 tipo de cuota
          this.interesAux, // e14  interes por pago
          0, // c14 = 0, capital para Cuota Variable aun no existe
          this.numeroDias, // h4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
          this.numeroDias, // u14 30 parece fijo
          this.nroCuota, // 1 contador de pagos incremental
          this.loadAmount, // 1000 Monto prestado
          this.plazoMeses, // 6 Plazo en meses
          this.periodoGracia, // 0 Periodo de gracia
          this.interestRate, // 0.1 tasa de interes, // 0.1 tasa de interes
          this.cuotaSinSeguro, // g14 171.56 cuota_sin_seguro
        );
        this.capital = capitalAux > 1 ? capitalAux : 0;
      }
      // =====> COLUMNA CUOTA SIN SEGURO PARA CUOTA VARIABLE
      if (this.feeType === 'cuota_variable') {
        this.cuotaSinSeguroAux = this.calcularCuotaSinSeguro(
          this.feeType, // d7
          this.interesAux, // e14 interes calculado para la frecuencia de pago
          this.capital, // c14 : capital calculado -> Si es "Cuota Variable"
          this.numeroDias, //h4 = mensual 30, bimestral 60, trimestral 180, etc.
          this.numeroDias, // u14 = mensual 30, bimestral 60, trimestral 180, etc
          this.nroCuota, // b14 = 1, contador incremental
          this.loadAmount, // d4 = monto prestado
          this.plazoMeses, // d5 = plazo en meses
          this.periodoGracia, // 0 Periodo de gracia no se que es aun
          this.interestRate, // d9 = 0.1 -> 1  tasa de interes
        );
        this.cuotaSinSeguro =
          this.cuotaSinSeguroAux > 1 ? this.cuotaSinSeguroAux : 0;
      }
      // =====> COLUMNA CUOTA SIN SEGURO PARA CUOTA FIJA
      if (this.feeType === 'cuota_fija') {
        this.cuotaSinSeguroAux = this.calcularCuotaSinSeguro(
          this.feeType, // d7
          this.interesAux, // e14 interes calculado para la frecuencia de pago
          0, // c14 = 163.23, capital calculado -> Si es "Cuota Fija" aun no existe capital
          this.numeroDias, //h4 = mensual 30, bimestral 60, trimestral 180, etc.
          this.numeroDias, // u14 = mensual 30, bimestral 60, trimestral 180, etc
          this.nroCuota, // b14 = 1, contador incremental
          this.loadAmount, // d4 = monto prestado
          this.plazoMeses, // d5 = plazo en meses
          this.periodoGracia, // 0 Periodo de gracia no se que es aun
          this.interestRate, // d9 = 0.1 -> 1  tasa de interes
        );
        this.cuotaSinSeguro =
          this.cuotaSinSeguroAux > 1 ? this.cuotaSinSeguroAux : 0;
      }
      // =====> COLUMNA CAPITAL PARA CUOTA FIJA
      if (this.feeType === 'cuota_fija') {
        const capitalAux = this.calcularCapital(
          this.feeType, // d7 tipo de cuota
          this.interesAux, // e14  interes por pago
          0, // c14 = 0, capital para cuota_variable aun no existe Si es "Cuota Fija" aun no existe capital
          this.numeroDias, // h4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
          this.numeroDias, // u14 30 parece fijo
          this.nroCuota, // 1 contador de pagos incremental
          this.loadAmount, // 1000 Monto prestado
          this.plazoMeses, // 6 Plazo en meses
          this.periodoGracia, // 0 Periodo de gracia
          this.interestRate, // 0.1 tasa de interes, // 0.1 tasa de interes
          this.cuotaSinSeguro, // g14 171.56 cuota_sin_seguro
        );
        this.capital = capitalAux > 1 ? capitalAux : 0;
      }
      // =====> COLUMNA SEGURO GARANTIA INMUEBLE
      const seguroGarantiaInmuebleAux = this.calculateSeguGarantiaInmueble(
        this.realEstateMortgage,
        this.capital,
        this.constructionInsurance,
      );
      this.seguroGarantiaInmueble =
        seguroGarantiaInmuebleAux > 1 ? seguroGarantiaInmuebleAux : 0;
      // =====> COLUMNA TRE
      this.columnV = this.calculateColumV(
        this.paymentFrequency, // D8
        this.interestRate, // D9 Tasa de interes 0 -> 1
        this.treValue, // E24 de la pantalla principal
        this.saldoCapital, // Monto prestado
        this.numeroDias, // H4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
      );
      const treAux = this.calculateTRE(
        this.nroCuota, // Contador incremental
        this.paymentFrequency, // Frecuencia de pago
        this.interesAux, // interes calculado
        this.columnV, //
      );
      this.treR = treAux > 1 ? treAux : 0;
      // =====> COLUMNA SEGURO DESGRAVAMEN
      const seguroDesgravamenAux =
        this.creditInsurance === 'si'
          ? this.creditInsuranceValue * this.saldoCapital
          : 0;
      this.seguroDesgravamen = Math.max(seguroDesgravamenAux ?? 0, 0);

      // =====> COLUMNA SEGURO GARANTIA VEHICULO ============
      const seguroGarantiaVehiculoAux = this.calculateSeguGarantiaVehiculo(
        this.vehicleMortgage,
        this.interes,
        this.vehicleInsurance,
      );
      this.seguroGarantiaVehiculo =
        seguroGarantiaVehiculoAux > 1 ? seguroGarantiaVehiculoAux : 0;
      // =====> COLUMNA SALDO CAPITAL
      const saldoCapitalAux = this.saldoCapital - this.capital;
      this.saldoCapital = saldoCapitalAux > 1 ? saldoCapitalAux : 0;

      // =====> COLUMNA CUOTA TOTAL
      const seguroGarantiaAplicable = garantia_vehicular_activa
        ? 0
        : seguroGarantiaInmuebleAux;
      const cuotaTotalAux =
        this.cuotaSinSeguroAux +
        seguroDesgravamenAux +
        seguroGarantiaAplicable +
        treAux;
      this.cuotaTotal = cuotaTotalAux > 1 ? cuotaTotalAux : 0;
    }
    this.nroCuota = 1;

    const sumasTotales = this.sumarColumnas(this.data as EconomixReturnDto[]);

    return {
      data: this.data as EconomixReturnDto[],
      totalCapital: sumasTotales.capital,
      totalInteres: sumasTotales.interes,
      totalCuotaSinSeguro: sumasTotales.cuotaSinSeguro,
      totalSeguroDesgravement: sumasTotales.seguroDesgravement,
      totalSeguroGarantiaInmueble: sumasTotales.seguroGarantiaInmueble,
      totalSeguroGarantiaVehiculo: sumasTotales.seguroGarantiaVehiculo,
      totalTre: sumasTotales.tre,
      totalCuotaTotal:
        sumasTotales.interes +
        sumasTotales.seguroDesgravement +
        (garantia_vehicular_activa
          ? sumasTotales.seguroGarantiaVehiculo
          : sumasTotales.seguroGarantiaInmueble) +
        sumasTotales.tre +
        createEconomixFormInputDto.loadAmount,
      totalSaldoCapital: sumasTotales.saldoCapital,
      garantia_vehicular_activa,
      body: {
        loadAmount: createEconomixFormInputDto.loadAmount,
        creditTermValue: this.plazoMeses,
        creditTerm: createEconomixFormInputDto.creditTerm,
        feeType: createEconomixFormInputDto.feeType,
        paymentFrequency: createEconomixFormInputDto.paymentFrequency,
        interestRate: createEconomixFormInputDto.interestRate,
        typeIncome: createEconomixFormInputDto.typeIncome,
        typeCredit: createEconomixFormInputDto.typeCredit,
        typeGuarante: createEconomixFormInputDto.typeGuarante,
        creditInsurance: this.creditInsuranceValue,
        tre: createEconomixFormInputDto.tre,
        realEstateMortgage: this.constructionInsurance,
        vehicleMortgage: this.vehicleInsurance,
      },
    };
  }
  // --------------------------------------------------------------------------
  private calcularInteres(monto: number, interestRate: number, mes: number) {
    return (interestRate * monto) / (360 / this.numeroDias);
  }
  // --------------------------------------------------------------------------
  private calcularCuotaSinSeguro(
    d7, // d7 = 'cuota fija',
    e14, // e14 = 8.33,
    c14, // c14 = 163.23,
    h4, // mensual, bimestral, trimestral, semestral
    u14, // 30 parece fijo
    b14, // 1 contador de pagos incremental
    d4, // 1000 Monto prestado
    d5, // Plazo en meses
    d6, // 0 Periodo de gracia
    d9, // tasa de interes
  ) {
    // Verificar si la cuota es variable
    if (d7 === 'cuota_variable') {
      return this.calculateCuotaVariable02(e14, c14);
    }
    // Verificar si la cuota es fija
    else if (d7 === 'cuota_fija') {
      return this.calculateCuotaFija02(h4, u14, b14, d4, d5, d6, d9, e14);
    }
    // Cuota no reconocida
    else {
      return 0;
    }
  }
  // --------------------------------------------------------------------------
  // Calcular cuota variable
  private calculateCuotaVariable02(e14, c14) {
    return e14 + c14;
  }
  // --------------------------------------------------------------------------
  // Calcular cuota fija
  private calculateCuotaFija02(h4, u14, b14, d4, d5, d6, d9, e14) {
    if (
      h4 === u14 &&
      b14 > (360 / h4) * (d6 / 12) &&
      (360 / h4) * (d5 / 12) >= b14
    ) {
      const tipoInteres = d9 / (360 / h4);
      const baseExponente = 1 + tipoInteres;
      const periodo = -(360 / h4) * ((d5 - d6) / 12);
      const divisor = 1 - Math.pow(baseExponente, periodo);
      return d4 * (tipoInteres / divisor);
    } else {
      return e14;
    }
  }

  // --------------------------------------------------------------------------

  private calcularCapital(
    d7, // d7 = 'cuota fija',
    e14, // e14 = 8.33, interes
    c14, // c14 = 0 -> Si es "Cuota Fija" aun no existe capital
    h4, // 30, mensual, bimestral, trimestral, semestral
    u14, // 30 parece fijo
    b14, // 1 contador de pagos incremental
    d4, // 1000 Monto prestado
    d5, // 6 Plazo en meses
    d6, // 0 Periodo de gracia
    d9, // 0.1 tasa de interes
    g14, // g14 171.56 cuota_sin_seguro
  ) {
    // Verificar si la cuota es variable
    if (d7 === 'cuota_variable') {
      return this.calculateCuotaVariable(
        h4,
        u14,
        b14,
        d4,
        d5,
        d6,
        d9,
        e14,
        c14,
      );
    }
    // Verificar si la cuota es fija
    else if (d7 === 'cuota_fija') {
      return this.calculateCuotaFija(h4, u14, b14, d4, d5, d6, d9, e14, g14);
    }
    // Cuota no reconocida
    else {
      return 0;
    }
  }
  // --------------------------------------------------------------------------
  // Calcular cuota fija
  private calculateCuotaFija(h4, u14, b14, d4, d5, d6, d9, e14, g14) {
    if (
      h4 === u14 &&
      b14 > (360 / h4) * (d6 / 12) &&
      (360 / h4) * (d5 / 12) >= b14
    ) {
      return g14 - e14;
    } else {
      return 0;
    }
  }

  // Calcular cuota variable
  private calculateCuotaVariable(h4, u14, b14, d4, d5, d6, d9, e14, c14) {
    if (
      h4 === u14 &&
      b14 > (360 / h4) * (d6 / 12) &&
      (360 / h4) * (d5 / 12) >= b14
    ) {
      return d4 / ((360 / h4) * (d5 / 12 - d6 / 12));
    } else {
      return 0;
    }
  }
  // Calcular el TRE
  private calculateTRE(B14: number, D8: string, E14: number, V14D: number) {
    if (this.tre == 'si') {
      if (
        (D8 === 'mensual' && B14 > 12) ||
        (D8 === 'bimestral' && B14 > 6) ||
        (D8 === 'trimestral' && B14 > 4) ||
        (D8 === 'semestral' && B14 > 2)
      ) {
        return V14D - E14;
      } else {
        return 0;
      }
    } else return 0;
  }
  // Calcular seguro garantia inmueble
  private calculateColumV(
    D8, // D8 frecuencia de pago
    D9, // D9 tasa de interes 0 -> 1
    E24, // principal E24 tre value
    R14, // saldo capital calculado
    H4, // H4 numero de dias
  ) {
    if (
      D8 === 'mensual' ||
      D8 === 'bimestral' ||
      D8 === 'trimestral' ||
      D8 === 'semestral'
    ) {
      return ((D9 + E24) * R14) / (360 / H4);
    } else return 0;
  }

  private calculateSeguGarantiaInmueble(
    B28, // B28 principal seg garantia hipoteca inmueble
    C14, // C14 Capital Calculado : C14 dinamico
    D11, // D11 Seguro contruccion
  ) {
    if (B28 === 'si' && C14 > 1) {
      return D11;
    } else return 0;
  }

  private calculateSeguGarantiaVehiculo(
    D28, // D28 principal seg garantia hipoteca vehicular
    D14, // D14 Interes Calculado : C14 dinamico
    D12, // D12 Seguro vehicular
  ) {
    if (D28 === 'si' && D14 > 1) {
      return D12;
    } else return 0;
  }

  private sumarColumnas(data: EconomixReturnDto[]): EconomixReturnDto {
    const total = data.reduce(
      (acumulador, item) => {
        return {
          nroCuota: acumulador.nroCuota + item.nroCuota,
          capital: acumulador.capital + item.capital,
          interes: acumulador.interes + item.interes,
          cuotaSinSeguro: acumulador.cuotaSinSeguro + item.cuotaSinSeguro,
          seguroDesgravement:
            acumulador.seguroDesgravement + item.seguroDesgravement,
          seguroGarantiaInmueble:
            acumulador.seguroGarantiaInmueble + item.seguroGarantiaInmueble,
          seguroGarantiaVehiculo:
            acumulador.seguroGarantiaVehiculo + item.seguroGarantiaVehiculo,
          tre: acumulador.tre + item.tre,
          cuotaTotal: acumulador.cuotaTotal + item.cuotaTotal,
          saldoCapital: acumulador.saldoCapital + item.saldoCapital,
          tiempoDias: acumulador.tiempoDias + item.tiempoDias,
        };
      },
      {
        nroCuota: 0,
        capital: 0,
        interes: 0,
        cuotaSinSeguro: 0,
        seguroDesgravement: 0,
        seguroGarantiaInmueble: 0,
        seguroGarantiaVehiculo: 0,
        tre: 0,
        cuotaTotal: 0,
        saldoCapital: 0,
        tiempoDias: 0,
      },
    );

    return total;
  }
}

// Si la cuota es variable primero calcular la columna Capital
// =============  Mutacion
// mutation($createEconomixFormInputDto: CreateEconomixFormInputDto!){
//   createEconomixForm(createEconomixFormInputDto:$createEconomixFormInputDto){
//     nroCuota
//     capital
//     interes
//     cuotaSinSeguro
//   }
// }

// =============  Query varuables
// {
//   "createEconomixFormInputDto":{
//       "loadAmount":5000,
//       "creditTermValue":2,
//       "creditTerm":"Meses",
//       "feeType":"Cuota_Fija",
//       "paymentFrequency":"Mensual",
//       "interestRate":10,
//       "typeIncome":"Asalariado",
//       "typeCredit":"Credito_Vivienda",
//       "typeGuarante":"Hipoteca_Inmueble",
//       "creditInsurance":{"data":"SI","value":1.08},
//       "tre":{"data":"SI","value":3.01},
//       "realEstateMortgage":{"data":"SI","constructionValue":25000,"fireInsuranceRate":0.180},
//       "vehicleMortgage":{"data":"SI","vehicleValue":20000,"vehicleInsuranceRate":2.800}
//     }
// }
