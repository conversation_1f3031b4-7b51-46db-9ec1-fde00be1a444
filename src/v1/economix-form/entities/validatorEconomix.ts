import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class ValidatorInputs {
  constructor() {}

  validarTipoCreadito(
    tipoCredito: string,
    tipoCreditoOpcion: string,
    tre: string,
  ) {
    // console.log(tipoCredito, tipoCreditoOpcion, tre);

    if (tipoCredito === 'asalariado') {
      const valoresPermitidos = Object.values(TipoCreditoAsalariado).join(', ');
      if (
        !Object.values(TipoCreditoAsalariado).includes(
          tipoCreditoOpcion as TipoCreditoAsalariado,
        )
      ) {
        throw new BadRequestException(
          `Los únicos valores permitidos para tipoCreditoOpcion son: ${valoresPermitidos}`,
        );
      }
      this.validarTreSiTipoCredito(tipoCreditoOpcion, tre);
    }
    // ======== INDEPENDIENTE
    if (tipoCredito === 'independiente') {
      const valoresPermitidos = Object.values(TipoCreditoIndependiente).join(
        ', ',
      );
      if (
        !Object.values(TipoCreditoIndependiente).includes(
          tipoCreditoOpcion as TipoCreditoIndependiente,
        )
      ) {
        throw new BadRequestException(
          `Los únicos valores permitidos para tipoCreditoOpcion son: ${valoresPermitidos}`,
        );
      }
      this.validarTreSiTipoCredito(tipoCreditoOpcion, tre);
    }
  }

  validarTreSiTipoCredito(tipoCredito: string, tre: string) {
    if (
      tipoCredito === 'Credito_Vivienda_Social' ||
      tipoCredito === 'Credito_Productivo'
    ) {
      if (tre !== 'no') {
        console.log('Si entro');

        throw new BadRequestException(
          `Si el tipo de credito es: ${tipoCredito}, TRE debe ser: no`,
        );
      }
    }
  }

  calcularMontoMaximo(interes: number, valorInmueble: number): number {
    return valorInmueble * interes;
  }
}
// Si el tipo de ingreso es Asalariado debe estar entre una de estas opciones
enum TipoCreditoAsalariado {
  Credito_Vivienda_Social = 'Credito_Vivienda_Social',
  Credito_Vivienda = 'Credito_Vivienda',
  Credito_Vehicular_Nuevo = 'Credito_Vehicular_Nuevo',
  Credito_Consumo = 'Credito_Consumo',
}
// Si el tipo de ingreso es Independiente, debe estar en una de estas opciones
enum TipoCreditoIndependiente {
  Credito_Vivienda_Social = 'Credito_Vivienda_Social',
  Credito_Vivienda = 'Credito_Vivienda',
  Credito_Negocio = 'Credito_Negocio',
  Credito_Vehicular_Nuevo_Independiente = 'Credito_Vehicular_Nuevo_Independiente',
  Credito_Vehicular_Usado = 'Credito_Vehicular_Usado',
  Credito_Productivo = 'Credito_Productivo',
  Credito_Consumo = 'Credito_Consumo',
}
