import { ObjectType, Field, Int } from '@nestjs/graphql';

export enum CreditTermOptions {
  Meses = 'Meses',
  Anios = 'Anios',
}

export enum FeeTypeOptions {
  Cuota_Fija = 'Cuota_Fija',
  Cuota_Variable = 'Cuota_Variable',
}

export enum PaymentFrecuencyOptions {
  Mensual = 'Mensual',
  Bimestral = 'Bimestral',
  Trimestral = 'Trimestral',
  Semestral = 'Semestral',
}
// Opciones de ingreso
export enum TypeIncomeOptions {
  Asalariado = 'Asalariado',
  Independiente = 'Independiente',
}
// Opciones de tipo de crédito Independiente
export enum TypeCreditOptions {
  Credito_Vivienda_Social = 'Credito_Vivienda_Social',
  Credito_Vivienda = 'Credito_Vivienda',
  Credito_Negocio = 'Credito_Negocio',
  Credito_Vehicular_Nuevo_Independiente = 'Credito_Vehicular_Nuevo_Independiente',
  Credito_Vehicular_Nuevo = 'Credito_Vehicular_Nuevo',
  Credito_Vehicular_Usado = 'Credito_Vehicular_Usado',
  Credito_Productivo = 'Credito_Productivo',
  Credito_Consumo = 'Credito_Consumo',
}

export enum TypeGuaranteeOptions {
  Hipoteca_de_Inmueble = 'Hipoteca_de_Inmueble',
  Hipoteca_de_Inmueble_VS = 'Hipoteca_de_Inmueble_VS',
  Hipoteca_de_Inmueble_VS_1 = 'Hipoteca_de_Inmueble_VS_1',
  Hipoteca_de_Inmueble_VS_2 = 'Hipoteca_de_Inmueble_VS_2',
  Hipoteca_de_Inmueble_VS_3 = 'Hipoteca_de_Inmueble_VS_3',
  Hipoteca_de_Vehiculo_Nuevo = 'Hipoteca_de_Vehiculo_Nuevo',
  Hipoteca_de_Vehiculo_Nuevo_Independiente = 'Hipoteca_de_Vehiculo_Nuevo_Independiente',
  Hipoteca_de_Vehiculo_Usado = 'Hipoteca_de_Vehiculo_Usado',
  Personal = 'Personal',
  Prendaria = 'Prendaria',
  Sola_Firma = 'Sola_Firma',
  Hipoteca_Inmueble_Productivo = 'Hipoteca_Inmueble_Productivo',
  Hipoteca_Vehiculo_Productivo = 'Hipoteca_Vehiculo_Productivo',
  Personal_Procutivo = 'Personal_Procutivo',
  Prendaria_Procutivo = 'Prendaria_Procutivo',
}

// export enum TypeGuaranteeOptions {
//   PROPERTY = 'Hipoteca de Inmueble',
// }

export enum YesNoOptions {
  SI = 'si',
  NO = 'no',
}

@ObjectType()
export class EconomixForm {
  @Field(() => Int, { description: 'Example field (placeholder)' })
  exampleField: number;
}
