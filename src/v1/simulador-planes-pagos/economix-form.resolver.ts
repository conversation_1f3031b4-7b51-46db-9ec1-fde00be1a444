import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { EconomixFormService } from './economix-form.service';
import { EconomixForm } from './entities/economix-form.entity';
import { CreateEconomixFormInputDto } from './dto/create-economix-form.input';
import { UpdateEconomixFormInput } from './dto/update-economix-form.input';
import { Public } from 'src/common/decorators/public.decorator';
import { EconomixReturnDto } from './dto/economix-return.input';

@Resolver(() => EconomixForm)
export class EconomixFormResolver {
  constructor(private readonly economixFormService: EconomixFormService) {}

  // @Mutation((returns) => [EconomixReturnDto])
  // @Public()
  // createEconomixForm(
  //   @Args('createEconomixFormInputDto')
  //   createEconomixFormInputDto: CreateEconomixFormInputDto,
  // ) {
  //   // console.log('🚀createEconomixFormInputDto:', createEconomixFormInputDto);
  //   return this.economixFormService.create(createEconomixFormInputDto);
  //   // return null;
  // }

  @Query((returns) => [EconomixForm], { name: 'economixFormCeledonio' })
  findAllCeledonio() {
    return this.economixFormService.findAll();
  }

  // @Query(() => EconomixForm, { name: 'economixForm' })
  // findOne(@Args('id', { type: () => Int }) id: number) {
  //   return this.economixFormService.findOne(id);
  // }

  // @Mutation(() => EconomixForm)
  // updateEconomixForm(@Args('updateEconomixFormInput') updateEconomixFormInput: UpdateEconomixFormInput) {
  //   return this.economixFormService.update(updateEconomixFormInput.id, updateEconomixFormInput);
  // }

  // @Mutation(() => EconomixForm)
  // removeEconomixForm(@Args('id', { type: () => Int }) id: number) {
  //   return this.economixFormService.remove(id);
  // }
  @Mutation((returns) => [EconomixForm])
  @Public()
  testEconomixForm(@Args('id', { type: () => Int }) id: number) {
    console.log('Esto es una peticion de prueba: ', id);
    return null;
  }
}
