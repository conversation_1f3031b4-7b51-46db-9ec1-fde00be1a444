import { ObjectType, Field, Int } from '@nestjs/graphql';

export enum CreditTermOptions {
  MONTHS = 'Meses',
  YEARS = 'Años',
}

export enum FeeTypeOptions {
  FIXED = 'Cuota Vairable',
  VARIABLE = 'Cuota Fija',
}

export enum PaymentFrecuencyOptions {
  MONTHLY = 'Mensual',
  BIMONTHLY = 'Bimestral',
  QUARTERLY = 'Trimestral',
  SEMIANNUAL = 'Semestral',
}

export enum TypeIncomeOptions {
  SALARIED = 'Asalariado',
  INDEPENDENT = 'Independiente',
}

export enum TypeCreditOptions {
  HOUSING_CREDIT = 'Crédito.Vivienda',
  SOCIAL_HOUSING_CREDIT = 'Credito.Vivienda.Social',
  NEW_VEHICLE_CREDIT = 'Credito.Vehicular.nuevo',
  CONSUMER_CREDIT = 'Credito.Consumo',
}

export enum TypeGuaranteeOptions {
  PROPERTY = 'Hipoteca de Inmueble',
}

@ObjectType()
export class EconomixForm {
  @Field(() => Int, { description: 'Example field (placeholder)' })
  exampleField: number;
}
