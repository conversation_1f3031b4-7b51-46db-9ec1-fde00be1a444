import { Injectable } from '@nestjs/common';
import { CreateEconomixFormInputDto } from './dto/create-economix-form.input';
import { UpdateEconomixFormInput } from './dto/update-economix-form.input';

@Injectable()
export class EconomixFormService {
  private dataValues: object = {};
  private data: object[] = [];

  private interestRate: number = 0; // 0 ... 1
  private loadAmount: number = 0; // 0 ... n
  private numeroDias: number = 0; // mensual 30, bimestral 60, trmestral 90, etc
  private plazoMeses: number = 0; // siempre meses, si envia años => meses
  private periodoGracia: number = 0; // periodo de gracia ?
  private feeType: string = ''; // periodo de gracia ?
  private loanProtectionInsuranceRate: number = 0.09 / 100; // tase de seguro desgravamen
  // =========================

  private nroCuota: number = 0;
  private capital: number = 0;
  private interes: number = 0;
  private cuotaSinSeguro: number = 0;
  private seguroDesgravement: number = 0;
  private seguroGarantiaInmueble: number = 0;
  private seguroGarantiaVehiculo: number = 0;
  private tre: string = null;
  private cuotaTotal: number = null;
  private saldoCapital: number = null;
  private tiempoDias: number = 30;
  constructor() {}

  create(createEconomixFormInputDto: CreateEconomixFormInputDto) {
    this.interestRate = createEconomixFormInputDto.interestRate / 100; // add
    this.loadAmount = createEconomixFormInputDto.loadAmount; // add
    this.plazoMeses = 6; // plazo en meses si envia en años => meses
    this.periodoGracia = 0;
    this.numeroDias = 30; // mensual30, bimestral60, trimestral90
    this.feeType = createEconomixFormInputDto.feeType.toLocaleLowerCase(); // Tipo de cuota
    console.log(createEconomixFormInputDto); // datos del cliente
    // =====> Calcular interes
    const interesAux = this.calcularInteres(
      createEconomixFormInputDto.loadAmount,
      this.interestRate,
      30,
    );
    this.interes = interesAux > 1 ? interesAux : 0;
    // =====> calcular Cuota sin seguro
    const cuotaSinSeguroAux = this.calcularCuotaSinSeguro(
      createEconomixFormInputDto.feeType.toLocaleLowerCase(),
      this.interes,
      0, // c14 = 163.23, capital calculado
      30, //h4 = 30, mensual 30, bimestral 60, trimestral 180, semestral
      30, // u14 = 30 parece fijo
      1, // b14 = 1, contador incremental
      createEconomixFormInputDto.loadAmount, // d4 = 1000, monto prestado
      6, // d5 = 6, plazo en meses
      0, // 0 Periodo de gracia
      createEconomixFormInputDto.interestRate / 100, // d9 = 0.1, // 0.1 tasa de interes
    );
    this.cuotaSinSeguro = cuotaSinSeguroAux > 1 ? cuotaSinSeguroAux : 0;
    // =====> calcular capital
    const capitalAux = this.calcularCapital(
      createEconomixFormInputDto.feeType.toLocaleLowerCase(),
      this.interes, // e14  interes por pago
      0, // c14 = 0, capital para cuota_variable aun no existe
      30, // h4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
      30, // u14 30 parece fijo
      1, // 1 contador de pagos incremental
      createEconomixFormInputDto.loadAmount, // 1000 Monto prestado
      6, // 6 Plazo en meses
      0, // 0 Periodo de gracia
      createEconomixFormInputDto.interestRate / 100, // 0.1 tasa de interes, // 0.1 tasa de interes
      this.cuotaSinSeguro, // g14 171.56 cuota_sin_seguro
    );
    this.capital = capitalAux > 1 ? capitalAux : 0;
    // =====> saldo capital
    const saldoCapitalAux =
      createEconomixFormInputDto.loadAmount - this.capital;
    this.saldoCapital = saldoCapitalAux > 1 ? saldoCapitalAux : 0;
    // =====> columna desconocida
    this.seguroDesgravement =
      this.loanProtectionInsuranceRate * this.loadAmount;
    // console.log(
    //   '🚀 ~ EconomixFormService ~ create ~ unknown:',
    //   this.seguroDesgravement,
    // );

    // =====> Cuota Total
    const cuotaTotalAux = this.cuotaSinSeguro + this.seguroDesgravement;
    this.cuotaTotal = cuotaTotalAux > 1 ? cuotaTotalAux : 0;

    this.data = [];

    for (let index = 1; index <= 2; index++) {
      this.data.push({
        nroCuota: index,
        capital: this.capital,
        interes: this.interes,
        cuotaSinSeguro: this.cuotaSinSeguro,
        seguroDesgravement: this.seguroDesgravement,
        seguroGarantiaInmueble: 0,
        seguroGarantiaVehiculo: 0,
        // tre: 0,
        cuotaTotal: this.cuotaTotal,
        saldoCapital: this.saldoCapital,
        tiempoDias: this.tiempoDias,
      });

      const interesAux = this.calcularInteres(
        this.saldoCapital,
        this.interestRate,
        this.tiempoDias,
      );
      this.interes = interesAux > 1 ? interesAux : 0;

      // =====> calcular cuota sin seguro
      const cuotaSinSeguroAux = this.calcularCuotaSinSeguro(
        createEconomixFormInputDto.feeType.toLocaleLowerCase(),
        this.interes,
        0, // c14 = 163.23, capital calculado
        this.numeroDias, //h4 = 30, mensual 30, bimestral 60, trimestral 180, semestral
        this.tiempoDias, // u14 = 30 parece fijo
        index + 1, // b14 = 1, contador incremental
        this.loadAmount, // d4 = 1000, monto prestado
        this.plazoMeses, // d5 = 6, plazo en meses siempre meses
        this.periodoGracia, // 0 Periodo de gracia
        this.interestRate, // d9 = 0.1, // 0.1 tasa de interes
      );
      this.cuotaSinSeguro = cuotaSinSeguroAux > 1 ? cuotaSinSeguroAux : 0;
      // =====> calcular capital
      const capitalAux = this.calcularCapital(
        this.feeType, // tipo de cuota
        this.interes, // e14  interes por pago
        0, // c14 = 0, capital para cuota_variable aun no existe
        this.numeroDias, // h4 30, mensual 30, bimestral 60, trimestral 90, semestral etc.
        this.tiempoDias, // u14 30 parece fijo
        index + 1, // 1 contador de pagos incremental
        createEconomixFormInputDto.loadAmount, // 1000 Monto prestado
        this.plazoMeses, // 6 Plazo en meses
        this.periodoGracia, // 0 Periodo de gracia
        this.interestRate, // 0.1 tasa de interes, // 0.1 tasa de interes
        this.cuotaSinSeguro, // g14 171.56 cuota_sin_seguro
      );
      this.capital = capitalAux > 1 ? capitalAux : 0;
      // =====> saldo capital
      const saldoCapitalAux = this.saldoCapital - this.capital;
      this.saldoCapital = saldoCapitalAux > 1 ? saldoCapitalAux : 0;
      // =====> columna desconocida
      let unknown = (0.09 / 100) * this.saldoCapital;

      // =====> Cuota Total
      const cuotaTotalAux = this.cuotaSinSeguro + 1000;
      // this.cuotaTotal = cuotaTotalAux > 1 ? cuotaTotalAux : 0;
      this.cuotaTotal = 100;
      console.log(this.cuotaTotal);
    }

    this.dataValues = {
      data: this.data,
    };
    // console.log('🚀 Data Body: ', this.data);

    return this.data;
  }
  // --------------------------------------------------------------------------
  private calcularInteres(monto: number, interestRate: number, mes: number) {
    return (interestRate * monto) / (360 / 30);
  }
  // --------------------------------------------------------------------------
  private calcularCuotaSinSeguro(
    d7, // d7 = 'cuota fija',
    e14, // e14 = 8.33,
    c14, // c14 = 163.23,
    h4, // mensual, bimestral, trimestral, semestral
    u14, // 30 parece fijo
    b14, // 1 contador de pagos incremental
    d4, // 1000 Monto prestado
    d5, // 6 Plazo en meses
    d6, // 0 Periodo de gracia
    d9, // 0.1 tasa de interes
  ) {
    // Verificar si la cuota es variable
    if (d7 === 'cuota variable') {
      return this.calculateCuotaVariable02(e14, c14);
    }
    // Verificar si la cuota es fija
    else if (d7 === 'cuota fija') {
      return this.calculateCuotaFija02(h4, u14, b14, d4, d5, d6, d9, e14);
    }
    // Cuota no reconocida
    else {
      return 0;
    }
  }
  // --------------------------------------------------------------------------
  // Calcular cuota variable
  private calculateCuotaVariable02(e14, c14) {
    return e14 + c14;
  }
  // --------------------------------------------------------------------------
  // Calcular cuota fija
  private calculateCuotaFija02(h4, u14, b14, d4, d5, d6, d9, e14) {
    if (
      h4 === u14 &&
      b14 > (360 / h4) * (d6 / 12) &&
      (360 / h4) * (d5 / 12) >= b14
    ) {
      const tipoInteres = d9 / (360 / h4);
      const baseExponente = 1 + tipoInteres;
      const periodo = -(360 / h4) * ((d5 - d6) / 12);
      const divisor = 1 - Math.pow(baseExponente, periodo);
      return d4 * (tipoInteres / divisor);
    } else {
      return e14;
    }
  }

  // --------------------------------------------------------------------------

  private calcularCapital(
    d7, // d7 = 'cuota fija',
    e14, // e14 = 8.33, interes
    c14, // c14 = 0, capital cuota_variable aun no existe
    h4, // 30, mensual, bimestral, trimestral, semestral
    u14, // 30 parece fijo
    b14, // 1 contador de pagos incremental
    d4, // 1000 Monto prestado
    d5, // 6 Plazo en meses
    d6, // 0 Periodo de gracia
    d9, // 0.1 tasa de interes
    g14, // g14 171.56 cuota_sin_seguro
  ) {
    // Verificar si la cuota es variable
    if (d7 === 'cuota variable') {
      return this.calculateCuotaVariable(
        h4,
        u14,
        b14,
        d4,
        d5,
        d6,
        d9,
        e14,
        c14,
      );
    }
    // Verificar si la cuota es fija
    else if (d7 === 'cuota fija') {
      return this.calculateCuotaFija(h4, u14, b14, d4, d5, d6, d9, e14, g14);
    }
    // Cuota no reconocida
    else {
      return 0;
    }
  }
  // --------------------------------------------------------------------------
  // Calcular cuota fija
  private calculateCuotaFija(h4, u14, b14, d4, d5, d6, d9, e14, g14) {
    if (
      h4 === u14 &&
      b14 > (360 / h4) * (d6 / 12) &&
      (360 / h4) * (d5 / 12) >= b14
    ) {
      return g14 - e14;
    } else {
      return 0;
    }
  }

  // Calcular cuota variable
  private calculateCuotaVariable(h4, u14, b14, d4, d5, d6, d9, e14, c14) {
    if (
      h4 === u14 &&
      b14 > (360 / h4) * (d6 / 12) &&
      (360 / h4) * (d5 / 12) >= b14
    ) {
      return d4 / ((360 / h4) * (d5 / 12 - d6 / 12));
    } else {
      return 0;
    }
  }

  findAll() {
    return `This action returns all economixForm`;
  }
}
