import { InputType, Int, Field, Float, ObjectType } from '@nestjs/graphql';
import {
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsPositive,
  IsString,
} from 'class-validator';
import { CreditTermOptions } from '../entities/economix-form.entity';

@ObjectType()
export class EconomixReturnDto {
  @Field()
  @IsNumber()
  nroCuota: number;

  @Field()
  @IsNumber()
  capital: number;

  @Field()
  @IsNumber()
  interes: number;

  @Field()
  @IsNumber()
  cuotaSinSeguro: number;

  @Field()
  @IsNumber()
  seguroDesgravement: number;

  @Field()
  @IsNumber()
  seguroGarantiaInmueble: number;

  @Field()
  @IsNumber()
  seguroGarantiaVehiculo: number;

  @Field()
  @IsNumber()
  tre: number;

  @Field()
  @IsNumber()
  cuotaTotal: number;

  @Field()
  @IsNumber()
  saldoCapital: number;

  @Field()
  @IsNumber()
  tiempoDias: number;
}
