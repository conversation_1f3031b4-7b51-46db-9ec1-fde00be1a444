import { InputType, Int, Field, Float } from '@nestjs/graphql';
import { IsIn, IsInt, IsNotEmpty, IsPositive, IsString } from 'class-validator';
import {
  CreditTermOptions,
  FeeTypeOptions,
  PaymentFrecuencyOptions,
  TypeCreditOptions,
  TypeGuaranteeOptions,
  TypeIncomeOptions,
} from '../entities/economix-form.entity';

@InputType()
export class CreateEconomixFormInputDto {
  @Field(() => Int, { description: 'El campo es requerido' })
  @IsInt()
  @IsPositive()
  loadAmount: number;

  @Field(() => Int, { description: 'El campo es requerido' })
  @IsInt()
  @IsPositive()
  creaditTermValue: number;

  @Field({ description: 'El campo es requerido' })
  @IsString()
  @IsNotEmpty()
  @IsIn([CreditTermOptions.MONTHS, CreditTermOptions.YEARS])
  creaditTerm: CreditTermOptions;

  @Field()
  @IsString()
  @IsNotEmpty()
  @IsIn([FeeTypeOptions.FIXED, FeeTypeOptions.VARIABLE])
  feeType: FeeTypeOptions;

  @Field()
  @IsString()
  @IsNotEmpty()
  @IsIn([
    PaymentFrecuencyOptions.MONTHLY,
    PaymentFrecuencyOptions.BIMONTHLY,
    PaymentFrecuencyOptions.QUARTERLY,
    PaymentFrecuencyOptions.SEMIANNUAL,
  ])
  paymentFrecuency: PaymentFrecuencyOptions;

  @Field(() => Float)
  @IsNotEmpty()
  interestRate: number;

  @Field()
  @IsString()
  @IsNotEmpty()
  @IsIn([TypeIncomeOptions.SALARIED, TypeIncomeOptions.INDEPENDENT])
  typeIncome: TypeIncomeOptions;

  @Field()
  @IsString()
  @IsNotEmpty()
  @IsIn([
    TypeCreditOptions.HOUSING_CREDIT,
    TypeCreditOptions.SOCIAL_HOUSING_CREDIT,
    TypeCreditOptions.CONSUMER_CREDIT,
    TypeCreditOptions.NEW_VEHICLE_CREDIT,
  ])
  typeCredit: TypeCreditOptions;

  @Field()
  @IsString()
  @IsNotEmpty()
  @IsIn([TypeGuaranteeOptions.PROPERTY])
  typeGuarante: TypeGuaranteeOptions;
}
