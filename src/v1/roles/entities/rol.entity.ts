import { Field, ObjectType } from '@nestjs/graphql';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Admin } from 'src/v1/admins/entities/admin.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

@ObjectType()
@Entity({ name: 'roles' })
export default class Rol extends AuditableEntity {
  @Field()
  @PrimaryGeneratedColumn()
  id: number;

  @Field({ nullable: true })
  @Column({ type:'varchar',default:'', nullable: true })
  nombre: string;

  @Field({ nullable: true })
  @Column({ type:'varchar', default:'', nullable: true})
  cod_rol: string;

  @Field(type => [Admin])
  @OneToMany(type => Admin, admin => admin.rol)
  administradores: Promise<Admin[]>;
}
