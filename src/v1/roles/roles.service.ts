import { Injectable } from '@nestjs/common';
import Rol from './entities/rol.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class RolesService {
    constructor( 
        @InjectRepository(Rol)
        private readonly rolRepository: Repository<Rol>,
    ) {}

    getRolAdmin(){
        return this.rolRepository.findOne({where: {cod_rol: 'admin'}});
    }
}
