import { ObjectType, Field, Int } from '@nestjs/graphql';
import {
  IsString,
  IsUUID,
  Length,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Book } from 'src/v1/books/entities/book.entity';
import { Simulator } from 'src/v1/simulators/entities/simulator.entity';
import { Version } from 'src/v1/versions/entities/version.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

@Entity()
@ObjectType()
export class Chapter extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Name must be a string' })
  name?: string;

  @Column({ default: null })
  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'ChapterNumber must be a number' })
  chapterNumber?: number;

  // @ManyToOne(() => Book, (book) => book.chapter)
  // book: Book;

  @Field(() => Version)
  @ManyToOne(() => Version, (version) => version.chapter, { eager: true })
  version: Version;

  @OneToMany(() => Simulator, (simulator) => simulator.chapter)
  @Field(() => [Simulator])
  simulators: Simulator[];

  // @RelationId((chapter: Chapter) => chapter.book)
  // bookId: string;

  //atributo para decir que el el panel del capitulos esta abierto
  @Column({ default: false })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Name must be a string' })
  open?: boolean;
}
