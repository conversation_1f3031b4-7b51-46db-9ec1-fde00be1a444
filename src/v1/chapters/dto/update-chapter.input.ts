import { InputType, Field, Int } from '@nestjs/graphql';
import { IsString, IsNumber } from 'class-validator';

@InputType()
export class UpdateChapterInput {
  @Field(() => String)
  id: string;

  @Field({ nullable: true })
  @IsString({ message: 'Name must be a string' })
  name: string;

  @Field(() => Int)
  @IsNumber({}, { message: 'ChapterNumber must be a number' })
  chapterNumber: number;
}

@InputType()
export class UpdateChapterNumberInput {
  @Field(() => [UpdateChapterInput])
  chapters: UpdateChapterInput[];
}
@InputType()
export class UpdateOrderChapterSimulatorsInput {
  @Field(() => String)
  chapterId: string;

  @Field(() => [SimulatorOrder])
  simulators: SimulatorOrder[];
}

@InputType()
export class SimulatorOrder {
  @Field(() => String)
  id: string;

  @Field(() => String)
  name: string;

  @Field(() => Int)
  simulatorNumber: number;
}

@InputType()
export class UpdateChapterOpenInput {
  @Field(() => String)
  id: string;

  @Field(() => Boolean)
  open: boolean;
}
