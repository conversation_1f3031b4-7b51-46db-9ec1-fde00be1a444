import { InputType, Int, Field } from '@nestjs/graphql';
import { IsString, IsNumber, IsUUID } from 'class-validator';

@InputType()
export class CreateChapterInput {
  @Field({ nullable: true })
  @IsString({ message: 'Name must be a string' })
  name: string;

  @Field(() => Int)
  @IsNumber({}, { message: 'ChapterNumber must be a number' })
  chapterNumber?: number;

  @Field(() => String)
  @IsUUID('4', { message: 'Invalid UUID format for bookId' })
  versionId: string;

}
