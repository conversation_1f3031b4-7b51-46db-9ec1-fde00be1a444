import { Module } from '@nestjs/common';
import { ChaptersService } from './chapters.service';
import { ChaptersResolver } from './chapters.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chapter } from './entities/chapter.entity';
import { SimulatorsModule } from '../simulators/simulators.module';
import { SheetsModule } from '../sheets/sheets.module';
import { FormulasModule } from '../formulas/formulas.module';


@Module({
  imports: [TypeOrmModule.forFeature([Chapter]), SimulatorsModule, SheetsModule,
  FormulasModule
],
  exports: [ChaptersService],
  providers: [ChaptersResolver, ChaptersService],
})
export class ChaptersModule {}
