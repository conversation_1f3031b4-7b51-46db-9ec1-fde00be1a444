import {
  Resolver,
  Query,
  Mutation,
  Args,
  Int,
  Parent,
  ResolveField,
} from '@nestjs/graphql';
import { ChaptersService } from './chapters.service';
import { Chapter } from './entities/chapter.entity';
import { CreateChapterInput } from './dto/create-chapter.input';
import {
  UpdateChapterInput,
  UpdateChapterNumberInput,
  UpdateChapterOpenInput,
  UpdateOrderChapterSimulatorsInput,
} from './dto/update-chapter.input';
import { Simulator } from '../simulators/entities/simulator.entity';
import { SimulatorsService } from '../simulators/simulators.service';
import { UseGuards } from '@nestjs/common';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Public } from 'src/common/decorators/public.decorator';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';

export interface ChapterUpdateInput {
  id: string;
  chapterNumberNew: number;
  chapterNumberOld: number;
}

@Resolver(() => Chapter)
export class ChaptersResolver {
  constructor(
    private readonly chaptersService: ChaptersService,
    private readonly simulatorsService: SimulatorsService,
  ) {}

  // @Mutation(() => Chapter)
  // createChapter(@Args('createChapterInput') createChapterInput: CreateChapterInput) {
  //   return this.chaptersService.create(createChapterInput);
  // }

  @Public()
  @Query(() => [Chapter])
  async getChapters_ByVersionId(
    @Args('versionId', { type: () => String }) versionId: string,
  ): Promise<Chapter[]> {
    return await this.chaptersService.getChapters_ByVersionId(versionId);
  }

  @Public()
  @Query(() => Simulator)
  async getSimulator_ByNameAndVersionId(
    @Args('versionId', { type: () => String }) versionId: string,
    @Args('simulatorName', { type: () => String }) simulatorName: string,
  ): Promise<Simulator> {
    return await this.chaptersService.getSimulator_ByNameAndVersionId(
      versionId,
      simulatorName,
    );
  }

  @Query(() => String, { nullable: true })
  async getActiveVersionIdBySimulatorName(
    @Args('simulatorName', { type: () => String }) simulatorName: string,
  ): Promise<string | null> {
    return await this.chaptersService.getVersionActiveId_BySimulatorName(
      simulatorName,
    );
  }

  @Public()
  @Query(() => [Chapter])
  async getChapters_BySimulatorName(
    @Args('simulatorName', { type: () => String }) simulatorName: string,
  ): Promise<Chapter[]> {
    const versionActiveId =
      await this.chaptersService.getVersionActiveId_BySimulatorName(
        simulatorName,
      );
    if (!versionActiveId) {
      return [];
    }
    return await this.chaptersService.getChapters_ByVersionId(versionActiveId);
  }

  @ResolveField(() => [Simulator])
  async simulators(@Parent() chapter: Chapter): Promise<Simulator[]> {
    const res = await this.simulatorsService.getSimulators_ByChapterId(
      chapter.id,
    );
    return res.map((val) => {
      return {
        ...val,
        coverSimulatorPath: val?.coverSimulatorPath
          ? process.env.HOST_ADMIN + '/' + val.coverSimulatorPath
          : null,
      } as Simulator;
    });
  }

  @Query(() => Chapter)
  async getChapter_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<Chapter> {
    return await this.chaptersService.getChapter_ById(id);
  }

  // @UseGuards(JwtAuthAdminGuard)
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Mutation(() => Chapter)
  async insertChapter(
    @Args('chapterData') chapterData: CreateChapterInput,
  ): Promise<Chapter> {
    return await this.chaptersService.insertChapter(chapterData);
  }

  // @UseGuards(JwtAuthAdminGuard)
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Mutation(() => Chapter)
  async updateChapter(
    @Args('chapterData') chapterData: UpdateChapterInput,
  ): Promise<Chapter> {
    return await this.chaptersService.updateChapter(chapterData);
  }

  // @UseGuards(JwtAuthAdminGuard)
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Mutation(() => Boolean)
  async deleteChapter_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<boolean> {
    return await this.chaptersService.deleteChapter_ById(id);
  }

  @Query(() => Chapter)
  async getChapter_byID2(
    @Args('id', { type: () => String }) id: string,
  ): Promise<Chapter> {
    return await this.chaptersService.getChapter_byID2(id);
  }

  // @UseGuards(JwtAuthAdminGuard)
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Mutation(() => Boolean)
  async updateChapterNumber(
    @Args('chapterData') chapterData: UpdateChapterNumberInput,
  ): Promise<boolean> {
    return await this.chaptersService.updateChapterNumber(chapterData);
  }

  @Mutation(() => Boolean)
  async sortTheChapterSimulators(
    @Args('OrderData') OrderData: UpdateOrderChapterSimulatorsInput,
  ): Promise<boolean> {
    return await this.chaptersService.sortTheChapterSimulators(OrderData);
  }

  @Mutation(() => Boolean)
  async updateChapterOpen(
    @Args('chapterData') chapterData: UpdateChapterOpenInput,
  ): Promise<boolean> {
    return await this.chaptersService.updateChapterOpen(chapterData);
  }

  // @Query(() => [Chapter], { name: 'chapters' })
  // findAll() {
  //   return this.chaptersService.findAll();
  // }

  // @Query(() => Chapter, { name: 'chapter' })
  // findOne(@Args('id', { type: () => Int }) id: number) {
  //   return this.chaptersService.findOne(id);
  // }

  // @Mutation(() => Chapter)
  // updateChapter(@Args('updateChapterInput') updateChapterInput: UpdateChapterInput) {
  //   return this.chaptersService.update(updateChapterInput.id, updateChapterInput);
  // }

  // @Mutation(() => Chapter)
  // removeChapter(@Args('id', { type: () => Int }) id: number) {
  //   return this.chaptersService.remove(id);
  // }

  //  @ResolveField(() => [Simulator])
  // async simulators(@Parent() chapter: Chapter): Promise<Simulator[]> {
  //   let res = await this.simulatorsService.getSimulators_ByChapterId(Simulator.id);
  //   return res;
  // }

  @Mutation(() => Boolean)
  async asignarNumeroDeOrdenEnSimudores(
    @Args('versionId') versionId: string,
  ): Promise<boolean> {
    return await this.chaptersService.asignarNumeroDeOrdenEnSimudores(
      versionId,
    );
  }

  @Mutation(() => Boolean)
  async copyBookVersionChapters(
    @Args('surceVersionId') surceVersionId: string,
    @Args('destinationVersionId') destinationVersionId: string,
  ): Promise<boolean> {
    return await this.chaptersService.copyBookVersionChapters(
      surceVersionId,
      destinationVersionId,
    );
  }
}
