import { Injectable } from '@nestjs/common';
import { CreateChapterInput } from './dto/create-chapter.input';
import {
  UpdateChapterInput,
  UpdateChapterNumberInput,
  UpdateChapterOpenInput,
  UpdateOrderChapterSimulatorsInput,
} from './dto/update-chapter.input';
import { InjectConnection, InjectRepository } from '@nestjs/typeorm';
import { Chapter } from './entities/chapter.entity';
import { Connection, IsNull, Repository } from 'typeorm';
import { Book } from '../books/entities/book.entity';
import { Version } from '../versions/entities/version.entity';
import { SimulatorsService } from '../simulators/simulators.service';
import { SheetsService } from '../sheets/sheets.service';
import { FormulasService } from '../formulas/formulas.service';
import { v4 as uuidv4 } from 'uuid';
import { Simulator } from '../simulators/entities/simulator.entity';
import { Sheet } from '../sheets/entities/sheet.entity';
import { Formula } from '../formulas/entities/formula.entity';

@Injectable()
export class ChaptersService {
  constructor(
    @InjectRepository(Chapter)
    private readonly chapterRepository: Repository<Chapter>,
    private simulatorsService: SimulatorsService,
    private sheetsService: SheetsService,
    private formulasService: FormulasService,
    @InjectConnection() private readonly connection: Connection,
  ) {}

  async getChapters_ByVersionId(versionId: string): Promise<Chapter[]> {
    const res = await this.chapterRepository.find({
      where: { version: { id: versionId, deletedAt: IsNull() } },
      order: { chapterNumber: 'ASC' },
    });
    console.log(res);
    return res;
  }

  async getSimulator_ByNameAndVersionId(
    versionId: string,
    simulatorName: string,
  ): Promise<Simulator> {
    const chapters = await this.chapterRepository.find({
      where: { version: { id: versionId, deletedAt: IsNull() } },
      relations: ['simulators'],
      order: { chapterNumber: 'ASC' },
    });

    for (const chapter of chapters) {
      const simulator = chapter.simulators.find(
        (sim: Simulator) => sim.name === simulatorName,
      );
      if (simulator) {
        return simulator;
      }
    }

    throw new Error(
      `Simulador con nombre ${simulatorName} no encontrado en la versión ${versionId}`,
    );
  }

  async getVersionActiveId_BySimulatorName(
    simulatorName: string,
  ): Promise<string | null> {
    const activeChapter = await this.chapterRepository
      .createQueryBuilder('chapter')
      .leftJoinAndSelect('chapter.simulators', 'simulators')
      .leftJoinAndSelect('chapter.version', 'version')
      .where('simulators.name = :simulatorName', { simulatorName })
      .andWhere('version.versionActive = :active', { active: true })
      .getOne();

    return activeChapter ? activeChapter.version.id : null;
  }

  async getChapter_ById(id: string): Promise<Chapter> {
    const chapter = await this.chapterRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });
    if (!chapter) throw new Error('Capítulo no existe');
    return chapter;
  }

  //migracion
  async insertChapter(data: CreateChapterInput): Promise<Chapter> {
    const chapter = this.chapterRepository.create(data);
    chapter.version = { id: data.versionId } as Version;
    return await this.chapterRepository.save(chapter);
  }

  async updateChapter(data: UpdateChapterInput): Promise<Chapter> {
    const chapter = await this.getChapter_ById(data.id);
    const chapter_update = this.chapterRepository.create(data);
    return await this.chapterRepository.save(chapter_update);
  }

  async deleteChapter_ById(id: string): Promise<boolean> {
    const chapter = await this.getChapter_ById(id);
    const delete_result = await this.chapterRepository.update(
      { id },
      { deletedAt: new Date() },
    );
    if (delete_result.affected === 0)
      throw new Error('No se pudo eliminar el capítulo');
    return true;
  }

  async getChapter_byID2(id: string): Promise<Chapter> {
    const chapter = await this.chapterRepository.findOne({
      where: { id },
      relations: ['simulators', 'version', 'version.book'],
    });
    return chapter;
  }

  async updateChapterNumber(data: UpdateChapterNumberInput): Promise<boolean> {
    const chapters = await this.chapterRepository.findByIds(
      data.chapters.map((val) => val.id),
    );
    const chapters_update = chapters
      .map((chapter) => {
        const updatedChapter = data.chapters.find(
          (val) => val.id === chapter.id,
        );
        if (
          updatedChapter &&
          updatedChapter.chapterNumber !== chapter.chapterNumber
        ) {
          return { ...chapter, chapterNumber: updatedChapter.chapterNumber };
        }
        return undefined;
      })
      .filter((chapter) => chapter !== undefined);
    if (chapters_update.length > 0) {
      const update_result = await this.chapterRepository.save(chapters_update);
      if (update_result.length === 0)
        throw new Error('No se pudo actualizar el número de capítulo');
    }
    return true;
  }

  async asignarNumeroDeOrdenEnSimudores(version_id: string): Promise<boolean> {
    let capitulos: Chapter[] = await this.getChapters_ByVersionId(version_id);
    console.log(capitulos.length);
    for (let i = 0; i < capitulos.length; i++) {
      const simuladores =
        await this.simulatorsService.getSimulators_ByChapterId(capitulos[i].id);
      // let simuladores = capitulos[i].simulators;
      // console.log(simuladores);
      for (let j = 0; j < simuladores.length; j++) {
        simuladores[j].simulatorNumber = j + 1;
      }
      // //  const a= await this.chapterRepository.save(capitulos[i]);
      const a = await this.simulatorsService.saveSimulators(simuladores);
      //  console.log('capts')
      //  console.log(a)
    }
    console.log('capitulos');

    return true;
  }

  async sortTheChapterSimulators(OrderData: UpdateOrderChapterSimulatorsInput) {
    const chapter = await this.getChapter_ById(OrderData.chapterId);
    const simulators = await this.simulatorsService.getSimulators_ByChapterId(
      chapter.id,
    );
    const simulators_update = simulators
      .map((simulator) => {
        const updatedSimulator = OrderData.simulators.find(
          (val) => val.id === simulator.id,
        );
        if (
          updatedSimulator &&
          updatedSimulator.simulatorNumber !== simulator.simulatorNumber
        ) {
          return {
            ...simulator,
            simulatorNumber: updatedSimulator.simulatorNumber,
          };
        }
        return undefined;
      })
      .filter((simulator) => simulator !== undefined);
    if (simulators_update.length > 0) {
      const update_result = await this.simulatorsService.saveNumberSimulator(
        simulators_update,
      );
      if (update_result.length === 0)
        throw new Error('No se pudo actualizar el número de capítulo');
    }
    return true;
  }
  async updateChapterOpen(data: UpdateChapterOpenInput): Promise<boolean> {
    const chapter_update = this.chapterRepository.create(data);
    const res = await this.chapterRepository.save(chapter_update);
    if (!res) throw new Error('No se pudo actualizar el capítulo');
    const chapter = await this.getChapter_ById(data.id);
    return chapter.open;
  }

  async getCantidadDeCapitulosPorVersion_ByVersionId(
    versionId: string,
  ): Promise<number> {
    const query = `
      SELECT COUNT(*) as count FROM chapter as ch
      where ch.versionId=? and ch.deletedAt is null
    `;
    const values = [versionId];
    const res = await this.chapterRepository.query(query, values);
    return res[0].count;
  }

  async copyBookVersionChapters(
    sourceVersionId: string,
    destinationVersionId: string,
  ): Promise<boolean> {
    const capitulos_para_insertar = [];
    const simuladores_para_insertar = [];
    const hojas_para_insertar = [];
    const formulas_para_insertar = [];

    const capitulos_originales = await this.getChapters_ByVersionId(
      sourceVersionId,
    );
    for (const [index_c, cap_original] of capitulos_originales.entries()) {
      const cap_nuevo = await this.capituloNuevo(
        cap_original,
        destinationVersionId,
      );
      capitulos_para_insertar.push(cap_nuevo);

      const simuladores_originales =
        await this.simulatorsService.getSimulators_ByChapterId(cap_original.id);

      for (const [index_s, sim_original] of simuladores_originales.entries()) {
        const sim_nuevo = await this.simuladorNuevo(sim_original, cap_nuevo.id);
        simuladores_para_insertar.push(sim_nuevo);
        const sheets_originales =
          await this.sheetsService.getSheets_BySimulatorId2(sim_original.id);
        console.log('sheets_originales');
        console.log(sheets_originales);

        for (const [index_sh, sheet_original] of sheets_originales.entries()) {
          const sheet_nueva = await this.sheetNueva(
            sheet_original,
            sim_nuevo.id,
          );
          hojas_para_insertar.push(sheet_nueva);
        }
        const formulas_originales =
          await this.formulasService.getFormulas_BySimulatorId(sim_original.id);
        for (const [
          index_f,
          formula_original,
        ] of formulas_originales.entries()) {
          const formula_nueva = await this.formulaNueva(
            formula_original,
            sim_nuevo.id,
          );
          formulas_para_insertar.push(formula_nueva);
        }
      }
    }
    // console.log('capitulos_para_insertar');
    // console.log(capitulos_para_insertar.length);
    const res_capitulos = await this.chapterRepository.save(
      capitulos_para_insertar,
    );
    // console.log('capitulos_insertados');
    // console.log(res_capitulos.length);

    // console.log('simuladores_para_insertar');
    // console.log(simuladores_para_insertar.length);
    const res_simuladores =
      await this.simulatorsService.simulatorRepository.save(
        simuladores_para_insertar,
      );
    // console.log('simuladores_insertados');
    // console.log(res_simuladores.length);

    // console.log('hojas_para_insertar');
    // console.log(hojas_para_insertar);
    const res_hojas = await this.sheetsService.sheetRepository.save(
      hojas_para_insertar,
    );
    // console.log('hojas_insertadas');
    // console.log(res_hojas);

    // console.log('formulas_para_insertar');
    // console.log(formulas_para_insertar.length);
    const res_formulas = await this.formulasService.formulaRepository.save(
      formulas_para_insertar,
    );
    // console.log('formulas_insertadas');
    // console.log(res_formulas.length);

    return true;
  }

  async capituloNuevo(
    cap_original: Chapter,
    destinationVersionId: string,
  ): Promise<Chapter> {
    const copia_capitulo = { ...cap_original };
    delete copia_capitulo.createdAt;
    delete copia_capitulo.updatedAt;
    delete copia_capitulo.deletedAt;
    copia_capitulo.id = uuidv4();
    copia_capitulo.version = { id: destinationVersionId } as Version;
    return this.chapterRepository.create(copia_capitulo);
  }

  async simuladorNuevo(
    sim_original: Simulator,
    cap_nuevoId: string,
  ): Promise<Simulator> {
    const copia_simulador = { ...sim_original };
    delete copia_simulador.createdAt;
    delete copia_simulador.updatedAt;
    delete copia_simulador.deletedAt;
    // copia_simulador.name=copia_simulador.name+'copia';
    copia_simulador.id = uuidv4();
    copia_simulador.chapter = { id: cap_nuevoId } as Chapter;
    return this.simulatorsService.simulatorRepository.create(copia_simulador);
  }

  async sheetNueva(sheet_original: Sheet, sim_nuevoId: string): Promise<Sheet> {
    const copia_sheet = { ...sheet_original };
    delete copia_sheet.createdAt;
    delete copia_sheet.updatedAt;
    delete copia_sheet.deletedAt;
    copia_sheet.id = uuidv4();
    copia_sheet.simulator = { id: sim_nuevoId } as Simulator;
    return this.sheetsService.sheetRepository.create(copia_sheet);
  }

  async formulaNueva(
    formula_original: Formula,
    sim_nuevoId: string,
  ): Promise<Formula> {
    const copia_formula = { ...formula_original };
    delete copia_formula.createdAt;
    delete copia_formula.updatedAt;
    delete copia_formula.deletedAt;
    copia_formula.id = uuidv4();
    copia_formula.simulator = { id: sim_nuevoId } as Simulator;
    return this.formulasService.formulaRepository.create(copia_formula);
  }

  // Metodos de celedonio para importar simuladores

  async capituloNuevoConNumeroCapitulo(
    cap_original: Chapter,
    chapterNumber,
    destinationVersionId: string,
  ): Promise<Chapter> {
    const copia_capitulo = { ...cap_original };
    delete copia_capitulo.createdAt;
    delete copia_capitulo.updatedAt;
    delete copia_capitulo.deletedAt;
    copia_capitulo.id = uuidv4();
    copia_capitulo.chapterNumber = chapterNumber;
    copia_capitulo.version = { id: destinationVersionId } as Version;
    return this.chapterRepository.create(copia_capitulo);
  }
  async checkDuplicateChaper(
    chapter: Chapter,
    version: Version,
  ): Promise<boolean> {
    const duplicate = await this.chapterRepository
      .createQueryBuilder('chapter')
      .leftJoinAndSelect('chapter.version', 'version')
      .leftJoinAndSelect('chapter.simulators', 'simulator')
      .where('chapter.name = :chapterName', { chapterName: chapter.name })
      .andWhere('version.id = :versionId', { versionId: version.id })
      .getOne();
    // console.log('Duplicado so: ', duplicate);
    // if (duplicate) throw new Error(`${chapter.name} ya existe`);
    if (duplicate) return true;
    return false;
  }
  async checkDuplicateSimulatorInChapter(
    // version: Version,
    chapter: Chapter,
    simulator: Simulator,
  ): Promise<boolean> {
    const duplicate = await this.chapterRepository
      .createQueryBuilder('chapter')
      .leftJoinAndSelect('chapter.version', 'version')
      .leftJoinAndSelect('chapter.simulators', 'simulator')
      .where('chapter.id = :chapterId', { chapterId: chapter.id })
      .andWhere('simulator.name = :simulatorName', {
        simulatorName: simulator.name,
      })
      // .andWhere('version.id = :versionId', { versionId: version.id })
      .getOne();
    // console.log('Duplicado so: ', duplicate);
    // if (duplicate) throw new Error(`${chapter.name} ya existe`);
    if (duplicate) return true;
    return false;
  }

  async simuladorNuevoWithSimulatorNumber(
    sim_original: Simulator,
    simulatorNumber: number,
    cap_nuevoId: string,
  ): Promise<Simulator> {
    const copia_simulador = { ...sim_original };
    delete copia_simulador.createdAt;
    delete copia_simulador.updatedAt;
    delete copia_simulador.deletedAt;
    // copia_simulador.name=copia_simulador.name+'copia';
    copia_simulador.id = uuidv4();
    copia_simulador.simulatorNumber = simulatorNumber; // Encremetar el numero del simulador
    copia_simulador.chapter = { id: cap_nuevoId } as Chapter;
    return this.simulatorsService.simulatorRepository.create(copia_simulador);
  }

  async saveChapter(chapter) {
    return await this.chapterRepository.save(chapter);
  }
}
