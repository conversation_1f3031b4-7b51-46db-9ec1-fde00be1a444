import { Injectable } from '@nestjs/common';
import { CreateCommentInput } from './dto/create-comment.input';
import { UpdateCommentInput } from './dto/update-comment.input';
import { Comment } from './entities/comment.entity';
import { IsNull, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CommentInput } from '../peoples/dto/comment.input';

@Injectable()
export class CommentsService {
  constructor(
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
  ) {}

  async create(createCommentInput: CommentInput) {
    return await this.commentRepository.save({
      id: uuidv4(),
      ...createCommentInput,
    });
  }

  async findAll(): Promise<Comment[]> {
    return await this.commentRepository.find({
      where: {
        deletedAt: IsNull(),
      },
      order: {
        deletedAt: 'DESC',
      },
    });
  }

  async findOne(id: string): Promise<Comment> {
    return await this.commentRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });
  }

  async remove(id: string): Promise<Boolean> {
    return (await this.commentRepository.delete({ id })).affected === 1;
  }
}
