import { ObjectType, Field, Int } from '@nestjs/graphql';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { IsString, IsEmail, IsOptional } from 'class-validator';

@Entity()
@ObjectType()
export class Comment extends AuditableEntity {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Name must be a string' })
  name?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsEmail({}, { message: 'Invalid email format' })
  mail?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'RegionCode must be a string' })
  regionCode?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Cellphone must be a string' })
  cellphone?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Affair must be a string' })
  affair?: string;

  @Column({ default: null })
  @Field({ nullable: true })
  @IsOptional()
  @IsString({ message: 'Message must be a string' })
  message?: string;
}
