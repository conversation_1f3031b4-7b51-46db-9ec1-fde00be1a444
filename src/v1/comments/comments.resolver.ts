import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { CommentsService } from './comments.service';
import { Comment } from './entities/comment.entity';
import { CreateCommentInput } from './dto/create-comment.input';
import { UpdateCommentInput } from './dto/update-comment.input';
import { UseGuards } from '@nestjs/common';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';

@Resolver(() => Comment)
export class CommentsResolver {
  constructor(private readonly commentsService: CommentsService) {}

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => [Comment], { name: 'comments' })
  findAll() {
    return this.commentsService.findAll();
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Comment, { name: 'findOnecomment' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.commentsService.findOne(id);
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => Comment, { name: 'removeComment' })
  removeComment(@Args('id', { type: () => String }) id: string) {
    return this.commentsService.remove(id);
  }
}
