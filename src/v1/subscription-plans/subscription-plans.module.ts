import { Module } from '@nestjs/common';
import { SubscriptionPlansResolver } from './subscription-plans.resolver';
import { SubscriptionPlansService } from './subscription-plans.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionPlan } from './entities/subscription-plan.entity';

@Module({
  exports: [SubscriptionPlansService],
  imports: [TypeOrmModule.forFeature([SubscriptionPlan])],
  providers: [SubscriptionPlansResolver, SubscriptionPlansService],
})
export class SubscriptionPlansModule {}
