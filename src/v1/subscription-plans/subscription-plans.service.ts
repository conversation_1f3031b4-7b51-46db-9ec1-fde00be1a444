import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { IsNull, Repository } from 'typeorm';
import {
  InsertSubscriptionPlanInput,
  UpdateSubscriptionPlanInput,
} from './dto/subscription-plan.input';

@Injectable()
export class SubscriptionPlansService {
  constructor(
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
  ) {}

  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return await this.subscriptionPlanRepository.find({
      where: { deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });
  }

  async getSubscriptionPlan_ById(id: string): Promise<SubscriptionPlan> {
    // console.log('dato: ', id);
    const subscripion_plan = await this.subscriptionPlanRepository.findOne({
      where: { id, deletedAt: <PERSON>Null() },
    });
    if (!subscripion_plan) throw new Error('Plan de subscripción no existe');
    return subscripion_plan;
  }

  async insertSubscriptionPlan(
    data: InsertSubscriptionPlanInput,
  ): Promise<SubscriptionPlan> {
    const subscription_plan = this.subscriptionPlanRepository.create(data);
    return await this.subscriptionPlanRepository.save(subscription_plan);
  }

  async updateSubscriptionPlan(
    data: UpdateSubscriptionPlanInput,
  ): Promise<SubscriptionPlan> {
    const sp = await this.getSubscriptionPlan_ById(data.id);
    const subscription_plan = this.subscriptionPlanRepository.create(data);
    return await this.subscriptionPlanRepository.save(subscription_plan);
  }

  async deleteSubscriptionPlan_ById(id: string): Promise<boolean> {
    const sp = await this.getSubscriptionPlan_ById(id);
    const delete_result = await this.subscriptionPlanRepository.update(
      { id, deletedAt: IsNull() },
      { deletedAt: new Date() },
    );
    if (delete_result.affected === 0)
      throw new Error('No se pudo eliminar el plan de subscripción');
    return true;
  }
}
