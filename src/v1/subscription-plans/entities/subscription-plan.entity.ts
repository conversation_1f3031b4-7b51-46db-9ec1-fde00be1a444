import { ObjectType, Field, Int } from '@nestjs/graphql';
import {
  BeforeInsert,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';

//entities
import { AuditableEntity } from 'src/config/auditable-entity.config';
import { IsUUID, IsString, IsInt, Min } from 'class-validator';
import { VersionSubscriptionPlan } from 'src/v1/version-subscription-plans/entities/version-subscription-plan.entity';

@Entity()
@ObjectType()
export class SubscriptionPlan extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Column()
  @Field({})
  name: string;

  @IsInt()
  @Min(0, { message: 'Duration days must be at least 0' })
  @Column({ type: 'integer', default: 0 })
  @Field(() => Int)
  duration_days: number;

  @OneToMany(
    () => VersionSubscriptionPlan,
    (versionSubscriptionPlan) => versionSubscriptionPlan.subscriptionPlan,
  )
  versionSubscriptionPlans: VersionSubscriptionPlan[];
}
