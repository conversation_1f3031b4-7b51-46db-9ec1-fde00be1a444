import { Field, InputType } from '@nestjs/graphql';
import { IsUUID, IsString, IsInt, Min } from 'class-validator';

@InputType()
export class InsertSubscriptionPlanInput {
  @IsString()
  @Field()
  name: string;

  @IsInt()
  @Min(0, { message: 'Duration days must be at least 0' })
  @Field()
  duration_days: number;
}

@InputType()
export class UpdateSubscriptionPlanInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field()
  id: string;

  @IsString()
  @Field()
  name: string;

  @IsInt()
  @Min(0, { message: 'Duration days must be at least 0' })
  @Field()
  duration_days: number;
}
