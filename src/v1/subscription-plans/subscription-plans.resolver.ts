import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { SubscriptionPlansService } from './subscription-plans.service';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import {
  InsertSubscriptionPlanInput,
  UpdateSubscriptionPlanInput,
} from './dto/subscription-plan.input';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/jwt/guards/auth.guard';
// import { JwtAuthAdminGuard } from 'src/jwt/guards/authAdmin.guard';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';

@Resolver()
export class SubscriptionPlansResolver {
  constructor(
    private readonly subscription_planService: SubscriptionPlansService,
  ) {}

  @Query(() => [SubscriptionPlan])
  public async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return await this.subscription_planService.getSubscriptionPlans();
  }

  @Query((returns) => SubscriptionPlan)
  async getSubscriptionPlan_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<SubscriptionPlan> {
    return await this.subscription_planService.getSubscriptionPlan_ById(id);
  }

  @Mutation((returns) => SubscriptionPlan)
  async insertSubscriptionPlan(
    @Args('subscription_planData')
    subscription_planData: InsertSubscriptionPlanInput,
  ): Promise<SubscriptionPlan> {
    console.log(subscription_planData);
    return await this.subscription_planService.insertSubscriptionPlan(
      subscription_planData,
    );
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation((returns) => SubscriptionPlan)
  async updateSubscriptionPlan(
    @Args('subscription_planData')
    subscription_planData: UpdateSubscriptionPlanInput,
  ): Promise<SubscriptionPlan> {
    // console.log(subscription_planData);
    return await this.subscription_planService.updateSubscriptionPlan(
      subscription_planData,
    );
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Mutation((returns) => Boolean)
  async deleteSubscriptionPlan_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<boolean> {
    return await this.subscription_planService.deleteSubscriptionPlan_ById(id);
  }
}
