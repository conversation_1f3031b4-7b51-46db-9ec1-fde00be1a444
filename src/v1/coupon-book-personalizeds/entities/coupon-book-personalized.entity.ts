import { Entity, Column, PrimaryGeneratedC<PERSON>umn, ManyToOne } from 'typeorm';
import { Book } from 'src/v1/books/entities/book.entity';
import { Coupon } from 'src/v1/coupons/entities/coupon.entity';

@Entity()
export class CouponBookPersonalized {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Book, book => book.couponBookPersonaizeds)
  book: Book;

  @ManyToOne(() => Coupon, coupon => coupon.couponBookPersonalizeds)
  coupon: Coupon;
}