import { Module } from '@nestjs/common';
import { CouponBookPersonalizedsService } from './coupon-book-personalizeds.service';
import { CouponBookPersonalizedsResolver } from './coupon-book-personalizeds.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CouponBookPersonalized } from './entities/coupon-book-personalized.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CouponBookPersonalized])],
  providers: [CouponBookPersonalizedsService, CouponBookPersonalizedsResolver]
})
export class CouponBookPersonalizedsModule {}
