import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CouponBookPersonalized } from './entities/coupon-book-personalized.entity';
import { Repository } from 'typeorm';

@Injectable()
export class CouponBookPersonalizedsService {
    constructor(
        @InjectRepository(CouponBookPersonalized)
        private couponBookPersonalizedRepository: Repository<CouponBookPersonalized>,
      ) {}
}
