import { Resolver } from '@nestjs/graphql';
import { CouponBookPersonalized } from './entities/coupon-book-personalized.entity';
import { CouponBookPersonalizedsService } from './coupon-book-personalizeds.service';

@Resolver(() => CouponBookPersonalized)
export class CouponBookPersonalizedsResolver {
    constructor(
        private readonly couponBookPersonalizedsService: CouponBookPersonalizedsService
    ){}
}
