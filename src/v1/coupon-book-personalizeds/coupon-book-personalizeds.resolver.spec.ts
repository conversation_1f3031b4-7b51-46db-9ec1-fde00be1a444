import { Test, TestingModule } from '@nestjs/testing';
import { CouponBookPersonalizedsResolver } from './coupon-book-personalizeds.resolver';

describe('CouponBookPersonalizedsResolver', () => {
  let resolver: CouponBookPersonalizedsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CouponBookPersonalizedsResolver],
    }).compile();

    resolver = module.get<CouponBookPersonalizedsResolver>(CouponBookPersonalizedsResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
