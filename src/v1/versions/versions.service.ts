import { Injectable, NotFoundException } from '@nestjs/common';
import { UpdateVersionInput } from './dto/update-version.input';
import { Version } from './entities/version.entity';
import {
  Connection,
  DeleteResult,
  IsNull,
  Repository,
  getManager,
} from 'typeorm';
import { InjectConnection, InjectRepository } from '@nestjs/typeorm';
import { InsertVersionInput } from './dto/create-version.input';
import { v4 as uuidv4 } from 'uuid';
import { Upload } from 'graphql-upload/Upload.js';
import * as fs from 'fs';
import {
  FileWithPublicUrl_Output,
  LastVersionAccessible_Output,
  VersionOutput2,
  VersionWithNroPurchases_Output,
} from './dto/version.output';
import { ChaptersService } from '../chapters/chapters.service';
import { SimulatorsService } from '../simulators/simulators.service';
import { EnumPaymentStatus } from '../shopping/entities/shopping.entity';
import { Operaciones } from 'src/@core/utils/operations';
import { AttachedFilesService } from '../attached-files/attached-files.service';
import { EnumNombreTabla_AttachedFile } from '../attached-files/entities/attached-file.entity';
import { ISubcriberForBook, PeoplesService } from '../peoples/peoples.service';
import { People } from '../peoples/entities/people.entity';
import { AttachmentInput } from 'src/mail/dto/attachments-input';
import { IDataEmailNewVersionBook, MailService } from 'src/mail/mail.service';
import {
  IDataVersionBookPurchase,
  ProductsService,
} from '../products/products.service';
import { constantsUrls } from 'src/common/constants';

type AnyObject = { [key: string]: any };
@Injectable()
export class VersionsService {
  opAux = Operaciones;
  constructor(
    @InjectRepository(Version) private versionRepository: Repository<Version>,
    @InjectConnection() private readonly connection: Connection,
    private readonly chaptersService: ChaptersService,
    private readonly simulatorsService: SimulatorsService,
    private readonly attached_filesService: AttachedFilesService,
    private peopleService: PeoplesService,
    private mailService: MailService,
    private productsService: ProductsService,
  ) {}

  booksFormat = [
    'epub',
    'pdf',
    'mobi',
    'azw',
    'txt',
    'html',
    'fb2',
    'djvu',
    'cbz',
    'cbr',
    'lit',
    'iba',
    'kf8',
    'lrf',
    'pdb',
    'azw3',
    'opf',
    'dnl',
  ];

  coverFormats = [
    'JPEG',
    'JPG',
    'PNG',
    'GIF',
    'BMP',
    'SVG',
    'WebP',
    'ICO',
    'TIFF',
    'APNG',
    'JP2',
    'HEIC',
  ];

  async findOne(id: string): Promise<Version> {
    const version = await this.versionRepository.findOne({
      where: { id, deletedAt: IsNull() },
    });
    if (!version) throw new NotFoundException('La versión no existe.');
    version.coverPath = process.env.HOST_ADMIN + '/' + version.coverPath;
    version.bookPath = process.env.HOST_ADMIN + '/' + version.bookPath;
    return version;
  }

  async getActiveVersionBook_ById(id: string): Promise<Version> {
    const res = await this.findOne(id);
    if (res.versionActive === false)
      throw new Error('La versión de este libro no es la más reciente.');
    return res;
  }

  async findOneActive(id: string): Promise<Version> {
    return await this.versionRepository.findOne({
      where: { book: { id }, deletedAt: IsNull(), versionActive: true },
    });
  }

  async findAll(id: string): Promise<Version[]> {
    const res = await this.versionRepository.find({
      where: { book: { id }, deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });

    return res.map((version) => ({
      ...version,
      coverPath: process.env.HOST_ADMIN + '/' + version.coverPath,
      bookPath: process.env.HOST_ADMIN + '/' + version.bookPath,
    })) as Version[];
  }

  async update(
    id: string,
    updateVersionInput: UpdateVersionInput,
  ): Promise<boolean> {
    const result = await this.versionRepository.update(
      { id, deletedAt: IsNull() },
      updateVersionInput,
    );
    return result.affected === 1;
  }

  async resetActiveVersion(
    id: string,
    isActive: boolean,
    bookId: string,
  ): Promise<boolean> {
    if (!isActive) return false;

    const versionsData = await this.versionRepository.find({
      where: { book: { id: bookId }, deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });
    versionsData.forEach(async (data) => {
      if (data.id !== id) {
        await this.versionRepository.update(
          { id: data.id, deletedAt: IsNull() },
          { versionActive: false },
        );
      }
    });

    return true;
  }

  async getVersions(): Promise<Version[]> {
    return await this.versionRepository.find({
      where: { deletedAt: IsNull() },
    });
  }

  async getVersions_ByBookId(bookId: string): Promise<Version[]> {
    const res = await this.versionRepository.find({
      where: { deletedAt: IsNull(), book: { id: bookId } },
    });
    return res.map((version) => ({
      ...version,
      coverPath: process.env.HOST_ADMIN + '/' + version.coverPath,
    })) as Version[];
  }

  async getVersions_ByBookId_OrderAscByCreatedAt(
    bookId: string,
  ): Promise<Version[]> {
    const res = await this.versionRepository.find({
      where: { deletedAt: IsNull(), book: { id: bookId } },
      order: { createdAt: 'ASC' },
    });
    return res.map((version) => ({
      ...version,
      coverPath: process.env.HOST_ADMIN + '/' + version.coverPath,
    })) as Version[];
  }

  async getVersionsWithNroPurchases_ByBookId(
    bookId: string,
  ): Promise<VersionWithNroPurchases_Output[]> {
    const res = await this.versionRepository
      .createQueryBuilder('version')
      .leftJoin('version.product', 'product')
      .leftJoin(
        'product.shopping',
        'shopping',
        'shopping.paymentStatus = :paymentStatus',
        { paymentStatus: EnumPaymentStatus.PAGO_CONFIRMADO },
      )
      .select('version.*')
      .addSelect('COUNT(shopping.id)', 'nroPurchases')
      .where('version.deletedAt IS NULL')
      .andWhere('version.bookId = :bookId', { bookId })
      .groupBy('version.id')
      .getRawMany();
    console.log('getVersionsWithNroPurchases_ByBookId');
    console.log(res);

    let res_o: VersionWithNroPurchases_Output[] = [];
    for (const [i, v] of res.entries()) {
      let v_: any = v;
      v_.coverPath = process.env.HOST_ADMIN + '/' + v_.coverPath;
      res_o.push(v_);
    }
    return res_o;
  }

  async insertVersion(
    coverFile: Upload,
    versionData: InsertVersionInput,
    books_ids: string[],
  ) {
    // if (
    //   !this.booksFormat.some(
    //     (format) =>
    //       format.toLowerCase() ==
    //       bookFile.filename.split('.').pop().toLowerCase(),
    //   )
    // ) {
    //   throw new Error(
    //     `the file format of the book variable must be one of the sgt list: ${this.booksFormat.join(
    //       ', ',
    //     )}`,
    //   );
    // }

    if (
      !this.coverFormats.some(
        (format) =>
          format.toLowerCase() ==
          coverFile.filename.split('.').pop().toLowerCase(),
      )
    ) {
      throw new Error(
        `the file format of the cover variable must be one of the sgt list: ${this.coverFormats.join(
          ', ',
        )}`,
      );
    }
    const newVersion = this.versionRepository.create(versionData);

    // newVersion.bookPath = await this.SaveFile(bookFile, 'books/');
    newVersion.bookPath = '';
    newVersion.coverPath = (
      await this.SaveFile(coverFile, 'public/images/covers/')
    ).replaceAll('public/', '');
    console.log('new version');
    console.log(newVersion);
    const versionInserted: Version = await this.versionRepository.save(
      newVersion,
    );
    await this.transactionFunction(async (queryRunner) => {
      // const versionInserted= await queryRunner.manager.save(versionData);
      this.resetActiveVersion(
        versionInserted.id,
        versionInserted.versionActive,
        versionInserted.bookId,
      );
    });
    const version = await this.findOne(versionInserted.id);
    console.log('version');
    console.log(version);

    console.log('books ids');
    console.log(books_ids);
    const libros_guardados =
      await this.attached_filesService.saveArchivosAdjuntos(
        EnumNombreTabla_AttachedFile.VERSIONES,
        version.id,
        books_ids,
      );

    console.log('libros guardados');
    console.log(libros_guardados);

    return version;
  }

  async updateVersion(versionData: UpdateVersionInput, books_ids: string[]) {
    // const bookFile = (await versionData?.bookFile) || null;
    const coverFile = (await versionData?.coverFile) || null;
    const id = versionData.id;
    let newPathBook = '';
    let newPathCover = '';
    if (id) {
      const versionDataOld = await this.versionRepository.findOne({
        where: { id, deletedAt: IsNull() },
      });

      // if (bookFile) {
      //   newPathBook = await this.SaveFile(bookFile, 'books/');
      //   await this.deleteFile(versionDataOld.bookPath);
      // }

      if (coverFile) {
        newPathCover = (
          await this.SaveFile(coverFile, 'public/images/covers/')
        ).replaceAll('public/', '');
        await this.deleteFile('public/' + versionDataOld.coverPath);
      }
    }

    const newVersion = this.versionRepository.create(versionData);
    newVersion.bookPath = newPathBook || newVersion.bookPath;
    newVersion.coverPath = newPathCover || newVersion.coverPath;
    const result: Version = await this.versionRepository.save(newVersion);

    await this.transactionFunction(async (queryRunner) => {
      this.resetActiveVersion(
        versionData.id,
        versionData.versionActive,
        versionData.bookId,
      );
    });
    const version = await this.findOne(result.id);

    console.log('books ids');
    console.log(books_ids);
    const libros_guardados =
      await this.attached_filesService.saveArchivosAdjuntos(
        EnumNombreTabla_AttachedFile.VERSIONES,
        version.id,
        books_ids,
      );

    console.log('libros guardados');
    console.log(libros_guardados);

    return version;
  }

  async deleteVersion_ById(id: string): Promise<boolean> {
    const version = await this.findOne(id);
    // let deleteResult: DeleteResult = await this.versionRepository.delete(id);
    // if (deleteResult.affected == 0) {
    //   throw new Error('No se pudo eliminar la versión.');
    // }
    version.deletedAt = new Date();
    await this.versionRepository.save(version);
    return true;
  }

  async SaveFile(file: Upload, path: string): Promise<string> {
    if (!file) throw new Error('no file provided.');

    const uniqueFileName = uuidv4();
    const fileExtension = file.filename.split('.').pop() || 'txt';
    const uploadPath = `${path}${uniqueFileName}.${fileExtension}`;

    return new Promise((resolve, reject) => {
      const writeStream = fs.createWriteStream(uploadPath);
      file
        .createReadStream()
        .pipe(writeStream)
        .on('finish', () => resolve(uploadPath))
        .on('error', reject);
    });
  }

  transactionFunction(callback): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      const queryRunner = this.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        await callback(queryRunner);

        await queryRunner.commitTransaction();
        resolve(true);
        return true;
      } catch (e) {
        await queryRunner.rollbackTransaction();
        throw e;
        reject(e);
      } finally {
        await queryRunner.release();
      }
    });
  }

  async deleteFile(filePath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.unlink(filePath, (error) => {
        if (error) {
          reject(new Error(`Error deleting file: ${error.message}`));
          return;
        }
        resolve(true);
      });
    });
  }

  async getMyVersionsBooks_ByPeopleId(
    peopleId: string,
  ): Promise<VersionOutput2[]> {
    let res = await this.versionRepository
      .createQueryBuilder('version')
      .innerJoinAndSelect('version.product', 'product')
      .innerJoinAndSelect('version.chapter', 'chapter')
      .innerJoinAndSelect('version.book', 'book')
      .innerJoinAndSelect('product.shopping', 'shopping')
      .where('product.dateStart <= :date', { date: new Date() })
      .andWhere('product.dateEnd >= :date', { date: new Date() })
      .andWhere('shopping.peopleId = :peopleId', { peopleId })
      .getMany();

    let res_o: VersionOutput2[] = [];
    for (const [i, v] of res.entries()) {
      let v_: any = v;
      v_.chapterCount =
        await this.chaptersService.getCantidadDeCapitulosPorVersion_ByVersionId(
          v_.id,
        );
      v_.simulatorCount =
        await this.simulatorsService.getCantidadDeSimuladoresPorVersion_ByVersionId(
          v_.id,
        );
      res_o.push(v_);
    }

    return res_o;
  }
  // async getMyVersionsBooks_ByPeopleId(peopleId: string): Promise<VersionOutput2[]> {
  //   let res:any= await this.versionRepository.createQueryBuilder('version')
  //    .innerJoinAndSelect('version.product', 'product')
  //    .innerJoinAndSelect('version.book', 'book')
  //    .innerJoinAndSelect('product.shopping', 'shopping')
  //    .addSelect(subQuery => {
  //     return subQuery
  //         .select('COUNT(chapter.id)', 'chapterCount')
  //         .from('chapter', 'chapter') // Asegúrate de que 'chapter' sea el alias correcto y el nombre de la tabla sea el adecuado
  //         .where('chapter.versionId = version.id') // Asegúrate de que la clave foránea sea correcta
  //         .andWhere('chapter.deletedAt IS NULL');
  //     }, 'version_chapterCount')
  //       // Añadir una subconsulta para contar los simuladores a través de los capítulos
  //     .addSelect(subQuery => {
  //       return subQuery
  //           .select('COUNT(DISTINCT simulator.id)', 'simulatorCount')
  //           .from('simulator', 'simulator')
  //           .innerJoin('simulator.chapter', 'chapter')
  //           // Asumiendo que 'simulator' se relaciona con 'chapter' y tiene una columna 'deletedAt'
  //           .where('chapter.versionId = version.id')
  //           .andWhere('simulator.deletedAt IS NULL');
  //     }, 'version_simulatorCount')
  //    .where('product.dateStart <= :date', { date: new Date() })
  //     .andWhere('product.dateEnd >= :date', { date: new Date() })
  //     .andWhere('shopping.peopleId = :peopleId', { peopleId })
  //     .getRawMany();
  //     console.log("get my version books by people id")
  //     console.log(res);
  //     console.log("resul0: " + res[0].version_chapterCount)
  //     console.log("fin")
  //     res=this.transformArrayOfObjects(res);
  //   return res;
  //   // return [];
  // }

  //  transformKey(key: string): string {
  //   // Divide la clave en partes basadas en el guion bajo (_)
  //   const parts = key.split('_');
  //   // Elimina la primera parte (palabra) y el guion bajo
  //   parts.shift();
  //   // Une las partes restantes con un guion bajo, si hay más de una parte
  //   return parts.join('_');
  // }

  //  transformObjectKeys(obj: AnyObject): AnyObject {
  //   const transformedObject: AnyObject = {};

  //   Object.keys(obj).forEach(key => {
  //     // Aplica la transformación a cada clave
  //     const newKey = this.transformKey(key);
  //     // Asigna el valor original a la nueva clave en el objeto transformado
  //     transformedObject[newKey] = obj[key];
  //   });

  //   return transformedObject;
  // }

  //  transformArrayOfObjects(array: AnyObject[]): AnyObject[] {
  //   return array.map(obj => this.transformObjectKeys(obj));
  // }

  async getMyVersionBook_ByVersionBookId_PeopleId(
    versionId: string,
    peopleId: string,
  ): Promise<Version | null> {
    const res = await this.versionRepository
      .createQueryBuilder('version')
      .innerJoinAndSelect('version.product', 'product')
      .innerJoinAndSelect('version.book', 'book')
      .innerJoinAndSelect('product.shopping', 'shopping')
      .where('version.id = :versionId ', { versionId: versionId })
      .andWhere('product.dateStart <= :date', { date: new Date() })
      .andWhere('product.dateEnd >= :date', { date: new Date() })
      .andWhere('shopping.peopleId = :peopleId', { peopleId })
      .getOne();
    // console.log(res);
    return res;
  }

  async sendVersionDocumentsToSubscribers(versionId: string): Promise<boolean> {
    const version = await this.findOne(versionId).catch((e) => {
      throw new Error('La versión no existe');
    });
    const personas_subcribersForBook: ISubcriberForBook[] =
      await this.peopleService.getSubcribersForBook(version.bookId);

    for (const p_sub of personas_subcribersForBook) {
      console.log('p_sub');
      console.log(p_sub);
      console.log('version');
      console.log(version.id);
      console.log('name table');
      console.log(EnumNombreTabla_AttachedFile.VERSIONES);

      const docs_version =
        await this.attached_filesService.getArchivosAdjuntos_Registro(
          EnumNombreTabla_AttachedFile.VERSIONES,
          version.id,
        );
      console.log('docs_version');
      console.log(docs_version);

      const attachments: AttachmentInput[] = await Promise.all(
        docs_version.map(async (doc) => {
          const file = await doc.file;
          return {
            filename: file.originalname,
            path: './files/archivos/' + file.filename,
          } as AttachmentInput;
        }),
      );

      const data: IDataEmailNewVersionBook = {
        people_name: p_sub.people_name,
        people_email: p_sub.people_email,
        book_name: p_sub.book_title,
        book_version: version.version, //ojo siempre enviar la dato de la nueva version
        attachments: attachments,
      };
      console.log('data');
      console.log(data);
      const res = await this.mailService.sendEmail_newVersionBook(data);
      console.log('res email');
      console.log(res);
    }

    const setFalseIsBooksPendingSend = await this.setFalseIsBooksPendingSend(
      versionId,
    );
    return true;
  }

  async setFalseIsBooksPendingSend(version_id: string): Promise<boolean> {
    const v = new Version();
    v.id = version_id;
    v.is_books_pending_send = false;
    const res = await this.versionRepository.save(v);
    return true;
  }

  async getLastAccessibleBookVersion(
    bookId: string,
    people_id: string,
  ): Promise<LastVersionAccessible_Output | null> {
    const versions_book = await this.getVersions_ByBookId_OrderAscByCreatedAt(
      bookId,
    );
    //en orden descendente el primero es el ultimo que compró
    const compras_libro_persona: IDataVersionBookPurchase[] =
      await this.productsService.getVersionsBookPurchases_ByPeopleId_BookId(
        people_id,
        bookId,
      );
    if (compras_libro_persona.length == 0) {
      // throw new Error('No ha comprado ninguna versión de este libro');
      return null;
    }
    const last_version_purchased: IDataVersionBookPurchase =
      compras_libro_persona[0];
    let last_version_accessible: Version = null;
    for (const [index, v] of versions_book.entries()) {
      if (v.createdAt <= last_version_purchased.product.dateEnd) {
        last_version_accessible = v;
      } else {
        break;
      }
    }
    const files: FileWithPublicUrl_Output[] = [];
    const files_attached =
      await this.attached_filesService.getArchivosAdjuntos_Registro(
        EnumNombreTabla_AttachedFile.VERSIONES,
        last_version_accessible.id,
      );
    for (const f of files_attached) {
      const file = await f.file;
      files.push({
        ...file,
        publicUrl: constantsUrls.urlArchivos + file.filename,
      } as FileWithPublicUrl_Output);
    }

    const res = {
      ...last_version_accessible,
      files: files,
    } as LastVersionAccessible_Output;
    return res;
  }
}
