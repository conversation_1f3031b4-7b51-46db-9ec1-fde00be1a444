import { InputType, Int, Field } from '@nestjs/graphql';
import { IsBoolean, IsInt, IsNumber, IsString } from 'class-validator';

@InputType()
export class InsertVersionInput {

  @IsString()
  @Field({ nullable: true })
  coverPath: string;

  @IsString()
  @Field({ nullable: true })
  bookPath: string;

  @IsBoolean({ message: 'VersionActive must be a boolean value' })
  @Field(() => Boolean, )
  versionActive: boolean;

  @IsNumber({}, { message: 'String must be a string' })
  @Field(() => String, { nullable: true })
  version: string;

  @IsString()
  @Field(() => String)
  bookId: string; 

  @IsBoolean()
  @Field(() => Boolean,)
  is_books_pending_send: boolean;
}
