import { InputType, Field, Int, Float } from '@nestjs/graphql';
import {
  IsString,
  IsUUID,
  IsNumber,
  IsBoolean,
  IsOptional,
} from 'class-validator';
import { Upload } from 'graphql-upload/Upload.js';
import * as GraphQLUpload from 'graphql-upload/GraphQLUpload.js';

@InputType()
export class UpdateVersionInput {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @Field(() => String, { nullable: true })
  id?: string;

  @IsString()
  @Field({ nullable: true })
  coverPath: string;

  @IsString()
  @Field({ nullable: true })
  bookPath: string;

  // @Field(() => GraphQLUpload, { nullable: true })
  // bookFile?: Upload;

  @Field(() => GraphQLUpload, { nullable: true })
  coverFile?: Upload;

  @IsBoolean({ message: 'VersionActive must be a boolean value' })
  @Field(() => Boolean)
  versionActive: boolean;

  @IsNumber({}, { message: 'Version must be a string' })
  @Field(() => String, { nullable: false })
  version?: string;

  @IsString()
  @Field(() => String)
  bookId: string;

  @IsBoolean()
  @Field(() => Boolean,{nullable:true})
  is_books_pending_send: boolean;
}
