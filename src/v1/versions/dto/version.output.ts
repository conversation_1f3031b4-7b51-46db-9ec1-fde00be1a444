
import { Field, ObjectType } from "@nestjs/graphql";
import { Version } from "../entities/version.entity";
import File from "src/v1/files/entities/file.entity";


@ObjectType()
export class VersionOutput2 extends Version {

    @Field()
    chapterCount: number;
    
    @Field()
    simulatorCount: number;
}

@ObjectType()
export class VersionWithNroPurchases_Output {

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;

    @Field()
    id: string;

    @Field()
    coverPath: string;

    @Field()
    bookPath: string;

    @Field()
    versionActive: boolean;

    @Field()
    version: string;

    @Field()
    nroPurchases: number;

    @Field()
    is_books_pending_send: boolean;

    constructor(version: Version, nroPurchases: number) {
        this.createdAt = version.createdAt;
        this.updatedAt = version.updatedAt;
        this.id = version.id;
        this.coverPath = version.coverPath;
        this.bookPath = version.bookPath;
        this.versionActive = version.versionActive;
        this.version = version.version;
        this.nroPurchases = nroPurchases;
        this.is_books_pending_send = version.is_books_pending_send;
    }
}

@ObjectType()
export class LastVersionAccessible_Output extends Version{
    @Field(type=>[FileWithPublicUrl_Output])
    files: FileWithPublicUrl_Output []
}

@ObjectType()
export class FileWithPublicUrl_Output extends File{
        @Field()
        publicUrl: string;
}