import {
  Resolver,
  Query,
  Mutation,
  Args,
  Int,
  ResolveField,
  Parent,
  Context,
} from '@nestjs/graphql';
import { VersionsService } from './versions.service';
import { Version } from './entities/version.entity';
import { UpdateVersionInput } from './dto/update-version.input';
import { InsertVersionInput } from './dto/create-version.input';
import { Upload } from 'graphql-upload/Upload.js';
import * as GraphQLUpload from 'graphql-upload/GraphQLUpload.js';
import { Chapter } from '../chapters/entities/chapter.entity';
import { ChaptersService } from '../chapters/chapters.service';
import { LastVersionAccessible_Output, VersionOutput2, VersionWithNroPurchases_Output,  } from './dto/version.output';

@Resolver(() => Version)
export class VersionsResolver {
  constructor(
    private readonly versionsService: VersionsService,
    private readonly chaptersService: ChaptersService,
  ) {}

  @Mutation((returns) => Version)
  async insertVersion(
    // @Args({ name: 'bookFile', type: () => GraphQLUpload })
    // bookFile: Upload,
    @Args({ name: 'cover', type: () => GraphQLUpload })
    coverFile: Upload,
    @Args('versionData')
    versionData: InsertVersionInput,
    @Args('books_ids', { type: () => [String] }) books_ids: string[],
  ): Promise<Version> {

    console.log('coverFile', coverFile);
    console.log('versionData', versionData);
    console.log('books_ids', books_ids);


    const res = await this.versionsService.insertVersion(
      coverFile,
      versionData,
      books_ids,
    );
    return res;
  }

  @ResolveField(() => [Chapter])
  async chapter(@Parent() version: Version): Promise<Chapter[]> {
    const res = await this.chaptersService.getChapters_ByVersionId(version.id);
    return res;
  }

  @Mutation((returns) => Version)
  async updateVersion(
    @Args('versionData') versionData: UpdateVersionInput,
    @Args('books_ids', { type: () => [String] }) books_ids: string[],
  ): Promise<Version> {
    const res = await this.versionsService.updateVersion(versionData, books_ids);
    return res;
  }

  @Query(() => [Version])
  public async getVersions(): Promise<Version[]> {
    return await this.versionsService.getVersions();
  }

  @Query(() => [Version])
  public async getVersions_ByBookId(
    @Args('bookId')
    bookId: string,
  ): Promise<Version[]> {
    return await this.versionsService.getVersions_ByBookId(bookId);
  }

  @Query(() => [VersionWithNroPurchases_Output])
  public async getVersionsWithNroPurchases_ByBookId(
    @Args('bookId')
    bookId: string,
  ): Promise<VersionWithNroPurchases_Output[]> {
    return await this.versionsService.getVersionsWithNroPurchases_ByBookId(bookId);
  }

  @Query(() => Version)
  public async getVersion_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<Version> {
    return await this.versionsService.findOne(id);
  }

  @Mutation(() => Boolean)
  async deleteVersion_ById(
    @Args('id', { type: () => String }) id: string,
  ): Promise<boolean> {
    return await this.versionsService.deleteVersion_ById(id);
  }


  @Query(() => [VersionOutput2])
  public async getMyVersionsBooks_ByPeopleId(
    @Context() context,
  ): Promise<VersionOutput2[]> {
    const peopleId = context.req.user.sub;
    return await this.versionsService.getMyVersionsBooks_ByPeopleId(peopleId);
  }

  @Query(() => Version, { nullable: true })
  public async getMyVersionBook_ByVersionBookId_PeopleId(
    @Context() context,
    @Args('versionId', { type: () => String }) versionId: string,
  ): Promise<Version | null> {
    const peopleId = context.req.user.sub;
    return await this.versionsService.getMyVersionBook_ByVersionBookId_PeopleId(versionId, peopleId);
  }

  @Mutation(()=>Boolean)
  public async sendVersionDocumentsToSubscribers (
    @Args('versionId', { type: () => String }) versionId: string,
  ): Promise<boolean>{
    return await this.versionsService.sendVersionDocumentsToSubscribers(versionId);
  }

  @Query(() => LastVersionAccessible_Output, { nullable: true })
  public async getLastAccessibleBookVersion(
    @Context() context,
    @Args('bookId', { type: () => String }) bookId: string,
  ): Promise<LastVersionAccessible_Output | null> {
    const peopleId = context.req.user.sub;
    console.log('peopleId', peopleId);
    console.log('bookId', bookId);
    return await this.versionsService.getLastAccessibleBookVersion(bookId, peopleId);
  }
}
