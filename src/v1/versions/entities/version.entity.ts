import { Book } from './../../books/entities/book.entity';
import { Shopping } from 'src/v1/shopping/entities/shopping.entity';
import { Products } from 'src/v1/products/entities/products.entity';
import { VersionSubscriptionPlan } from 'src/v1/version-subscription-plans/entities/version-subscription-plan.entity';

import { ObjectType, Field, Int, Float } from '@nestjs/graphql';
import { IsString, IsUUID, IsNumber, IsBoolean } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
} from 'typeorm';
import { Chapter } from 'src/v1/chapters/entities/chapter.entity';

@Entity()
@ObjectType()
export class Version extends AuditableEntity {
  @IsUUID('4', { message: 'Invalid UUID format' })
  @PrimaryGeneratedColumn('uuid')
  @Field(() => String, { nullable: true })
  id: string;

  @IsString()
  @Column()
  @Field({ nullable: true })
  coverPath: string;

  @IsString()
  @Column()
  @Field({ nullable: true, defaultValue: '' })
  bookPath: string;

  @IsBoolean()
  @Column({ type: 'boolean' })
  @Field(() => Boolean)
  versionActive: boolean;

  @Column({ type: 'boolean', default: false })
  @Field(() => Boolean)
  is_books_pending_send: boolean;

  @IsString({ message: 'Version must be a string' })
  @Column({ type: 'varchar', length: '10', nullable: false })
  @Field(() => String)
  version: string;

  @OneToMany(() => Products, (products) => products.version)
  product: Products[];

  @Field(() => Book)
  @ManyToOne(() => Book, (book) => book.version, { eager: true })
  @JoinColumn()
  book: Book;

  @Field()
  @Column()
  @RelationId((version: Version) => version.book)
  bookId: string;

  @OneToMany(
    () => VersionSubscriptionPlan,
    (versionSubscriptionPlan) => versionSubscriptionPlan.version,
  )
  versionSubscriptionPlans: VersionSubscriptionPlan[];

  @OneToMany(() => Chapter, (chapter) => chapter.version, { cascade: true })
  chapter: Chapter[];
}
