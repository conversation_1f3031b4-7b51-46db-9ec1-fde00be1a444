import { Module } from '@nestjs/common';
import { VersionsService } from './versions.service';
import { VersionsResolver } from './versions.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Version } from './entities/version.entity';
import { ChaptersModule } from '../chapters/chapters.module';
import { SimulatorsModule } from '../simulators/simulators.module';
import { AttachedFilesModule } from '../attached-files/attached-files.module';
import { PeoplesModule } from '../peoples/peoples.module';
import { MailModule } from 'src/mail/mail.module';
import { ProductModule } from '../products/products.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Version]), 
    ChaptersModule, 
    SimulatorsModule,
    AttachedFilesModule,
    PeoplesModule,
    MailModule,
    ProductModule
],
  exports: [VersionsService],
  providers: [VersionsResolver, VersionsService],
})
export class VersionsModule {}
