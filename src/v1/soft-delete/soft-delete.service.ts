import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Chapter } from '../chapters/entities/chapter.entity';
import { Simulator } from '../simulators/entities/simulator.entity';
import { Formula } from '../formulas/entities/formula.entity';
import { Sheet } from '../sheets/entities/sheet.entity';
import { Repository } from 'typeorm';
import { Version } from '../versions/entities/version.entity';
import { Book } from '../books/entities/book.entity';
import { VersionsService } from '../versions/versions.service';

@Injectable()
export class SoftDeleteService {
  constructor(
    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,
    @InjectRepository(Version)
    private readonly versionRepository: Repository<Version>,
    private readonly versionsService:VersionsService,
    @InjectRepository(Chapter)
    private readonly chapterRepository: Repository<Chapter>,
    @InjectRepository(Simulator)
    private readonly simulatorRepository: Repository<Simulator>,
    @InjectRepository(Formula)
    private readonly formulaRepository: Repository<Formula>,
    @InjectRepository(Sheet)
    private readonly sheetRepository: Repository<Sheet>,
  ) {}

  /**
   * Esta función elimina una versión por su ID. Primero, elimina todas las hojas, fórmulas, simuladores y capítulos asociados a la versión.
   * Luego, elimina la versión en sí. Retorna verdadero si la operación fue exitosa.
   *
   * @param {string} id - El ID de la versión a eliminar.
   * @returns {Promise<boolean>} - Retorna verdadero si la operación fue exitosa.
   */
  async deleteVersion_ById(id: string): Promise<boolean> {
    await this.versionsService.findOne(id);
    await this.softDeleteSheets_ByVersionId(id);
    await this.softDeleteFormulas_ByVersionId(id);
    await this.softDeleteSimulators_ByVersionId(id);
    await this.softDeleteChapters_ByVersionId(id);
    await this.softDeleteVersion_ByVersionId(id);
    return true;
  }

  async softDeleteSheets_ByVersionId(id: string): Promise<void> {
    const ids_sheets = await this.sheetRepository
      .createQueryBuilder('sheet')
      .select('sheet.id')
      .innerJoin('sheet.simulator', 'simulator')
      .innerJoin('simulator.chapter', 'chapter')
      .where('chapter.versionId = :id', { id: id })
      .getMany()
      .then((sheets) => {
        return sheets.map((sheet) => sheet.id);
      });

    const updated_sheets = await this.sheetRepository
      .createQueryBuilder('sheet')
      .update(Sheet)
      .set({ deletedAt: new Date() })
      .whereInIds(ids_sheets)
      .execute();

    console.log('updated_sheets');
    console.log(updated_sheets);
  }

  async softDeleteFormulas_ByVersionId(id: string): Promise<void> {
    const ids_formulas = await this.formulaRepository
      .createQueryBuilder('formula')
      .select('formula.id')
      .innerJoin('formula.simulator', 'simulator')
      .innerJoin('simulator.chapter', 'chapter')
      .where('chapter.versionId = :id', { id: id })
      .getMany()
      .then((formulas) => {
        return formulas.map((formula) => formula.id);
      });

    const updated_formulas = await this.formulaRepository
      .createQueryBuilder('formula')
      .update(Formula)
      .set({ deletedAt: new Date() })
      .whereInIds(ids_formulas)
      .execute();

    console.log('updated_formulas');
    console.log(updated_formulas);
  }

  async softDeleteSimulators_ByVersionId(id: string): Promise<void> {
    const ids_simulators = await this.simulatorRepository
      .createQueryBuilder('simulator')
      .select('simulator.id')
      .innerJoin('simulator.chapter', 'chapter')
      .where('chapter.versionId = :id', { id: id })
      .getMany()
      .then((simulators) => {
        return simulators.map((simulator) => simulator.id);
      });

    const updated_simulators = await this.simulatorRepository
      .createQueryBuilder('simulator')
      .update(Simulator)
      .set({ deletedAt: new Date() })
      .whereInIds(ids_simulators)
      .execute();

    console.log('updated_simulators');
    console.log(updated_simulators);
  }

  async softDeleteChapters_ByVersionId(id: string): Promise<void> {
    const ids_chapters = await this.chapterRepository
      .createQueryBuilder('chapter')
      .select('chapter.id')
      .where('chapter.versionId = :id', { id: id })
      .getMany()
      .then((chapters) => {
        return chapters.map((chapter) => chapter.id);
      });

    const updated_chapters = await this.chapterRepository
      .createQueryBuilder('chapter')
      .update(Chapter)
      .set({ deletedAt: new Date() })
      .whereInIds(ids_chapters)
      .execute();

    console.log('updated_chapters');
    console.log(updated_chapters);
  }

  async softDeleteVersion_ByVersionId(id: string): Promise<void> {
    const updated_version = await this.versionRepository
      .createQueryBuilder('version')
      .update(Version)
      .set({ deletedAt: new Date() })
      .where('version.id = :id', { id: id })
      .execute();

    console.log('updated_version');
    console.log(updated_version);
  }
}
