import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { SoftDeleteService } from './soft-delete.service';

@Resolver()
export class SoftDeleteResolver {
    constructor(
        private readonly softDeleteService: SoftDeleteService,
    ) {}

    @Mutation(() => Boolean)
    async deleteVersion_ById (
      @Args('id', { type: () => String }) id: string,
    ): Promise<boolean> {
      return await this.softDeleteService.deleteVersion_ById(id);
    }
}
