import { Module } from '@nestjs/common';
import { SoftDeleteResolver } from './soft-delete.resolver';
import { SoftDeleteService } from './soft-delete.service';
import { Book } from '../books/entities/book.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Version } from '../versions/entities/version.entity';
import { Chapter } from '../chapters/entities/chapter.entity';
import { Simulator } from '../simulators/entities/simulator.entity';
import { Sheet } from '../sheets/entities/sheet.entity';
import { Formula } from '../formulas/entities/formula.entity';
import { VersionsModule } from '../versions/versions.module';

@Module({
  imports: [ 
    TypeOrmModule.forFeature([ Book, Version, Chapter, Simulator, Sheet, Formula ]),
    VersionsModule,
  ],
  exports:[SoftDeleteService],
  providers: [SoftDeleteResolver, SoftDeleteService]
})
export class SoftDeleteModule {}
