import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import File from './entities/file.entity';
import { InsertResult, Repository } from 'typeorm';
import { InsertFileInput } from './dto/file.input';

import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FilesService {
    constructor(
        @InjectRepository(File)
        private filesRepository: Repository<File>,
      ) {}
    
      async insertArchivo(data:InsertFileInput):Promise<any>{
        const file= new File();
        file.id= uuidv4();
        file.filename= data.filename;
        file.originalname= data.originalname;
        const result= await this.filesRepository.save(file);
        console.log(result);
        return result;
      }
}
