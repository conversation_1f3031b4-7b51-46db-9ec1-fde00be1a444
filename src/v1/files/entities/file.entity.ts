

import { Field, ObjectType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import { AuditableEntity } from 'src/config/auditable-entity.config';
import AttachedFile from 'src/v1/attached-files/entities/attached-file.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@ObjectType()
@Entity({ name: 'files'})
export default class File extends AuditableEntity {
    @IsUUID('4', { message: 'Invalid UUID format' })
    @Field()
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Field({nullable:true})
    @Column({nullable:true, })
    filename: string; 

    @Field({nullable:true})
    @Column({nullable:true, })
    originalname: string; 

    @Field(type=>[AttachedFile])
    @OneToMany(type=>AttachedFile, attached_file=>attached_file.file )
    attached_files :Promise<AttachedFile[]>
    
}