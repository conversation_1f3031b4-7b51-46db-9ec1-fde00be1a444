import { <PERSON>, Get, Param, Post, Res, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FilesService } from './files.service';
import { Public } from 'src/common/decorators/public.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { editFileName } from 'src/@core/utils/file-uploading.utils';
import { diskStorage } from 'multer';

@Controller('files')
export class FilesController {

    constructor(
        private filesService:FilesService,
      ){}

    @Public()
    @Post('upload_file')
    @UseInterceptors(FileInterceptor('file', {storage:diskStorage(
        {destination: './files/archivos',filename:editFileName}
    )}))
    async uploadedFile(@UploadedFile() file:Express.Multer.File) {
      const response = {
        originalname: file.originalname,
        filename: file.filename,
      };
      let archivo_guardado=await this.filesService.insertArchivo (response);
      return archivo_guardado;
    }
  
    @Public()
    @Get(':filepath')
    seeUploadedFile(@Param('filepath') image, @Res() res) {
      return res.sendFile(image, { root: './files/archivos' });
    }

}

