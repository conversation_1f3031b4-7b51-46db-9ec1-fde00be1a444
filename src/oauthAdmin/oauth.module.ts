// import { Module } from '@nestjs/common';
// import { PassportModule } from '@nestjs/passport';
// import { GoogleStrategyAdmin } from './google.strategy';
// import { OauthAdminService } from './oauth.service';
// import { OauthAdminController } from './oauth.controller';
// import { AdminsModule } from 'src/v1/admins/admins.module';
// import { PeoplesModule } from 'src/v1/peoples/peoples.module';
// import { AuthModule } from 'src/jwt/auth.module';

// @Module({
//   imports: [
//     PassportModule.register({ defaultStrategy: 'googleAdmin' }),
//     AdminsModule,
//     PeoplesModule,
//     AuthModule,
//   ],
//   providers: [GoogleStrategyAdmin, OauthAdminService],
//   controllers: [OauthAdminController],
// })
// export class OauthAdminModule {}
