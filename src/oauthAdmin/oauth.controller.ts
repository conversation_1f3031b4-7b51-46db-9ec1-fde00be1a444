// import { <PERSON>, Get, Req, Res, UseGuards } from '@nestjs/common';
// import { AuthGuard } from '@nestjs/passport';
// import { Request, Response } from 'express';
// import { AdminsService } from './../v1/admins/admins.service';
// import { AuthServiceJwt } from './../jwt/auth.service';
// import { PeoplesService } from './../v1/peoples/peoples.service';
// import { Public } from 'src/common/decorators/public.decorator';

// @Controller('admin/auth/v2')
// export class OauthAdminController {
//   constructor(
//     private adminService: AdminsService,
//     private authServiceJwt: AuthServiceJwt,
//     private peopleService: PeoplesService,
//   ) {}

//   @Public()
//   @Get('google')
//   @UseGuards(AuthGuard('googleAdmin'))
//   googleLogin(@Req() req: Request, @Res() res: Response) {}

//   @Public()
//   @Get('google/callback')
//   @UseGuards(AuthGuard('googleAdmin'))
//   async googleLoginCallback(@Req() req: Request, @Res() res: Response) {
//     const user = req['user'];

//     const searchPeople = await this.peopleService.getPersona_ByEmail(user.email);

//     const searchAdmin = await this.adminService.findOne(searchPeople.email);

//     if (!searchAdmin) {
//       return res.redirect(`${process.env.HOST_CLIENT}/admin/auth/v2/google/`);
//     }

//     const newToken = `Bearer ${await this.authServiceJwt.createCredentials({
//       id: searchAdmin.id,
//       rol: 'admin',
//     })}`;

//     return res.redirect(
//       `${process.env.HOST_CLIENT}/admin/auth/v2/google/${newToken}`,
//     );

//     // res.status(200).json(searchAdmin);
//   }
// }
