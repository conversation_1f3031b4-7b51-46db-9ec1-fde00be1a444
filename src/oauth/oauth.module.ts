import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { GoogleStrategy } from './google.strategy';
import { OauthService } from './oauth.service';
import { OauthController } from './oauth.controller';

import { CredentialsModule } from './../v1/credentials/credentials.module';
import { PeoplesModule } from './../v1/peoples/peoples.module';
import { AuthModule } from './../jwt/auth.module';
@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'google' }),
    CredentialsModule,
    PeoplesModule,
    AuthModule,
  ],
  providers: [GoogleStrategy, OauthService],
  controllers: [OauthController],
})
export class OauthModule {}
