import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { OauthService } from './oauth.service'; // Debes crear este servicio

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private readonly authService: OauthService) {
    super({
      clientID: process.env.OAUTH_clientID,
      clientSecret: process.env.OAUTH_clientSecret,
      callbackURL: process.env.OAUTH_callbackURL,

      scope: ['profile', 'email', 'openid'],
      passReqToCallback: true,
    });
  }

  async validate(
    request: any,
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ) {
    const user = await this.authService.validateGoogleLogin(profile);

    done(null, { ...user, accessToken, rol: 'user' });
  }
}
