// oauth.controller.ts

import { Controller, Get, Req, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response } from 'express';

import { CredentialsService } from './../v1/credentials/credentials.service';

import { PeoplesService } from './../v1/peoples/peoples.service';
import { AuthServiceJwt } from './../jwt/auth.service';
import { Public } from 'src/common/decorators/public.decorator';
import { Payload } from 'src/v1/credentials/entities/credentials.entity';

@Controller('auth/v2')
export class OauthController {
  constructor(
    private credentialsService: CredentialsService,
    private peopleService: PeoplesService,
    private authServiceJwt: AuthServiceJwt,
  ) {}

  @Public()
  @Get('google')
  @UseGuards(AuthGuard('google'))
  googleLogin(@Req() req: Request, @Res() res: Response) {}

  @Public()
  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleLoginCallback(@Req() req: Request, @Res() res: Response) {
    console.log('req');
    console.log(req['user']);
    const user = req['user'];
    const searchUser = await this.peopleService.getPersona_ByEmail(user.email);
    const roles=['user'];

    if (!searchUser) {
      const peopleData = await this.peopleService.create({
        lastName: user.family_name,
        name: user.given_name,
        photoProfile: user.picture,
        email: user.email as string,
        emailVerified: user.email_verified as boolean,
      });

      await this.credentialsService.create({
        people: peopleData,
      });
      // await this.userService.findOne(user.email);
      
      const payload:Payload = {
        sub: peopleData.id,
        name: peopleData.name,
        lastName: peopleData.lastName,
        roles: roles,
      }

      const newToken = `Bearer ${await this.authServiceJwt.createCredentials(
        payload,
      )}`;

      return res.redirect(
        `${process.env.HOST_CLIENT}/auth/v2/google/${newToken}`,
      );
    }


    const payload:Payload = {
      sub: searchUser.id,
      name: searchUser.name,
      lastName: searchUser.lastName,
      roles: roles,
    }
    const newToken = `Bearer ${await this.authServiceJwt.createCredentials(
      payload
    )}`;

    return res.redirect(
      `${process.env.HOST_CLIENT}/auth/v2/google/${newToken}`,
    );
  }
}
