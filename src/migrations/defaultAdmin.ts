import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcrypt';

import { v4 as uuidv4 } from 'uuid';

export class AdminCreateDefault1631646226711 implements MigrationInterface {
  constructor() {}
  public async up(queryRunner: QueryRunner): Promise<void> {
    const id = uuidv4();
    const rolId=1;
    try {
      await queryRunner.query(
        `INSERT INTO people (id, email,name,lastName, emailVerified) 
        VALUES ('${id}', '${process.env.DEFAULT_ADMIN}','superAdmin','superAdmin',1)`,
      );

      await queryRunner.query(
        `INSERT INTO credentials (id, password,peopleId) VALUES (uuid(), '${await bcrypt.hash(
          process.env.DEFAULT_ADMIN_PASSWORD,
          10,
        )}','${id}')`,
      );

      await queryRunner.query(
        `INSERT INTO roles (id, nombre , cod_rol) VALUES (${rolId},'Administrador', 'admin')`,
      );

      await queryRunner.query(
        `INSERT INTO admin (id, peopleId, rolId) VALUES (uuid(), '${id}',${rolId})`,
      );
    } catch (e) {
      console.log(e);
      // console.log('The default user has already been created');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
