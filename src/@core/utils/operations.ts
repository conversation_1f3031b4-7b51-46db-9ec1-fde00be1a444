export class Operaciones {
  static zeroFill(number, width) {
    width -= number.toString().length;
    if (width > 0) {
      return new Array(width + (/\./.test(number) ? 2 : 1)).join('0') + number;
    }
    return number + ''; // siempre devuelve tipo cadena
  }

  static nombreCompleto(empleado): string {
    let nombre_completo: string =
      empleado.apellido_paterno +
      ' ' +
      empleado.apellido_materno +
      ' ' +
      empleado.primer_nombre +
      ' ' +
      empleado.otros_nombres;
    return nombre_completo;
  }

  static getStringFechaAnioMes(anio: number, mes: number) {
    let mes_ = Operaciones.zeroFill(mes, 2);
    let strFecha: string = anio + '-' + mes_ + '-01';
    return strFecha;
  }

  static diasEnUnMes(mes, año) {
    return new Date(año, mes, 0).getDate();
  }

  static roundNumber(num, scale) {
    if (!('' + num).includes('e')) {
      return +(Math.round(Number(num + 'e+' + scale)) + 'e-' + scale);
    } else {
      var arr = ('' + num).split('e');
      var sig = '';
      if (+arr[1] + scale > 0) {
        sig = '+';
      }
      return +(
        Math.round(Number(+arr[0] + 'e' + sig + (+arr[1] + scale))) +
        'e-' +
        scale
      );
    }
  }

  static ordenarArrayObjects(array, propiedad) {
    let array_slice = array.slice();
    array_slice.sort(function (a, b) {
      if (a[propiedad] > b[propiedad]) {
        return 1;
      }
      if (a[propiedad] < b[propiedad]) {
        return -1;
      }
      // a must be equal to b
      return 0;
    });
    // console.log(array_slice);
    return array_slice;
  }

  // Función para eliminar el prefijo 'version_' de las propiedades de un objeto
  static removePrefixFromKeys(obj: any, prefix: string): any {
    return Object.keys(obj).reduce((result, key) => {
      const newKey = key.startsWith(prefix) ? key.slice(prefix.length) : key;
      result[newKey] = obj[key];
      return result;
    }, {});
  }
}
