import { extname } from "path";
import { FuncionesAux } from "./funciones-aux";

export const editFileName = (req, file, callback) => {
    let name = file.originalname.split('.')[0];
    name= FuncionesAux.reemplazar<PERSON><PERSON><PERSON>sPorGuionesBajos(name);
    let date=new Date().getTime();
    const fileExtName = extname(file.originalname);
    const randomName = Array(9)
      .fill(null)
      .map(() => Math.round(Math.random() * 16).toString(16))
      .join('');
    callback(null, `${name}-${randomName}${date}${fileExtName}`);
  };
