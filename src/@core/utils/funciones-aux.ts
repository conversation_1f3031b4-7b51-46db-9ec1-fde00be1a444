export class FuncionesAux{
    static nombre_completo(persona):string{
        let concat_nombre_completo=persona.nombres+' '+persona.apellidos
        let nombre_completo:string= this.eliminarEspaciosInnecesarios(concat_nombre_completo);
        return nombre_completo;
      }

    static reemplazar<PERSON><PERSON><PERSON>sPorGuionesBajos(texto:string){
      return texto.replace(/ /g, "_");
    }

    static generatePassword(passwordLength) {
      var numberChars = '0123456789';
      var upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      var lowerChars = 'abcdefghijklmnopqrstuvwxyz';
      var allChars = numberChars + upperChars + lowerChars;
      var randPasswordArray = Array(passwordLength);
      randPasswordArray[0] = numberChars;
      randPasswordArray[1] = upperChars;
      randPasswordArray[2] = lowerChars;
      randPasswordArray = randPasswordArray.fill(allChars, 3);
      return this.shuffleArray(
        randPasswordArray.map(function (x) {
          return x[Math.floor(Math.random() * x.length)];
        })
      ).join('');
    }
    static shuffleArray(array) {
      for (var i = array.length - 1; i > 0; i--) {
        var j = Math.floor(Math.random() * (i + 1));
        var temp = array[i];
        array[i] = array[j];
        array[j] = temp;
      }
      return array;
    }

    static eliminarEspaciosInnecesarios(cadena: String){
      return cadena.trim().replace("\\s+", " ");
    }
}