# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Items {
  value: String!
  editable: Boolean!
  columnName: String!
  row: Int!
  bold: Boolean
  orderNumber: Int
  position: [Int!]!
  show: Boolean!
}

type Formula {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  position: [Int!]!
  formula: String!
  name: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type Rol {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: Float!
  nombre: String
  cod_rol: String
  administradores: [Admin!]!
}

type Admin {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  rolId: Int
  rol: Rol
  infoPeople: People!
}

type People {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  name: String
  lastName: String
  birthDate: DateTime
  photoProfile: String
  email: String!
  codeVerification: String
  emailVerified: Boolean!
  cellPhone: String
  regionCode: String
  academicLevel: String
  country: String
  city: String
  companyInstitution: String
  studentProfessional: String
}

type SubscriptionPlan {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  name: String!
  duration_days: Int!
}

type VersionSubscriptionPlan {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  price: Float!
  versionId: String!
  version: Version!
  subscriptionPlanId: String!
  subscriptionPlan: SubscriptionPlan!
}

type QrPaymentBankNotification {
  QRId: String!
  Gloss: String!
  sourceBankId: Int!
  originName: String!
  VoucherId: String!
  TransactionDateTime: String!
  additionalData: String!
}

type QrInfo {
  id: String
  qr: String
  success: Boolean
  message: String
}

type QrBody {
  currency: String
  gloss: String
  amount: Float!
  singleUse: Boolean!
  expirationDate: String!
  additionalData: String
  destinationAccountId: String
}

type Shopping {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  proofOfpayment: String
  amountPay: Float
  qrInfo: QrInfo
  qrBody: QrBody
  qrPaymentBankNotification: QrPaymentBankNotification
  paymentMade: Boolean
  paymentStatus: String
  people: People!
  coupon: Coupon
  client: People!
}

type Coupon {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  name: String!
  dateStart: DateTime!
  dateEnd: DateTime!
  quantity: Int!
  usedQuantity: Int!
  unlimited: Boolean!
  discountPercentage: Float!
  isEnable: Boolean!
  isAllBooks: String
  selectedBookIds: [String!]
}

type Book {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  title: String!
  author: String!
  description: String!
  code: String!
  versions: [Version!]!
}

type Version {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  coverPath: String
  bookPath: String
  versionActive: Boolean!
  is_books_pending_send: Boolean!
  version: String!
  book: Book!
  bookId: String!
  chapter: [Chapter!]!
}

type Chapter {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  name: String
  chapterNumber: Int
  version: Version!
  simulators: [Simulator!]!
  open: Boolean
}

type Simulator {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  name: String!
  isPublic: Boolean!
  coverSimulatorPath: String
  simulatorNumber: Int
  chapter: Chapter!
  chapterId: String!
}

type Comment {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  name: String
  mail: String
  regionCode: String
  cellphone: String
  affair: String
  message: String
}

type SheetOutput {
  id: String
  name: String!
  show: Boolean!
  orderNumber: Int
  sheetNumber: Int!
  type: String!
  data: [[Items!]!]!
  simulatorId: String!
}

type ItemOutput {
  value: String!
  editable: Boolean!
  columnName: String!
  row: Int!
  position: [Int!]
  bold: Boolean
  show: Boolean!
}

type TableOutput {
  id: String!
  name: String!
  sheetNumber: Int!
  orderNumber: Int
  type: String!
  data: [[ItemOutput!]!]!
  show: Boolean!
  simulatorId: String!
}

type SimulatorOutput {
  id: String
  name: String!
  isPublic: Boolean!
  simulatorNumber: Int!
  tables: [TableOutput!]!
  coverSimulatorPath: String
  chapterId: String!
  bookId: String!
  versionId: String!
}

type PeopleOutput {
  updatedAt: DateTime
  id: String
  name: String
  lastName: String
  birthDate: DateTime
  photoProfile: String
  email: String!
  codeVerification: String
  emailVerified: Boolean!
  credentialExists: Boolean!
  cellPhone: String
  regionCode: String
  academicLevel: String
  country: String
  city: String
  companyInstitution: String
  studentProfessional: String
}

type InfoTableDataPeople {
  quantityItems: Int
  quantityItemsFilter: Int
}

type PeopleDataFilterOutput {
  peoples: [People!]
  infoTable: InfoTableDataPeople
}

type JwtOutput {
  token: String
}

type File {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String!
  filename: String
  originalname: String
  attached_files: [AttachedFile!]!
}

type AttachedFile {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: Float!
  nombre_tabla: String
  registro_id: String
  file_id: String
  file: File!
}

type VersionOutput {
  id: String!
  versionActive: Boolean
  version: String!
  cover: String
}

type SalesBookOutput {
  id: String!
  title: String
  author: String
  sales: Int!
  chapters: Int!
  simulators: Int!
  description: String
  code: String
  version: VersionOutput
}

type VersionOutput2 {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  coverPath: String
  bookPath: String
  versionActive: Boolean!
  is_books_pending_send: Boolean!
  version: String!
  book: Book!
  bookId: String!
  chapter: [Chapter!]!
  chapterCount: Float!
  simulatorCount: Float!
}

type VersionWithNroPurchases_Output {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: String!
  coverPath: String!
  bookPath: String!
  versionActive: Boolean!
  version: String!
  nroPurchases: Float!
  is_books_pending_send: Boolean!
}

type LastVersionAccessible_Output {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String
  coverPath: String
  bookPath: String
  versionActive: Boolean!
  is_books_pending_send: Boolean!
  version: String!
  book: Book!
  bookId: String!
  chapter: [Chapter!]!
  files: [FileWithPublicUrl_Output!]!
}

type FileWithPublicUrl_Output {
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt: DateTime
  id: String!
  filename: String
  originalname: String
  attached_files: [AttachedFile!]!
  publicUrl: String!
}

type ProductItemOutput {
  """Title of the product"""
  title: String!

  """Version of the product"""
  version: String!

  """Author of the product"""
  author: String!

  """Cover path of the product"""
  coverPath: String!

  """Price of the product"""
  price: Float!

  """Quantity of the product"""
  quantity: Int!

  """Is subscribed flag"""
  isSubscribed: Boolean!
}

type Summary {
  """List of products"""
  products: [ProductItemOutput!]!

  """Coupon code"""
  coupon: String

  """Discount amount"""
  discount: Float!

  """Total amount"""
  total: Float!

  """Total amount without discount"""
  totalWithoutDiscount: Float!
}

type Summary2 {
  """Product details"""
  product: ProductItemOutput!

  """Coupon code"""
  coupon: String

  """Discount amount"""
  discount: Float!

  """Total amount"""
  total: Float!

  """Total amount without discount"""
  totalWithoutDiscount: Float!
}

type ReportUser {
  id: String
  name: String
  lastName: String
  email: String
  shoppingQuantity: Int
}

type ReportShopping {
  id: String
  title: String
  author: String
  sales: Int
}

type ReportTotalIncomeMonth {
  year: Int!
  month: Int!
  incomeBooks: Float!
}

type ReportTotalSalesBooksMonth {
  year: Int!
  month: Int!
  salesBooks: Int!
}

type ReportSalesData {
  id: String!
  date: DateTime!
  amount: Float!
  method: String!
  status: String!
  email: String!
  isDisabled: Boolean
  personId: String!
  user: String!
  profile: String!
}

type ProofOfPaymentFromAUser {
  id: String!
  proofOfpayment: String!
  amount: Float!
  paymentMade: Boolean!
  paymentStatus: String!
  clientId: String!
  nameClient: String!
  lastNameClient: String!
  emailClient: String!
  photoProfileClient: String!
  nameCoupon: String
  discountCoupon: Float
  totalPrice: Float!
  totalPriceWithDiscount: Float!
  datePurchase: DateTime!
}

type ProductItem {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: String!
  price: Float
  priceWithDiscount: Float
  amount: Float
  dateStart: DateTime
  dateEnd: DateTime
  version: Version
  versionSubscriptionPlan: VersionSubscriptionPlan
}

type ShoppingDetailsOutput {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: String!
  proofOfPayment: String
  amountPay: Float!
  paymentMade: Boolean!
  paymentStatus: String!
  method: String!

  """List of products"""
  products: [ProductItem!]!
  qrInfo: QrInfo
  qrBody: QrBody
  qrBank: QrPaymentBankNotification
  coupon: Coupon
  people: People
}

type EconomixReturnDto {
  nroCuota: Float!
  capital: Float!
  interes: Float!
  cuotaSinSeguro: Float!
  seguroDesgravement: Float!
  seguroGarantiaInmueble: Float!
  seguroGarantiaVehiculo: Float!
  tre: Float!
  cuotaTotal: Float!
  saldoCapital: Float!
  tiempoDias: Float!
}

type TreR {
  data: String!
  value: Float!
}

type LoanDetailsR {
  loadAmount: Float!
  creditTermValue: Int!
  creditTerm: String!
  feeType: String!
  paymentFrequency: String!
  interestRate: Float!
  typeIncome: String!
  typeCredit: String!
  typeGuarante: String!
  creditInsurance: Float!
  tre: TreR!
  realEstateMortgage: Float!
  vehicleMortgage: Float!
}

type MainDataDto {
  data: [EconomixReturnDto!]!
  totalCapital: Float!
  totalInteres: Float!
  totalCuotaSinSeguro: Float!
  totalSeguroDesgravement: Float!
  totalSeguroGarantiaInmueble: Float!
  totalSeguroGarantiaVehiculo: Float!
  totalTre: Float!
  totalCuotaTotal: Float!
  totalSaldoCapital: Float!
  body: LoanDetailsR!

  """Indica si la garantía vehicular está activa."""
  garantia_vehicular_activa: Boolean!
}

type ImportChapterSimulatorToVersion {
  """Example field (placeholder)"""
  exampleField: Int!
}

type Query {
  simulators: [Simulator!]!
  simulatorTables(simulatorId: String!): [SheetOutput!]!
  simulatorAllAdmin: [Simulator!]!
  simulatorAdmin(simulatorId: String!): SimulatorOutput!
  getSimulatorById(id: String!): Simulator!
  verifyTokenRecovery(token: String!): People!
  getProfileUser: PeopleOutput!
  findOneUser(id: String!): PeopleOutput!
  findAllUsers: [People!]!
  findAllUsersFilter(filterData: PeopleInputFilter!): PeopleDataFilterOutput!
  suspendAccountUser(id: String!): Boolean!
  proofOfPaymentPendingState: [Shopping!]!
  getShoppingByPeopleId: [Shopping!]!
  comments: [Comment!]!
  findOnecomment(id: String!): Comment!
  removeComment(id: String!): Comment!
  findAllFormula: [Formula!]!
  refreshTokenJWT: JwtOutput!
  refreshTokenJWTAdmin: JwtOutput!
  loginUserPassword(email: String!, password: String!): String!
  loginAdminPassword(email: String!, password: String!): String!
  generateHash(password: String!): String!
  findAllAdmin: [Admin!]!
  findOneAdmin(id: String!): Admin!
  findAllBook: [Book!]!
  findBookId(id: String!): Book!
  bookForSales: [SalesBookOutput!]!
  getVersions: [Version!]!
  getVersions_ByBookId(bookId: String!): [Version!]!
  getVersionsWithNroPurchases_ByBookId(bookId: String!): [VersionWithNroPurchases_Output!]!
  getVersion_ById(id: String!): Version!
  getMyVersionsBooks_ByPeopleId: [VersionOutput2!]!
  getMyVersionBook_ByVersionBookId_PeopleId(versionId: String!): Version
  getLastAccessibleBookVersion(bookId: String!): LastVersionAccessible_Output
  getChapters_ByVersionId(versionId: String!): [Chapter!]!
  getSimulator_ByNameAndVersionId(versionId: String!, simulatorName: String!): Simulator!
  getActiveVersionIdBySimulatorName(simulatorName: String!): String
  getChapters_BySimulatorName(simulatorName: String!): [Chapter!]!
  getChapter_ById(id: String!): Chapter!
  getChapter_byID2(id: String!): Chapter!
  getFiles_DeRegistro(nombre_tabla: String!, registro_id: String!): [AttachedFile!]!
  proofOfPayment: [Shopping!]!
  proofOfPaymentPending: [Shopping!]!
  reportShopping: [ReportShopping!]!
  reportUser: [ReportUser!]!
  incomeByMonths: [ReportTotalIncomeMonth!]!
  quantitySalesBooksMonths: [ReportTotalSalesBooksMonth!]!
  incomeByRank(startDate: DateTime!, endDate: DateTime!): Float!
  getAllShopping: [ReportSalesData!]!
  getProofOfPaymentById(id: String!): ProofOfPaymentFromAUser!
  getPurchaseDetails(id: String!): ShoppingDetailsOutput!
  productAboutToExpire: Boolean!
  getVersionSubscriptionPlans_ByVersionId(id: String!): [VersionSubscriptionPlan!]!
  getVersionSubscriptionPlan_ById(id: String!): VersionSubscriptionPlan!
  getSubscriptionPlans: [SubscriptionPlan!]!
  getSubscriptionPlan_ById(id: String!): SubscriptionPlan!
  coupon(id: String!): Coupon!
  couponsList: [Coupon!]!
  importChapterSimulatorToVersion: [ImportChapterSimulatorToVersion!]!
}

input PeopleInputFilter {
  search: String
  order: String
  page: Float
  quantityItemsPage: Float
}

type Mutation {
  simulatorResolve(data: CalculateInput!): SimulatorOutput!
  calculateDynamicSimulatorData(data: CalculateDynamicInput!): SimulatorOutput!
  createAllsfs(createAllsfsInput: CreateAllsfsInput!): Simulator!
  updateAllsfs(updateAllsfsInput: SimulatorUpdateInput!): SimulatorOutput!
  deleteSimulator(simulatorId: String!): [Simulator!]!
  simulatorResolveAdmin(data: CalculateInput!): SimulatorOutput!
  reassignChapterToSimulator(id: String!, chapterId: String!): Boolean!
  confirmCode(dataUser: CodeVerificationInput!): Boolean!
  sendComment(data: CommentInput!): Boolean!
  recoveryAccountUser(email: String!): Boolean!
  updateEmailUser(id: String!, email: String!): Boolean!
  createPeople(createPeopleInput: PeopleInput!): People!
  updatePeople(peopleData: PeopleInput!): Boolean!
  updatePeopleAdmin(peopleData: PeopleInput!): Boolean!
  createAccountUser(dataUser: CreateUserInputAccount!): Boolean!
  createAccountUserForAdmin(dataUser: CreateUserInputAccount!): Boolean!
  updateCredentials(token: String!, password: String!): Boolean!
  changePasswordCredential(password: String!, newPassword: String!): Boolean!
  createAdmin(idPeople: String!): Boolean!
  updateItemAdmin(data: UpdateAdminInput!): Boolean!
  deleteAdmin(id: String!): Boolean!
  createBook(info: CreateBookInput!): Boolean!
  updateBook(updateData: UpdateBookInput!): Boolean!
  deleteBook(bookId: String!): Boolean!
  insertVersion(cover: Upload!, versionData: InsertVersionInput!, books_ids: [String!]!): Version!
  updateVersion(versionData: UpdateVersionInput!, books_ids: [String!]!): Version!
  deleteVersion_ById(id: String!): Boolean!
  sendVersionDocumentsToSubscribers(versionId: String!): Boolean!
  insertChapter(chapterData: CreateChapterInput!): Chapter!
  updateChapter(chapterData: UpdateChapterInput!): Chapter!
  deleteChapter_ById(id: String!): Boolean!
  updateChapterNumber(chapterData: UpdateChapterNumberInput!): Boolean!
  sortTheChapterSimulators(OrderData: UpdateOrderChapterSimulatorsInput!): Boolean!
  updateChapterOpen(chapterData: UpdateChapterOpenInput!): Boolean!
  asignarNumeroDeOrdenEnSimudores(versionId: String!): Boolean!
  copyBookVersionChapters(surceVersionId: String!, destinationVersionId: String!): Boolean!
  saveFiles_DeRegistro(registro_id: String!, nombre_tabla: String!, files_ids: [String!]!): [AttachedFile!]!
  purchaseDetail(data: CreateShoppingInput!): Summary!
  confirmPurchaseQR(data: CreateShoppingInput!): String!
  confirmPurchaseQR2(data: CreateShopping2Input!): String!
  purchaseDetail2(data: CreateShopping2Input!): Summary2!
  confirmPurchaseProofOfPayment(data: CreateShoppingInput!): Boolean!
  confirmPurchaseProofOfPayment2(data: CreateShopping2Input!): Shopping!
  uploadProofOfPayment(proofOfPayment: Upload!, shoppingId: String!): Boolean!
  enableAccountProofOfPayment(shoopingId: String!): Boolean!
  rejectProofOfPayment(shoopingId: String!, paymentRejectionReason: String): Boolean!
  insertVersionSubscriptionPlan(version_subscription_planData: InsertVersionSubscriptionPlanInput!): VersionSubscriptionPlan!
  deleteVersionSubscriptionPlan_ById(id: String!): Boolean!
  insertSubscriptionPlan(subscription_planData: InsertSubscriptionPlanInput!): SubscriptionPlan!
  updateSubscriptionPlan(subscription_planData: UpdateSubscriptionPlanInput!): SubscriptionPlan!
  deleteSubscriptionPlan_ById(id: String!): Boolean!
  createCoupon(createCouponInput: CreateCouponInput!): Coupon!
  updateCoupon(id: String!, updateCouponInput: UpdateCouponInput!): Boolean!
  removeCoupon(id: String!): Boolean!
  verifyCoupon(nameCoupon: String!, bookId: String!): String!
  createEconomixForm(createEconomixFormInputDto: CreateEconomixFormInputDto!): MainDataDto!
  createImportChapterSimulatorToVersion(body: ChaptersSimulatorsIdsDTO!): String!
  createImportSimulatorsToChapter(body: SimulatorsToChapterDTO!): String!
}

input CalculateInput {
  id: String!
  tables: [TableCalculate!]!
}

input TableCalculate {
  id: String!
  name: String!
  data: [[String!]!]!
}

input CalculateDynamicInput {
  id: String!
  tables: [TableDynamicCalculate!]!
}

input TableDynamicCalculate {
  id: String!
  name: String!
  type: String!
  data: [[String!]!]!
}

input CreateAllsfsInput {
  name: String!
  isPublic: Boolean!
  chapterId: String!
  coverSimulator: Upload
  tables: [Table!]!
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

input Table {
  name: String!
  orderNumber: Int
  type: String!
  data: [[Item!]!]!
  show: Boolean!
}

input Item {
  value: String!
  editable: Boolean!
  columnName: String!
  row: Int!
  position: [Int!]
  show: Boolean!
  bold: Boolean
}

input SimulatorUpdateInput {
  id: String
  name: String!
  isPublic: Boolean!
  coverSimulator: Upload
  chapterId: String
  tables: [TableUpdateInput!]!
}

input TableUpdateInput {
  name: String!
  data: [[ItemUpdateInput!]!]!
  show: Boolean!
  type: String!
  orderNumber: Int
}

input ItemUpdateInput {
  value: String!
  editable: Boolean!
  columnName: String!
  row: Int!
  position: [Int!]
  show: Boolean!
  bold: Boolean
}

input CodeVerificationInput {
  email: String!
  codeVerification: String
}

input CommentInput {
  name: String
  mail: String
  regionCode: String
  cellphone: String
  affair: String
  message: String
}

input PeopleInput {
  name: String
  lastName: String
  birthDate: DateTime
  photoProfile: String
  cellPhone: String
  regionCode: String
  email: String
  codeVerification: String
  emailVerified: Boolean
  academicLevel: String
  country: String
  city: String
  companyInstitution: String
  studentProfessional: String
}

input CreateUserInputAccount {
  password: String
  people: PeopleInputCreate!
}

input PeopleInputCreate {
  name: String!
  lastName: String!
  email: String!
  birthDate: DateTime
  photoProfile: String
  cellPhone: String
  regionCode: String
  academicLevel: String
  country: String
  city: String
  companyInstitution: String
  studentProfessional: String
}

input UpdateAdminInput {
  id: String!
  email: String!
}

input CreateBookInput {
  title: String!
  author: String!
  description: String!
  code: String!
}

input UpdateBookInput {
  id: String!
  title: String
  author: String
  description: String
  code: String
}

input InsertVersionInput {
  coverPath: String
  bookPath: String
  versionActive: Boolean!
  version: String
  bookId: String!
  is_books_pending_send: Boolean!
}

input UpdateVersionInput {
  id: String
  coverPath: String
  bookPath: String
  coverFile: Upload
  versionActive: Boolean!
  version: String!
  bookId: String!
  is_books_pending_send: Boolean
}

input CreateChapterInput {
  name: String
  chapterNumber: Int!
  versionId: String!
}

input UpdateChapterInput {
  id: String!
  name: String
  chapterNumber: Int!
}

input UpdateChapterNumberInput {
  chapters: [UpdateChapterInput!]!
}

input UpdateOrderChapterSimulatorsInput {
  chapterId: String!
  simulators: [SimulatorOrder!]!
}

input SimulatorOrder {
  id: String!
  name: String!
  simulatorNumber: Int!
}

input UpdateChapterOpenInput {
  id: String!
  open: Boolean!
}

input CreateShoppingInput {
  couponId: String
  planVersionId: String
  cartVersionBooksIds: [String!]!
}

input CreateShopping2Input {
  couponId: String
  versionBookId: String!
  versionSubscriptionPlanId: String!
}

input InsertVersionSubscriptionPlanInput {
  versionId: String!
  subscriptionPlanId: String!
  price: Float!
}

input InsertSubscriptionPlanInput {
  name: String!
  duration_days: Float!
}

input UpdateSubscriptionPlanInput {
  id: String!
  name: String!
  duration_days: Float!
}

input CreateCouponInput {
  name: String!
  dateStart: DateTime!
  dateEnd: DateTime!
  quantity: Int
  discountPercentage: Float!
  unlimited: Boolean!
  isEnable: Boolean!
  isAllBooks: IsAllBooks!
  selectedBookIds: [String!]!
}

enum IsAllBooks {
  SI
  NO
}

input UpdateCouponInput {
  name: String
  dateEnd: DateTime
  dateStart: DateTime
  quantity: Int
  unlimited: Boolean!
  discountPercentage: Float
  isEnable: Boolean
  isAllBooks: IsAllBooks!
  selectedBookIds: [String!]
}

input CreateEconomixFormInputDto {
  """El campo es requerido"""
  loadAmount: Int!

  """El campo es requerido"""
  creditTermValue: Int!
  creditTerm: CreditTermOptions!
  feeType: FeeTypeOptions!
  paymentFrequency: PaymentFrecuencyOptions!
  interestRate: Float!
  typeIncome: TypeIncomeOptions!
  typeCredit: TypeCreditOptionsIndep!
  typeGuarante: TypeGuaranteeOptions!
  creditInsurance: CreditInsurance!
  tre: TRE!
  realEstateMortgage: RealEstateMortgage!
  vehicleMortgage: VehicleMortgage!
}

"""Opciones de plazo de crédito"""
enum CreditTermOptions {
  Meses
  Anios
}

"""Opciones de tipo de credito"""
enum FeeTypeOptions {
  Cuota_Fija
  Cuota_Variable
}

"""Opciones de frecuencia de pago"""
enum PaymentFrecuencyOptions {
  Mensual
  Bimestral
  Trimestral
  Semestral
}

"""Opciones de tipo de ingreso"""
enum TypeIncomeOptions {
  Asalariado
  Independiente
}

"""Opciones de tipo de credito independiente"""
enum TypeCreditOptionsIndep {
  Credito_Vivienda_Social
  Credito_Vivienda
  Credito_Negocio
  Credito_Vehicular_Nuevo_Independiente
  Credito_Vehicular_Nuevo
  Credito_Vehicular_Usado
  Credito_Productivo
  Credito_Consumo
}

"""Opciones de tipo de credito independiente"""
enum TypeGuaranteeOptions {
  Hipoteca_de_Inmueble
  Hipoteca_de_Inmueble_VS
  Hipoteca_de_Inmueble_VS_1
  Hipoteca_de_Inmueble_VS_2
  Hipoteca_de_Inmueble_VS_3
  Hipoteca_de_Vehiculo_Nuevo
  Hipoteca_de_Vehiculo_Nuevo_Independiente
  Hipoteca_de_Vehiculo_Usado
  Personal
  Prendaria
  Sola_Firma
  Hipoteca_Inmueble_Productivo
  Hipoteca_Vehiculo_Productivo
  Personal_Procutivo
  Prendaria_Procutivo
}

input CreditInsurance {
  data: YesNoOptions!
  value: Float!
}

"""Opciones solo puede ser SI o NO"""
enum YesNoOptions {
  SI
  NO
}

input TRE {
  data: YesNoOptions!
  value: Float!
}

input RealEstateMortgage {
  data: YesNoOptions!
  constructionValue: Float!
  fireInsuranceRate: Float!
}

input VehicleMortgage {
  data: YesNoOptions!
  vehicleValue: Float!
  vehicleInsuranceRate: Float!
}

input ChaptersSimulatorsIdsDTO {
  currentVersion: String!
  bookId: String!
  versionId: String!
  chapterIds: [ChapterIds!]!
}

input ChapterIds {
  chapterId: String!
  simulatorIds: [String!]!
}

input SimulatorsToChapterDTO {
  currentVersion: String!
  bookId: String!
  versionId: String!
  chapterId: String!
  simulatorIds: [String!]!
}