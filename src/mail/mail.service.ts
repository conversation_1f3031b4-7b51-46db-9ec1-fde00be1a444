import { MailerService } from '@nestjs-modules/mailer';
import { Injectable } from '@nestjs/common';
import { Admin } from 'src/v1/admins/entities/admin.entity';
import { Credentials } from 'src/v1/credentials/entities/credentials.entity';
import { People } from 'src/v1/peoples/entities/people.entity';
import { AttachmentInput } from './dto/attachments-input';
import { CommentInput } from 'src/v1/peoples/dto/comment.input';
import {
  DataEmailExpiration,
  Shopping,
} from 'src/v1/shopping/entities/shopping.entity';
import { EmailLogInput } from 'src/v1/email-logs/dto/email-log.input';
import { EmailLogsService } from 'src/v1/email-logs/email-logs.service';

export interface IDataEmailNewVersionBook {
  book_version: string;
  book_name: string;
  people_name: string;
  people_email: string;
  attachments: AttachmentInput[];
}
@Injectable()
export class MailService {
  constructor(
    private mailerService: MailerService,
    private emailsService: EmailLogsService,
  ) {}

  async sendUserVerificationCode(people: People) {
    this.mailerService.sendMail({
      to: people.email,
      subject: 'Codigo de confirmacion',
      template: './verification',
      context: {
        name: people.name,
        codeVerification: people.codeVerification,
      },
    });
  }

  async sendUpdateAccountUser(people: People) {
    this.mailerService.sendMail({
      to: people.email,
      subject: 'Cuenta actualizada',
      template: './updateAccount',
      context: {
        name: people.name,
        codeVerification: people.codeVerification,
      },
    });
  }

  async sendUserConfirmationCreateAccountAdmin(people: People) {
    this.mailerService.sendMail({
      to: people.email,
      subject: 'Cuenta habilitada',
      template: './confirmation',
      context: {
        name: people.name,
        email: people.email,
      },
    });
  }

  async sendLinkRecoveryAccount(people: People, link: string) {
    this.mailerService.sendMail({
      to: people.email,
      subject: 'Recuperar cuenta',
      template: './recoveryAccountUsers',
      context: {
        link,
      },
    });
  }

  async sendCredentials(people: People, password: string) {
    this.mailerService.sendMail({
      to: people.email,
      subject: 'Credenciales de acceso',
      template: './credentials',
      context: {
        password,
        email: people.email,
        name: people.name,
      },
    });
  }

  async confirmationPurchase(
    people: People,
    attachments: AttachmentInput[],
    products: any[],
    total: string,
    discounted: string,
  ) {
    const template= attachments.length > 0 ? './sendBook2' : './sendBook_withoutPdf';
      this.mailerService.sendMail({
        to: people.email,
        subject: 'Compra completada con exito.',
        template: template,
        attachments,
        context: {
          email: people.email,
          name: people.name,
          products,
          total,
          discounted,
          supportEmail: process.env.SUPPORT_EMAIL,
        },
      });
    
  }

  async sendRejectedProofOfPayment(people: People, shopping: Shopping) {
    this.mailerService.sendMail({
      to: people.email,
      subject: 'Compra rechazada.',
      template: './sendRejectedProofOfPayment',
      context: {
        email: people.email,
        name: people.name + ' ' + people.lastName,
        reason: shopping.paymentRejectionReason,
        supportEmail: process.env.SUPPORT_EMAIL,
      },
    });
  }

  async sendExpirationNotification(data: DataEmailExpiration) {
    this.mailerService.sendMail({
      to: data.email,
      subject:
        '¡Tu plan de suscripción del libro ' +
        data.title +
        ' está por expirar!',
      template: './sendExpirationNotification',
      context: {
        emailContact: process.env.SUPPORT_EMAIL,
        title: data.title,
        email: data.email,
        name: data.name,
        expirationDate: data.expirationDate,
        daysLeft: data.daysLeft,
        url: data.url,
        type: data.type,
      },
    });
  }

  async proofOfPaymentSuccessful(data: {
    email: string;
    name: string;
    lastName: string;
  }) {
    try {
      this.mailerService.sendMail({
        to: data.email,
        subject: 'Confirmación de Recepción de Comprobante de Pago.',
        template: './proofOfPaymentSuccessful',
        context: {
          name: data.name + ' ' + data.lastName,
        },
      });
    } catch (e) {
      return false;
    }
  }

  async sendConfirmationOfReceiptOfContactForm(data: CommentInput) {
    await this.mailerService.sendMail({
      to: data.mail,
      subject: 'Confirmación de Recepción de Formulario de Contacto',
      template: './confirmationOfReceiptOfContactForm',
      context: {
        name: data.name,
      },
    });
  }

  async comment(data: CommentInput): Promise<Boolean> {
    try {
      this.mailerService.sendMail({
        to: process.env.SUPPORT_EMAIL,
        subject: ' Formulario de contacto: ' + data.affair,
        template: './sendComment',
        context: {
          name: data.name,
          mail: data.mail,
          regionCode: data.regionCode,
          cellphone: data.cellphone,
          affair: data.affair,
          message: data.message,
        },
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async sendEmail_newVersionBook(
    data: IDataEmailNewVersionBook,
  ): Promise<boolean> {
    const dataSendEmail = {
      to: data.people_email,
      subject: 'Nueva versión ' + data.book_version + ' : ' + data.book_name,
      template: './newBookVersionNotification',
      attachments: data.attachments,
      context: {
        people_name: data.people_name,
        book_version: data.book_version,
        book_name: data.book_name,
        supportEmail: process.env.SUPPORT_EMAIL,
      },
      sendDate: new Date(),
      status: 'enviado',
    };

    try {
      this.mailerService.sendMail({
        to: dataSendEmail.to,
        subject: dataSendEmail.subject,
        template: dataSendEmail.template,
        attachments: dataSendEmail.attachments,
        context: dataSendEmail.context,
      });

      const emailLog = {} as EmailLogInput;
      (emailLog.recipientEmail = dataSendEmail.to),
        (emailLog.senderEmail = this.getDefaultSenderEmail()),
        (emailLog.emailType = 'sendEmail_newVersionBook'),
        (emailLog.emailSubject = dataSendEmail.subject),
        (emailLog.sendDate = dataSendEmail.sendDate),
        (emailLog.status = dataSendEmail.status),
        (emailLog.additionalInfo = JSON.stringify(data));

      const emailLogInsert = await this.emailsService.insertEmailLog(emailLog);
      return true;
    } catch (e) {
      const emailLog = {} as EmailLogInput;
      (emailLog.recipientEmail = dataSendEmail.to),
        (emailLog.senderEmail = this.getDefaultSenderEmail()),
        (emailLog.emailType = 'sendEmail_newVersionBook'),
        (emailLog.emailSubject = dataSendEmail.subject),
        (emailLog.sendDate = dataSendEmail.sendDate),
        (emailLog.status = 'error'),
        (emailLog.errorMessage = e.message),
        (emailLog.additionalInfo = JSON.stringify(data));
      const emailLogInsert = await this.emailsService.insertEmailLog(emailLog);
      return false;
    }
  }

  getDefaultSenderEmail(): string {
    return process.env.MAIL_USER;
  }
}
