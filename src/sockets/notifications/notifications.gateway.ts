import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Injectable } from '@nestjs/common';
import { NotificationSocketService } from './notifications.service';

@Injectable()
@WebSocketGateway(parseInt(process.env.PORT_SOCKET), {
  namespace: 'notifications',
  cors: true,
})
export class NotificationsGateway {
  @WebSocketServer()
  server: Server;

  constructor(
    private readonly notificationSocketService: NotificationSocketService,
  ) {}

  notifyClient(peopleId: string, state: any): void {
    this.notificationSocketService.notifyClient(this.server, peopleId, state);
  }
}
