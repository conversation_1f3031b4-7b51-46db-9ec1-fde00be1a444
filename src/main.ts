import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

import * as express from 'express';

import * as graphqlUploadExpress from 'graphql-upload/graphqlUploadExpress.js';

// import { WsAdapter } from '@nestjs/platform-ws';

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    snapshot: true,
  });
  // app.useWebSocketAdapter(new WsAdapter(app));
  app.use(express.json({ limit: '5mb' }));
  // app.use(graphqlUploadExpress({ maxFileSize: 100000000, maxFiles: 10 }));
  app.use(
    '/graphql',
    graphqlUploadExpress({ maxFileSize: 10000000, maxFiles: 10 }),
  );

  app.enableCors({
    origin: [
      process.env.HOST_CLIENT ||
        'http://localhost:4200' ||
        'http://localhost:4201',
      '*************',
      '*************',
      'http://localhost:4201',
      'https://admin.economixhub.com',
    ],
    methods: ['GET', 'POST', 'PUT', 'OPTIONS'],
    credentials: true,
    allowedHeaders: [
      'Content-Type', // Necesario para el tipo de contenido de las solicitudes, como application/json, application/graphql, multipart/form-data, etc.
      'Authorization', // Esencial para pasar tokens JWT u otros esquemas de autorización.
      'X-Requested-With', // Útil para identificar solicitudes AJAX.
      'Accept', // Permite al servidor saber qué tipos de contenido puede enviar el cliente.
      'Origin', // Utilizado en la verificación CORS para identificar el dominio solicitante.
      'Access-Control-Allow-Credentials', // Necesario si tu aplicación necesita enviar cookies o información de autenticación en solicitudes de origen cruzado.
      'X-CSRF-Token',
      'X-XSRF-Token', // Importante si estás implementando protección contra CSRF/XSRF.
      'apollo-require-preflight', // Específico para operaciones avanzadas de Apollo si es necesario.
      'Cache-Control', // Puede ser útil para controlar la caché en el lado del cliente.
      'X-Content-Type-Options', // Ayuda a prevenir ataques basados en MIME-type sniffing.
      'X-Frame-Options', // Puede ser relevante si estás controlando el enmarcado de tu contenido.
      'X-App-Version', // Ejemplo de un encabezado personalizado que podrías necesitar para control de versiones de la app cliente.
    ],
  });

  await app.listen(process.env.PORT, '0.0.0.0');
}
bootstrap();
