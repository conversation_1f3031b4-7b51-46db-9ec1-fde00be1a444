import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthServiceJwt } from './auth.service';
import { JWT_CONFIG } from '../config/auth.config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Credentials } from './../v1/credentials/entities/credentials.entity';
import { JwtResolver } from './auth.resolver';
import { Admin } from 'src/v1/admins/entities/admin.entity';

import { JwtStrategy } from './strategies/jwt.strategy';
// import { JwtAdminStrategy } from './strategies/jwt-admim.strategy';

import { JwtAuthGuard } from './guards/auth.guard';
// import { JwtAuthAdminGuard } from './guards/authAdmin.guard';
import { People } from 'src/v1/peoples/entities/people.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Credentials, Admin, People]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.register(JWT_CONFIG),
  ],
  providers: [
    JwtResolver,
    JwtAuthGuard,
    // JwtAdminStrategy,
    AuthServiceJwt,
    JwtStrategy,
    // JwtAdminStrategy,
  ],
  exports: [AuthServiceJwt, JwtAuthGuard, 
    // JwtAdminStrategy
  ],
})
export class AuthModule {}
