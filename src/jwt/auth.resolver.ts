import { Resolver, Query, Context } from '@nestjs/graphql';

import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/jwt/guards/auth.guard';
import { Jwt } from './entities/jwt.entity';
import { AuthServiceJwt } from './auth.service';
import { JwtOutput } from './dto/jwt.output';
import { AuthGuard } from '@nestjs/passport';
// import { JwtAuthAdminGuard } from './guards/authAdmin.guard';
import { RolesGuard } from 'src/jwt/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';

@Resolver(() => Jwt)
export class JwtResolver {
  constructor(private readonly authServiceJwt: AuthServiceJwt) {}

  @UseGuards(JwtAuthGuard)
  @Query(() => JwtOutput, { name: 'refreshTokenJWT' })
  async refreshToken(@Context() context): Promise<JwtOutput> {
    // console.log("refreshToaken")
    // console.log(context.req.user);
    return {
      token: `Bearer ${await this.authServiceJwt.createCredentials(
        // {
        //   id: context.req.user.sub,
        //   rol: 'user',
        // }
        context.req.user
      )}` as string,
    };
  }

  // @UseGuards(JwtAuthAdminGuard)
@UseGuards(RolesGuard)
@Roles('admin')
  @Query(() => JwtOutput, { name: 'refreshTokenJWTAdmin' })
  async refreshTokenAdmin(@Context() context): Promise<JwtOutput> {
    return {
      token: `Bearer ${await this.authServiceJwt.createCredentials(
        // {
        //   id: context.req.user.sub,
        //   rol: 'admin',
        // }
        context.req.user
      )}` as string,
    };
  }
}
