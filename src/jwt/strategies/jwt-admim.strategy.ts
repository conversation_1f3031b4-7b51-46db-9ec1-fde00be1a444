// import { PassportStrategy } from '@nestjs/passport';
// import { ExtractJwt, Strategy } from 'passport-jwt';
// import { Admin } from '../../v1/admins/entities/admin.entity';
// import { People } from '../../v1/peoples/entities/people.entity';

// import { InjectRepository } from '@nestjs/typeorm';
// import { IsNull, Repository } from 'typeorm';

// export class JwtAdminStrategy extends PassportStrategy(Strategy, 'jwtAdmin') {
//   constructor(
//     @InjectRepository(Admin)
//     private adminRepository: Repository<Admin>,
//     @InjectRepository(People)
//     private peopleRepository: Repository<People>,
//   ) {
//     super({
//       jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
//       ignoreExpiration: false,
//       secretOrKey: process.env.JWT_SECRET_KEY,
//     });
//   }

//   async validate(payload: any) {
//     console.log('jwt-admin-strategy');
//     console.log(payload);
//     // if (payload.rol == 'admin') {
//     //   // console.log(payload);
//     //   const admin = await this.adminRepository.findOne({
//     //     where: {
//     //       id: payload?.id,
//     //       deletedAt: IsNull(),
//     //     },
//     //   });

//     //   const people = await this.peopleRepository.findOne({
//     //     where: {
//     //       id: admin.peopleId,
//     //       deletedAt: IsNull(),
//     //     },
//     //   });

//     //   // console.log(people);

//     //   if (admin && people) {
//     //     return { id: admin.id, rol: payload.rol };
//     //   }
//     //   return false;
//     // }
//     delete payload.iat;
//     delete payload.exp;
//     console.log("new payload")
//     console.log(payload)
//     return payload;
//   }
// }
