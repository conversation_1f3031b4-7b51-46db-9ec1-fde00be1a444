import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { Credentials } from 'src/v1/credentials/entities/credentials.entity';

import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import { People } from 'src/v1/peoples/entities/people.entity';

export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(

    @InjectRepository(People)
    private peopleRepository: Repository<People>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true,
      secretOrKey: process.env.JWT_SECRET_KEY,
    });
  }

  async validate(payload: any) {
    // if (payload.rol == 'user') {
    //   const people = await this.peopleRepository.findOne({
    //     where: {
    //       id: payload?.id,
    //       deletedAt: IsNull(),
    //     },
    //   });

    //   if (people) {
    //     return { id: people.id, rol: payload.rol };
    //   }
    //   return false;
    // } else {
    //   return false;
    // }
    delete payload.iat;
    delete payload.exp;
    console.log("payload");
    console.log(payload);
    return payload;
  }
}
