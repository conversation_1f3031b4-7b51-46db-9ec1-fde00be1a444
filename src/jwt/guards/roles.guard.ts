import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { ROLES_KEY } from 'src/common/decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<string[]>(ROLES_KEY, context.getHandler());
    console.log("requiredRoles", requiredRoles)
    if (!requiredRoles) {
      return true;
    }

    // const request = context.switchToHttp().getRequest();
    // console.log("request", request)
    // const user = request.user; // Asumiendo que el payload del token JWT está en request.user
    const ctx = GqlExecutionContext.create(context);
    const user = ctx.getContext().req.user;
    console.log("user", user)
    // const roles=user.roles;

    const hasRole = requiredRoles.some((role) => user.roles?.includes(role));
    if (!hasRole) {
      throw new UnauthorizedException('Acceso denegado: roles insuficientes');
    }

    return true;
  }
}
