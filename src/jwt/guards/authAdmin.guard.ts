// import { Injectable, ExecutionContext } from '@nestjs/common';
// import { AuthGuard } from '@nestjs/passport';
// import { GqlExecutionContext } from '@nestjs/graphql';
// import { AuthenticationError } from 'apollo-server-core';

// @Injectable()
// export class JwtAuthAdminGuard extends AuthGuard('jwtAdmin') {
//   canActivate(context: ExecutionContext) {
//     return super.canActivate(context);
//   }
//   getRequest(context: ExecutionContext) {
//     const ctx = GqlExecutionContext.create(context);
//     const request = ctx.getContext().req;
//     return request;
//   }

//   handleRequest(err: any, payload: any, info: any) {
//     console.log('payload', payload);
//     console.log('err', err);
//     if (err || !payload) {
//       throw err || new AuthenticationError('Could not authenticate with token');
//     }
//     return payload;
//   }
// }
