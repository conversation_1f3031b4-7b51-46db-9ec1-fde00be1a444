import { Injectable, ExecutionContext, ContextType } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthenticationError } from 'apollo-server-core';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from 'src/common/decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {

  constructor(private readonly reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    // return super.canActivate(context);
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      // return true;
      try {
        return super.canActivate(context);
      } catch (error) {
        // En caso de error, simplemente permitimos el acceso, asumiendo que es una ruta pública
        return true;
      }
    }
    return super.canActivate(context); 
  }
  getRequest(context: ExecutionContext) {
    // const ctx = GqlExecutionContext.create(context);
    // const request = ctx.getContext().req;
    // return request;
    if (context.getType<ContextType | 'graphql'>() === 'graphql') {
      return GqlExecutionContext.create(context).getContext().req;
    }
    return context.switchToHttp().getRequest();
  }

  // handleRequest(err: any, payload: any, info: any) {
  //   if (err || !payload) {
  //     throw err || new AuthenticationError('Could not authenticate with token');
  //   }
  //   return payload;
  // }
  handleRequest(err: any, user: any, info: any, context: ExecutionContext, status?: any) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Para rutas públicas, no lanzamos un error si hay un problema de autenticación.
    if (isPublic && (!user || err)) {
      return {};
    }

    if (err || !user) {
      throw err || new AuthenticationError('Could not authenticate with token');
    }
    
    return user;
  }
}
