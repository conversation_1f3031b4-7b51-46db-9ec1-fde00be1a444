import { Module } from '@nestjs/common';
import { BankService } from './bank.service';
import { BankController } from './bank.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Shopping } from 'src/v1/shopping/entities/shopping.entity';
import { Products } from 'src/v1/products/entities/products.entity';
import { Version } from 'src/v1/versions/entities/version.entity';
import { MailModule } from 'src/mail/mail.module';
import { People } from 'src/v1/peoples/entities/people.entity';
import { Book } from 'src/v1/books/entities/book.entity';
import { VersionSubscriptionPlansModule } from 'src/v1/version-subscription-plans/version-subscription-plans.module';
import { SubscriptionPlansModule } from 'src/v1/subscription-plans/subscription-plans.module';
import { SocketModule } from 'src/sockets/notifications/notifications.module';
import { BankRequestLogsModule } from 'src/v1/bank-request-logs/bank-request-logs.module';
import { PeoplesModule } from 'src/v1/peoples/peoples.module';
import { VersionsModule } from 'src/v1/versions/versions.module';
import { ProductModule } from 'src/v1/products/products.module';
import { AttachedFilesModule } from 'src/v1/attached-files/attached-files.module';

@Module({
  exports: [BankService],
  imports: [
    TypeOrmModule.forFeature([Shopping, Products, Version, People, Book]),
    VersionSubscriptionPlansModule,
    SubscriptionPlansModule,
    MailModule,
    SocketModule,
    BankRequestLogsModule,
    PeoplesModule,
    VersionsModule,
    ProductModule,
    AttachedFilesModule,
  ],
  controllers: [BankController],
  providers: [BankService],
})
export class BankModule {}
