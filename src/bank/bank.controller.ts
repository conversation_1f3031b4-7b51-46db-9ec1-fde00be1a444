import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { BankService } from './bank.service';
import { CreateBankDto } from './dto/create-bank.dto';
import { UpdateBankDto } from './dto/update-bank.dto';
import { Public } from 'src/common/decorators/public.decorator';

@Controller('bank')
export class BankController {
  constructor(private readonly bankService: BankService) {}

  @Public()
  @Post('ReceiveNotification')
  create(@Body() createBankDto: CreateBankDto) {
    return this.bankService.ReceiveNotification(createBankDto);
  }
}
