import { Injectable } from '@nestjs/common';
import { CreateBankDto } from './dto/create-bank.dto';
import { UpdateBankDto } from './dto/update-bank.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { EnumPaymentStatus, Shopping } from 'src/v1/shopping/entities/shopping.entity';
import { IsNull, Repository } from 'typeorm';
import { Products } from 'src/v1/products/entities/products.entity';
import { Version } from 'src/v1/versions/entities/version.entity';
import { MailService } from 'src/mail/mail.service';
import { People } from 'src/v1/peoples/entities/people.entity';
import { AttachmentInput } from 'src/mail/dto/attachments-input';
import { Book } from 'src/v1/books/entities/book.entity';
import { VersionSubscriptionPlansService } from 'src/v1/version-subscription-plans/version-subscription-plans.service';
import { SubscriptionPlansService } from 'src/v1/subscription-plans/subscription-plans.service';
import { NotificationsGateway } from './../sockets/notifications/notifications.gateway';
import { InsertBankRequestLogInput } from 'src/v1/bank-request-logs/dto/bank-request-log.input';
import {
  EnumBankName,
  EnumTypeLog,
} from 'src/v1/bank-request-logs/entities/bank-request-logs.entity';
import { BankRequestLogsService } from 'src/v1/bank-request-logs/bank-request-logs.service';
import { PeoplesService } from 'src/v1/peoples/peoples.service';
import { VersionsService } from 'src/v1/versions/versions.service';
import { ProductsService } from 'src/v1/products/products.service';
import { Operaciones } from 'src/@core/utils/operations';
import { AttachedFilesService } from 'src/v1/attached-files/attached-files.service';
import { EnumNombreTabla_AttachedFile } from 'src/v1/attached-files/entities/attached-file.entity';

@Injectable()
export class BankService {
  private url_api_token: string;
  private url_api_getQRWithImageAsync: string;
  private url_api_cancelQRByIdAsync: string;
  opAux=Operaciones;

  constructor(
    @InjectRepository(Shopping)
    private shoppingRepository: Repository<Shopping>,
    @InjectRepository(Products)
    private productsRepository: Repository<Products>,

    private productsService: ProductsService,

    // @InjectRepository(People)
    // private peopleRepository: Repository<People>,
    private peopleService: PeoplesService,

    // @InjectRepository(Version)
    // private versionRepository: Repository<Version>,
    private versionsService:VersionsService,

    @InjectRepository(Book)
    private bookRepository: Repository<Book>,

    private mailService: MailService,
    private versionSubscriptionPlanService: VersionSubscriptionPlansService,
    private subscriptionPlanService: SubscriptionPlansService,

    private bankRequestLogService: BankRequestLogsService,

    private notificationSocketGateway: NotificationsGateway,
    private readonly attached_filesService: AttachedFilesService,
  ) {
    this.url_api_token = '/ClientAuthentication.API/api/v1/auth/token';
    this.url_api_getQRWithImageAsync =
      '/QRSimple.API/api/v1/main/getQRWithImageAsync';

    this.url_api_cancelQRByIdAsync =
      '/QRSimple.API/api/v1/main/CancelQRByIdAsync';
  }

  async ReceiveNotification(createBankDto: CreateBankDto) {
    const shoppingData = createBankDto?.Gloss
      ? await this.shoppingRepository.findOne({
          where: {
            id: createBankDto?.Gloss.split('EconomixHub-')[1],
            deletedAt: IsNull(),
          },
        })
      : null;
    try {
      if (!shoppingData) throw new Error('The qr is not valid.');

      // let productsData = await this.productsRepository.find({
      //   where: { shopping: { id: shoppingData.id }, deletedAt: IsNull() },
      // });
      let productsData= await this.productsService.getProducts_ByShoppingId(shoppingData.id);
      console.log("productsData");
      console.log(productsData);

      // const versionData = await this.versionRepository.find({
      //   where: { deletedAt: IsNull() },
      // });
      // const versionData= await this.versionsService.getVersions();

      const updateDataState = await Promise.all(
        productsData.map(async (val) => {
          const versionSubscriptionPlan =
            await this.versionSubscriptionPlanService.getVersionSubscriptionPlan_ById(
              val.versionSubscriptionsPlansId,
            );

          const subscriptionPlan =
            await this.subscriptionPlanService.getSubscriptionPlan_ById(
              versionSubscriptionPlan.subscriptionPlanId,
            );

          // const version = versionData.filter((x) => x.id === val.versionId)[0];
          let date = new Date();
          let newDateEnd = new Date(date);
          newDateEnd.setDate(date.getDate() + subscriptionPlan.duration_days);

          const result =
            (
              await this.productsRepository.update(
                { id: val.id, deletedAt: IsNull() },
                {
                  dateStart: date, 
                  dateEnd: newDateEnd,
                },
              )
            ).affected === 1;
          return result;
        }),
      );

      productsData = await this.productsRepository.find({
        where: { shopping: { id: shoppingData.id }, deletedAt: IsNull() },
      });

      if (!updateDataState.every((x) => x))
        throw new Error('an unexpected error occurred');

      // const peopleData = await this.peopleRepository.findOne({
      //   where: { id: shoppingData.peopleId, deletedAt: IsNull() },
      // });

      const peopleData= await this.peopleService.getPersona_ById(shoppingData.peopleId);

      const attachmentsData: AttachmentInput[] = await this.getAllAttachments(productsData);
      // const attachmentsData: AttachmentInput[] = await Promise.all(
      //   productsData.map(async (val) => {
      //     // const versionData = await this.versionRepository.findOne({
      //     //   where: { id: val.versionId, deletedAt: IsNull() },
      //     // });
      //     // const versionData= await this.versionsService.findOne( val.versionId)
      //     const docs_version= await this.attached_filesService.getArchivosAdjuntos_Registro(
      //       EnumNombreTabla_AttachedFile.VERSIONES,
      //       val.versionId
      //     )
      //     // const bookData = await this.bookRepository.findOne({
      //     //   where: { id: versionData.bookId, deletedAt: IsNull() },
      //     // });

      //     const attachments: AttachmentInput[] = await Promise.all(docs_version.map(async (doc) => {
      //       const file = await doc.file;
      //       return {
      //         filename: file.filename,
      //         path: "./files/archivos/" + file.originalname,
      //         contentDisposition: 'attachment', 
      //       } as AttachmentInput;
      //     }));
      //     return attachments;
      //   }),
      // );

      const productsDetail = await Promise.all(
        productsData.map(async (val) => {
          // const versionData = await this.versionRepository.findOne({
          //   where: { id: val.versionId, deletedAt: IsNull() },
          // });
          const versionData= await this.versionsService.findOne( val.versionId)
          const bookData = await this.bookRepository.findOne({
            where: { id: versionData.bookId, deletedAt: IsNull() },
          });

          return {
            name: bookData.title,
            ...val,
            price: val.price.toLocaleString('es', {
              style: 'currency',
              currency: 'BOB',
            }),
            priceWithDiscount: val.priceWithDiscount.toLocaleString('es', {
              style: 'currency',
              currency: 'BOB',
            }),
            dateEnd: `${new Date(val.dateEnd).toLocaleDateString()} ${new Date(
              val.dateEnd,
            ).toLocaleTimeString()}`,
          };
        }),
      );

      const activeAccount = await this.shoppingRepository.update(
        {
          id: shoppingData.id,
          deletedAt: IsNull(),
        },
        {
          paymentStatus:EnumPaymentStatus.PAGO_CONFIRMADO,
          paymentMade: true,
          qrPaymentBankNotification: createBankDto,
        },
      );

      let total = productsData.reduce((acum, value) => acum + value.price, 0);
      total= this.opAux.roundNumber(total,2);
      let totalDiscounted = productsData.reduce(
        (acum, value) => acum + value.priceWithDiscount,
        0,
      );
      totalDiscounted= this.opAux.roundNumber(totalDiscounted,2);

      const discounted = total - totalDiscounted;

      if (activeAccount.affected === 1)
        await this.mailService.confirmationPurchase(
          peopleData,
          attachmentsData,
          productsDetail,
          totalDiscounted.toLocaleString('es', {
            style: 'currency',
            currency: 'BOB',
          }),
          discounted.toLocaleString('es', {
            style: 'currency',
            currency: 'BOB',
          }),
        );

      this.notificationSocketGateway.notifyClient(shoppingData.peopleId, {
        state: true,
      });
      return {
        success: true,
        message: 'OK',
      };
    } catch (e) {
      console.log(e);
      if(shoppingData){
        this.notificationSocketGateway.notifyClient(shoppingData.peopleId, {
          state: false,
        });
      }
      return {
        success: false,
        message: e.message,
      };
    }
  }

  // Función para obtener los archivos adjuntos para un solo producto.
  async getAttachmentsForProduct(product): Promise<AttachmentInput[]>{
    const docs_version = await this.attached_filesService.getArchivosAdjuntos_Registro(
      EnumNombreTabla_AttachedFile.VERSIONES,
      product.versionId
    );
    
    // Mapea los documentos a sus attachments y retorna el resultado.
    return await Promise.all(docs_version.map(async (doc) => {
      const file = await doc.file;
      return {
        filename: file.originalname,
        path: "./files/archivos/" + file.filename,
        contentDisposition: 'attachment', 
      } as AttachmentInput;
    }));
  }

  // Función principal para obtener todos los attachments para todos los productos.
  async getAllAttachments(productsData): Promise<AttachmentInput[]>{
    let attachmentsData: AttachmentInput[] = [];

    for (const product of productsData) {
      const attachments = await this.getAttachmentsForProduct(product);
      attachmentsData = attachmentsData.concat(attachments); // Concatena los nuevos attachments al array existente.
    }

    return attachmentsData;
  }

  async GetToken(): Promise<any> {
    console.log('get token');
    try {
      const response = await fetch(
        `${process.env.BNB_API_ROOT}${this.url_api_token}`,
        {
          method: 'POST',
          cache: 'no-cache',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            accountId: process.env.BNB_API_ACCOUNTID,
            authorizationId: process.env.BNB_AUTHORIZATIONID,
          }),
        },
      );

      if (!response.ok) {
        const fullResponse = {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          url: response.url,
          type: response.type,
          ok: response.ok,
          body: await response.text(), // Obtén el cuerpo como texto
        };

        let bankRequestLog: InsertBankRequestLogInput = {
          bank_name: EnumBankName.BNB,
          api_endpoint: this.url_api_token,
          type_log: EnumTypeLog.ERROR,
          show_message: 'Error al obtener el token',
          data: JSON.stringify(fullResponse),
        };

        await this.bankRequestLogService.insertBankRequestLog(bankRequestLog);
        throw new Error('Error al obtener el token');
      }
      return await response.json();
    } catch (e) {
      let bankRequestLog: InsertBankRequestLogInput = {
        bank_name: EnumBankName.BNB,
        api_endpoint: this.url_api_token,
        type_log: EnumTypeLog.ERROR,
        show_message: 'Ocurrio un error al obtener el token',
        data: JSON.stringify(e.message),
      };

      await this.bankRequestLogService.insertBankRequestLog(bankRequestLog);

      // console.log(e);
    }
  }

  async GetQRasync(bodyQR: any): Promise<any> {
    console.log('get qr');
    const authBNB: any = await this.GetToken();
    console.log('bodyQR');
    console.log(bodyQR);
    console.log('authBNB');
    console.log(authBNB);
    console.log(authBNB.message);

    try {
      const response: any = await fetch(
        `${process.env.BNB_API_ROOT}${this.url_api_getQRWithImageAsync}`,
        {
          method: 'POST',
          cache: 'no-cache',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + authBNB?.message,
          },
          body: JSON.stringify(bodyQR),
        },
      );
      // console.log(res);
      if (!response.ok) {
        const fullResponse = {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          url: response.url,
          type: response.type,
          ok: response.ok,
          body: await response.text(), // Obtén el cuerpo como texto
        };

        let bankRequestLog: InsertBankRequestLogInput = {
          bank_name: EnumBankName.BNB,
          api_endpoint: this.url_api_getQRWithImageAsync,
          type_log: EnumTypeLog.ERROR,
          show_message: 'Error al obtener el QR',
          data: JSON.stringify(fullResponse),
        };

        await this.bankRequestLogService.insertBankRequestLog(bankRequestLog);
        throw new Error('Error al generar el QR');
      }

      return await response.json();
    } catch (e) {
      // console.log(e);

      let bankRequestLog: InsertBankRequestLogInput = {
        bank_name: EnumBankName.BNB,
        api_endpoint: this.url_api_getQRWithImageAsync,
        type_log: EnumTypeLog.ERROR,
        show_message: 'Ocurrio un error al generar el QR',
        data: JSON.stringify(e.message),
      };

      await this.bankRequestLogService.insertBankRequestLog(bankRequestLog);

      throw new Error('Ocurrio un error al generar el QR');
    }
  }

  async CancelQRByIdAsync(qrId: string): Promise<any> {
    console.log('cancel qr');
    // console.log(JSON.stringify({ qrId }));

    const authBNB: any = await this.GetToken();
    try {
      const response = await fetch(
        `${process.env.BNB_API_ROOT}${this.url_api_cancelQRByIdAsync}`,
        {
          method: 'POST',
          cache: 'no-cache',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + authBNB?.message,
          },
          body: JSON.stringify({ qrId }),
        },
      );

      if (!response.ok) {
        const fullResponse = {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          url: response.url,
          type: response.type,
          ok: response.ok,
          body: await response.text(), // Obtén el cuerpo como texto
        };

        let bankRequestLog: InsertBankRequestLogInput = {
          bank_name: EnumBankName.BNB,
          api_endpoint: this.url_api_cancelQRByIdAsync,
          type_log: EnumTypeLog.ERROR,
          show_message: 'Error al cancelar el QR',
          data: JSON.stringify(fullResponse),
        };

        await this.bankRequestLogService.insertBankRequestLog(bankRequestLog);
        throw new Error('Error al cancelar el QR');
      }

      return await response.json();
    } catch (e) {
      let bankRequestLog: InsertBankRequestLogInput = {
        bank_name: EnumBankName.BNB,
        api_endpoint: this.url_api_cancelQRByIdAsync,
        type_log: EnumTypeLog.ERROR,
        show_message: 'Ocurrio un error al cancelar el QR',
        data: JSON.stringify(e.message),
      };

      await this.bankRequestLogService.insertBankRequestLog(bankRequestLog);

      // console.log(e);
    }
  }
}
