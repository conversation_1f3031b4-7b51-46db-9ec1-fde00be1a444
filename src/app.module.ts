import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';

//modules
import { SimulatorsModule } from './v1/simulators/simulators.module';
import { FormulasModule } from './v1/formulas/formulas.module';
import { SheetsModule } from './v1/sheets/sheets.module';

//graphql
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';

//typeorm
import { TypeOrmModule } from '@nestjs/typeorm';

//others
import { join } from 'path';
import databaseConfig from './config/database.config';

//ConfigModule
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

import { PeoplesModule } from './v1/peoples/peoples.module';
import { OauthModule } from './oauth/oauth.module';
import { <PERSON><PERSON><PERSON><PERSON>ontroller } from './oauth/oauth.controller';
// import { OauthAdminController } from './oauthAdmin/oauth.controller';
// import { OauthAdminModule } from './oauthAdmin/oauth.module';
import { AdminsModule } from './v1/admins/admins.module';
import { CredentialsModule } from './v1/credentials/credentials.module';

import { AuthModule } from './jwt/auth.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { MailModule } from './mail/mail.module';
import { BooksModule } from './v1/books/books.module';
import { VersionsModule } from './v1/versions/versions.module';
import { ShoppingModule } from './v1/shopping/shopping.module';
import { CouponsModule } from './v1/coupons/coupons.module';

// import { graphqlUploadExpress } from 'graphql-upload';
import { ProductModule } from './v1/products/products.module';
import { BankModule } from './bank/bank.module';
import { ChaptersModule } from './v1/chapters/chapters.module';
import { SubscriptionPlansModule } from './v1/subscription-plans/subscription-plans.module';
import { VersionSubscriptionPlansModule } from './v1/version-subscription-plans/version-subscription-plans.module';
import { CommentsModule } from './v1/comments/comments.module';
import { ThrottlerModule } from '@nestjs/throttler';

import { SocketModule } from './sockets/notifications/notifications.module';
import { BankRequestLogsModule } from './v1/bank-request-logs/bank-request-logs.module';
import { SoftDeleteModule } from './v1/soft-delete/soft-delete.module';
import { RolesModule } from './v1/roles/roles.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './jwt/guards/auth.guard';
import { DevtoolsModule } from '@nestjs/devtools-integration';
import { FilesModule } from './v1/files/files.module';
import { AttachedFilesModule } from './v1/attached-files/attached-files.module';
import { EmailLogsModule } from './v1/email-logs/email-logs.module';
import { EconomixFormModule } from './v1/economix-form/economix-form.module';
import { CouponBookPersonalizedsModule } from './v1/coupon-book-personalizeds/coupon-book-personalizeds.module';
import { ImportChapterSimulatorToVersionModule } from './v1/import-chapter-simulator-to-version/import-chapter-simulator-to-version.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 10,
      },
    ]),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      // resolvers: {},
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
    }),

    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
    }),

    TypeOrmModule.forRoot(databaseConfig()),
    DevtoolsModule.register({
      http: process.env.NODE_ENV !== 'production',
    }),
    SimulatorsModule,
    FormulasModule,
    SheetsModule,
    JwtModule,
    PeoplesModule,
    OauthModule,
    // OauthAdminModule,
    AdminsModule,
    CredentialsModule,

    AuthModule,
    MailModule,
    BooksModule,
    VersionsModule,
    ShoppingModule,
    CouponsModule,
    ProductModule,
    BankModule,
    ChaptersModule,
    SubscriptionPlansModule,
    VersionSubscriptionPlansModule,
    CommentsModule,
    SocketModule,
    BankRequestLogsModule,
    SoftDeleteModule,
    RolesModule,
    FilesModule,
    AttachedFilesModule,
    EmailLogsModule,
    EconomixFormModule,
    CouponBookPersonalizedsModule,
    ImportChapterSimulatorToVersionModule,
  ],

  controllers: [
    AppController,
    OauthController,
    // OauthAdminController
  ],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AppModule {}
