import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

//entities
import { Formula } from './../v1/formulas/entities/formula.entity';
import { Simulator } from './../v1/simulators/entities/simulator.entity';
import { Sheet } from './../v1/sheets/entities/sheet.entity';
import { Admin } from './../v1/admins/entities/admin.entity';
import { Credentials } from './../v1/credentials/entities/credentials.entity';
import { People } from './../v1/peoples/entities/people.entity';

import { Book } from './../v1/books/entities/book.entity';
import { Version } from './../v1/versions/entities/version.entity';
import { Shopping } from './../v1/shopping/entities/shopping.entity';
import { Coupon } from 'src/v1/coupons/entities/coupon.entity';
import { Products } from './../v1/products/entities/products.entity';
import { AdminCreateDefault1631646226711 } from './../migrations/defaultAdmin';

// import * as path from 'path';
import { Chapter } from 'src/v1/chapters/entities/chapter.entity';
import { VersionSubscriptionPlan } from 'src/v1/version-subscription-plans/entities/version-subscription-plan.entity';
import { SubscriptionPlan } from 'src/v1/subscription-plans/entities/subscription-plan.entity';
import { Comment } from 'src/v1/comments/entities/comment.entity';
import { BankRequestLog } from 'src/v1/bank-request-logs/entities/bank-request-logs.entity';
import Rol from 'src/v1/roles/entities/rol.entity';
import AttachedFile from 'src/v1/attached-files/entities/attached-file.entity';
import File from 'src/v1/files/entities/file.entity';
import { EmailLog } from 'src/v1/email-logs/entities/email-log.entity';
import { CouponBookPersonalized } from 'src/v1/coupon-book-personalizeds/entities/coupon-book-personalized.entity';

export default registerAs(
  'database',
  (): TypeOrmModuleOptions => ({
    type: 'mysql',
    host: process.env.DB_HOST,
    port: +process.env.DB_PORT,

    entities: [
      Formula,
      Simulator,
      Sheet,
      People,
      Admin,
      Credentials,
      Chapter,
      Book,
      Version,
      Shopping,
      Coupon,
      Products,
      VersionSubscriptionPlan,
      SubscriptionPlan,
      Comment,
      BankRequestLog,
      Rol,
      AttachedFile,
      File,
      EmailLog,
      CouponBookPersonalized
    ],
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    migrations: [AdminCreateDefault1631646226711],
    synchronize: true,
    migrationsRun: true,
    logging: true,
    logger: 'file',
  }),
);
