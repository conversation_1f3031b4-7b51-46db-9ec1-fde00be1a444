mutation {
  createAllsfs(createAllsfsInput: {
  name:"Simulador oferta y demanda.",
  tables:[
    {
      name: "tabla1",
      show: true,
      data: [
        [
          {
            value: "FUNCIONES",
            columnName: "A",
            editable: false,
            position: [0, 0],
            row:1,
            show: true
          },
        ],
        [
          {
            value: "Qxd= - PenD(Px) + CaD ",
            columnName: "A",
            editable: false,
            position: [1, 0],
            row:2,
            show: true
          },
        ],
        [
          {
            value: "Qxo= PenO(Px) + CaO",
            columnName: "A",
            editable: false,
            position: [2, 0],
            row:3,
            show: true
          },
        ],
      ],
    },
  
    {
      name: "tabla2",
      show: true,
      data: [
        [
          { value: "PenD", columnName: "A", editable: false, position: [0, 0] , row:1,show: true},
          { value: "5", columnName: "B", editable: true, position: [0, 1], row:1,show: true },
          { value: "3", columnName: "C", editable: true, position: [0, 2] , row:1,show: true},
        ],
        [
          { value: "CaD", columnName: "A", editable: false, position: [0, 0] , row:2,show: true},
          { value: "160", columnName: "B", editable: true, position: [0, 1], row:2,show: true },
          { value: "160", columnName: "C", editable: true, position: [0, 2], row:2,show: true},
        ],
        [
          { value: "PenO", columnName: "A", editable: false, position: [0, 0] , row:3,show: true},
          { value: "2", columnName: "B", editable: true, position: [0, 1], row:3,show: true },
          { value: "2", columnName: "C", editable: true, position: [0, 2], row:3 ,show: true},
        ],
        [
          { value: "CaO", columnName: "A", editable: false, position: [0, 0], row:4,show: true },
          { value: "20", columnName: "B", editable: true, position: [0, 1], row:4,show: true },
          { value: "20", columnName: "C", editable: true, position: [0, 2], row:4,show: true },
        ],
      ],
    },
  
    {
      name: "tabla3",
      show: true,
      data: [
        [
          { value: "Px1", columnName: "A", editable: false, position: [0, 0], row:1,show: true },
          { value: "Qxd1", columnName: "B", editable: false, position: [0, 1], row:1,show: true },
          { value: "Qxo1", columnName: "C", editable: false, position: [0, 2], row:1,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:2,show: true  },
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:2,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2] , row:2 ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:3 ,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:3 ,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:3 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:4  ,show: true},
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:4 ,show: true},
          { value: "", columnName: "C", editable: false, position: [0, 2] , row:4 ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:5 ,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:5,show: true  },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:5 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:6,show: true  },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:6 ,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:6 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:7,show: true  },
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:7,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:7,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0] , row:8,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:8 ,show: true},
          { value: "", columnName: "C", editable: false, position: [0, 2] , row:8 ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:9 ,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:9,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:9  ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:10,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:10 ,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2] , row:10,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0] , row:11,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:11,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:11 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0] , row:12,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:12,show: true  },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:12 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:13 ,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:13,show: true  },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:13,show: true  },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0] , row:14 ,show: true},
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:14,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:14 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0] , row:15,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:15 ,show: true},
          { value: "", columnName: "C", editable: false, position: [0, 2] , row:15 ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:16 ,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:16 ,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:16 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0] , row:17 ,show: true},
          { value: "", columnName: "B", editable: false, position: [0, 1], row:17 ,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2], row:17 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [0, 0], row:18 ,show: true },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:18 ,show: true },
          { value: "", columnName: "C", editable: false, position: [0, 2] , row:18,show: true },
        ],
      ],
    },
  
    {
      name: "tabla4",
      show: true,
      data: [
        [
          {
            value: "Ex. Con.",
            columnName: "A",
            editable: false,
            position: [0, 0],
            row:1
            ,show: true
          },
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:1 ,show: true},
        ],
        [
          {
            value: "Ex. Pro.",
            columnName: "A",
            editable: false,
            position: [0, 0]
            , row:2,show: true
          },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:2 ,show: true },
        ],
        [
          { value: "EPD", columnName: "A", editable: false, position: [0, 0] , row:3,show: true},
          { value: "", columnName: "B", editable: false, position: [0, 1] , row:3,show: true },
        ],
        [
          { value: "EPO", columnName: "A", editable: false, position: [0, 0], row:4,show: true  },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:4  ,show: true},
        ],
      ],
    },
  
    {
      name: "tabla5",
      show: true,
      data: [
        [
          {
            value: "Gt = It (It empresa) ",
            columnName: "A",
            editable: false,
            position: [0, 0]
            , row:1 ,show: true
          },
          { value: "", columnName: "B", editable: false, position: [0, 1], row:1 ,show: true },
        ],
      ],
    },
    {
      name: "tabla6",
      show: false,
      data: [
        [
          { value: "Px0", columnName: "A", editable: false, position: [0, 0] , row:1,show: true},
          { value: "Qxd0", columnName: "B", editable: false, position: [0, 1] , row:1,show: true },
          { value: "Qxo0", columnName: "C", editable: false, position: [0, 2] , row:1,show: true },
          { value: "", columnName: "D", editable: false, position: [0, 3] , row:1 ,show: true},
          { value: "", columnName: "E", editable: false, position: [0, 4] , row:1,show: true },
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [1, 0], row:2 ,show: true },
          { value: "", columnName: "B", editable: false, position: [1, 1], row:2  ,show: true},
          { value: "", columnName: "C", editable: false, position: [1, 2], row:2 ,show: true },
          { value: "", columnName: "D", editable: false, position: [1, 3], row:2 ,show: true },
          { value: "", columnName: "E", editable: false, position: [1, 4], row:2 ,show: true },
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [2, 0], row:3 ,show: true },
          { value: "", columnName: "B", editable: false, position: [2, 1], row:3 ,show: true },
          { value: "", columnName: "C", editable: false, position: [2, 2], row:3 ,show: true },
          { value: "", columnName: "D", editable: false, position: [2, 3], row:3,show: true  },
          { value: "", columnName: "E", editable: false, position: [2, 4], row:3 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [3, 0] , row:4,show: true },
          { value: "", columnName: "B", editable: false, position: [3, 1] , row:4,show: true },
          { value: "", columnName: "C", editable: false, position: [3, 2] , row:4,show: true },
          { value: "", columnName: "D", editable: false, position: [3, 3] , row:4,show: true },
          { value: "", columnName: "E", editable: false, position: [3, 4] , row:4,show: true },
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [4, 0], row:5 ,show: true },
          { value: "", columnName: "B", editable: false, position: [4, 1], row:5 ,show: true },
          { value: "", columnName: "C", editable: false, position: [4, 2], row:5 ,show: true },
          { value: "", columnName: "D", editable: false, position: [4, 3], row:5 ,show: true },
          { value: "", columnName: "E", editable: false, position: [4, 4], row:5 ,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [5, 0], row:6,show: true  },
          { value: "", columnName: "B", editable: false, position: [5, 1], row:6 ,show: true },
          { value: "", columnName: "C", editable: false, position: [5, 2], row:6 ,show: true },
          { value: "", columnName: "D", editable: false, position: [5, 3], row:6 ,show: true },
          { value: "", columnName: "E", editable: false, position: [5, 4], row:6 ,show: true },
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [6, 0], row:7 ,show: true },
          { value: "", columnName: "B", editable: false, position: [6, 1], row:7 ,show: true },
          { value: "", columnName: "C", editable: false, position: [6, 2], row:7 ,show: true },
          { value: "", columnName: "D", editable: false, position: [6, 3], row:7,show: true  },
          { value: "", columnName: "E", editable: false, position: [6, 4], row:7,show: true  },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [7, 0], row:8 ,show: true },
          { value: "", columnName: "B", editable: false, position: [7, 1], row:8 ,show: true },
          { value: "", columnName: "C", editable: false, position: [7, 2], row:8 ,show: true },
          { value: "", columnName: "D", editable: false, position: [7, 3], row:8 ,show: true },
          { value: "", columnName: "E", editable: false, position: [7, 4], row:8 ,show: true },
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [8, 0], row:9 ,show: true },
          { value: "", columnName: "B", editable: false, position: [8, 1], row:9  ,show: true},
          { value: "", columnName: "C", editable: false, position: [8, 2], row:9 ,show: true },
          { value: "", columnName: "D", editable: false, position: [8, 3], row:9 ,show: true },
          { value: "", columnName: "E", editable: false, position: [8, 4], row:9,show: true  },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [9, 0], row:10 ,show: true },
          { value: "", columnName: "B", editable: false, position: [9, 1], row:10,show: true  },
          { value: "", columnName: "C", editable: false, position: [9, 2], row:10,show: true  },
          { value: "", columnName: "D", editable: false, position: [9, 3], row:10 ,show: true },
          { value: "", columnName: "E", editable: false, position: [9, 4], row:10 ,show: true },
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [10, 0], row:11 ,show: true},
          { value: "", columnName: "B", editable: false, position: [10, 1], row:11,show: true },
          { value: "", columnName: "C", editable: false, position: [10, 2], row:11,show: true },
          { value: "", columnName: "D", editable: false, position: [10, 3], row:11 ,show: true},
          { value: "", columnName: "E", editable: false, position: [10, 4], row:11,show: true },
        ],
        [
          { value: "", columnName: "A", editable: false, position: [11, 0], row:12,show: true },
          { value: "", columnName: "B", editable: false, position: [11, 1], row:12 ,show: true},
          { value: "", columnName: "C", editable: false, position: [11, 2], row:12,show: true },
          { value: "", columnName: "D", editable: false, position: [11, 3], row:12 ,show: true},
          { value: "", columnName: "E", editable: false, position: [11, 4], row:12 ,show: true},
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [12, 0], row:13,show: true },
          { value: "", columnName: "B", editable: false, position: [12, 1], row:13,show: true },
          { value: "", columnName: "C", editable: false, position: [12, 2], row:13 ,show: true},
          { value: "", columnName: "D", editable: false, position: [12, 3], row:13,show: true },
          { value: "", columnName: "E", editable: false, position: [12, 4], row:13 ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [13, 0], row:14 ,show: true},
          { value: "", columnName: "B", editable: false, position: [13, 1], row:14 ,show: true},
          { value: "", columnName: "C", editable: false, position: [13, 2], row:14 ,show: true},
          { value: "", columnName: "D", editable: false, position: [13, 3], row:14,show: true },
          { value: "", columnName: "E", editable: false, position: [13, 4], row:14 ,show: true},
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [14, 0], row:15 ,show: true},
          { value: "", columnName: "B", editable: false, position: [14, 1], row:15 ,show: true},
          { value: "", columnName: "C", editable: false, position: [14, 2], row:15,show: true },
          { value: "", columnName: "D", editable: false, position: [14, 3], row:15,show: true },
          { value: "", columnName: "E", editable: false, position: [14, 4], row:15 ,show: true},
        ],
        [
          { value: "", columnName: "A", editable: false, position: [15, 0], row:16,show: true },
          { value: "", columnName: "B", editable: false, position: [15, 1], row:16,show: true },
          { value: "", columnName: "C", editable: false, position: [15, 2], row:16,show: true },
          { value: "", columnName: "D", editable: false, position: [15, 3], row:16 ,show: true},
          { value: "", columnName: "E", editable: false, position: [15, 4], row:16 ,show: true},
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [16, 0], row:17,show: true },
          { value: "", columnName: "B", editable: false, position: [16, 1], row:17,show: true },
          { value: "", columnName: "C", editable: false, position: [16, 2], row:17,show: true },
          { value: "", columnName: "D", editable: false, position: [16, 3], row:17,show: true },
          { value: "", columnName: "E", editable: false, position: [16, 4], row:17 ,show: true},
        ],
  
        [
          { value: "", columnName: "A", editable: false, position: [17, 0], row:18,show: true },
          { value: "", columnName: "B", editable: false, position: [17, 1], row:18 ,show: true},
          { value: "", columnName: "C", editable: false, position: [17, 2], row:18,show: true },
          { value: "", columnName: "D", editable: false, position: [17, 3], row:18,show: true },
          { value: "", columnName: "E", editable: false, position: [17, 4], row:18 ,show: true},
        ],
      ],
    },
  ]
}){
    id,
    name,
    tables{
      id,
      name,
      show,
      sheetNumber
      serviceId
      data{
        value,
        editable,
        columnName,
        row,
        position,
        show
        
      }
    }
  }
}
