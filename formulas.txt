-- 9122ec83-7fc2-4985-bbb1-b135242b39b7
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[9,0]","=SUMA( (1/tabla2!B3 * B10 )-(tabla2!B4/tabla2!B3) )","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[9,1]","=SUMA((tabla2!B2+(tabla2!B1*(tabla2!B4/tabla2!B3))))/(tabla2!B1*(1/tabla2!B3)+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[9,2]","=SUMA(tabla2!B4+(tabla2!B3*A10))","9122ec83-7fc2-4985-bbb1-b135242b39b7");


insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[8,0]","=SUMA(A10+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[7,0]","=SUMA(A9+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[6,0]","=SUMA(A8+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[5,0]","=SUMA(A7+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[4,0]","=SUMA(A6+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[3,0]","=SUMA(A5+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[2,0]","=SUMA(A4+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[1,0]","=SUMA(A3+1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[10,0]","=SUMA(A10-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[11,0]","=SUMA(A11-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[12,0]","=SUMA(A12-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[13,0]","=SUMA(A13-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[14,0]","=SUMA(A14-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[15,0]","=SUMA(A15-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[16,0]","=SUMA(A16-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[17,0]","=SUMA(A17-1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");


-- Qxd1
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[8,1]","=SUMA(B10-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[7,1]","=SUMA(B9-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[6,1]","=SUMA(B8-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[5,1]","=SUMA(B7-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[4,1]","=SUMA(B6-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[3,1]","=SUMA(B5-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[2,1]","=SUMA(B4-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[1,1]","=SUMA(B3-tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");


insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[10,1]","=SUMA(B10+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[11,1]","=SUMA(B11+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[12,1]","=SUMA(B12+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[13,1]","=SUMA(B13+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[14,1]","=SUMA(B14+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[15,1]","=SUMA(B15+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[16,1]","=SUMA(B16+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[17,1]","=SUMA(B17+tabla2!B1)","9122ec83-7fc2-4985-bbb1-b135242b39b7");




-- Qxo

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[8,2]","=SUMA(C10+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[7,2]","=SUMA(C9+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[6,2]","=SUMA(C8+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[5,2]","=SUMA(C7+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[4,2]","=SUMA(C6+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[3,2]","=SUMA(C5+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[2,2]","=SUMA(C4+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[1,2]","=SUMA(C3+tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[10,2]","=SUMA(C10-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[11,2]","=SUMA(C11-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[12,2]","=SUMA(C12-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[13,2]","=SUMA(C13-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[14,2]","=SUMA(C14-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[15,2]","=SUMA(C15-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[16,2]","=SUMA(C16-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla3","[17,2]","=SUMA(C17-tabla2!B3)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

-- Ex. Con.	TABLE 4
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla4","[0,1]","=SUMA(( tabla2!B2- tabla3!A10)*tabla3!B10)/2","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla4","[1,1]","=SUMA((tabla3!A10*tabla3!B10)/2)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla4","[2,1]","=SUMA(tabla2!B1*(tabla3!A10/tabla3!B10))","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla4","[3,1]","=SUMA(tabla2!B3*(tabla3!A10/tabla3!B10))","9122ec83-7fc2-4985-bbb1-b135242b39b7");

-- Gt = It (It empresa) 

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla5","[0,1]","=SUMA(tabla3!A10*tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

-- tabla oculta
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[1,0]","36","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[1,1]","52","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[1,2]","92","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[2,0]","35","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[2,1]","55","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[2,2]","90","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[3,0]","34","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[3,1]","58","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[3,2]","88","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[4,0]","33","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[4,1]","61","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[4,2]","86","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[5,0]","32","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[5,1]","64","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[5,2]","84","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[6,0]","31","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[6,1]","67","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[6,2]","82","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[7,0]","30","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[7,1]","70","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[7,2]","80","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[8,0]","29","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[8,1]","73","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[8,2]","78","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[9,0]","28","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[9,1]","76","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[9,2]","76","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[10,0]","27","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[10,1]","79","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[10,2]","74","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[11,0]","26","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[11,1]","82","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[11,2]","72","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[12,0]","25","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[12,1]","85","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[12,2]","70","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[13,0]","24","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[13,1]","88","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[13,2]","68","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[14,0]","23","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[14,1]","91","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[14,2]","66","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[15,0]","22","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[15,1]","94","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[15,2]","64","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[16,0]","21","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[16,1]","97","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[16,2]","62","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[17,0]","20","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[17,1]","100","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[17,2]","60","9122ec83-7fc2-4985-bbb1-b135242b39b7");

-- formulas ocultas
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[1,3]","=suma(tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[1,4]","0","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[2,3]","=suma(tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[2,4]","=suma(tabla3!A10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[3,3]","=suma(tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[3,4]","=suma(tabla3!A10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[4,3]","0","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[4,4]","=suma(tabla3!A10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");


-- --
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[11,3]","=suma(tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[11,4]","0","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[12,3]","=suma(tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[12,4]","=suma(tabla3!A10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[13,3]","=suma(tabla3!B10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[13,4]","=suma(tabla3!A10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");

insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[14,3]","0","9122ec83-7fc2-4985-bbb1-b135242b39b7");
insert into formula(id,name,position,formula, simulatorId) values(uuid(),"tabla6","[14,4]","=suma(tabla3!A10)","9122ec83-7fc2-4985-bbb1-b135242b39b7");