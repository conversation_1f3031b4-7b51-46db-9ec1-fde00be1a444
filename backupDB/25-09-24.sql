create table bank_request_log
(
    createdAt    timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt    timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt    timestamp(6)                              null,
    id           varchar(36)                               not null
        primary key,
    bank_name    enum ('BNB')                              not null,
    api_endpoint varchar(255)                              not null,
    type_log     enum ('REQUEST', 'RESPONSE', 'ERROR')     not null,
    show_message text                                      null,
    data         text                                      null
);

create table book
(
    createdAt   timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt   timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt   timestamp(6)                              null,
    id          varchar(36)                               not null
        primary key,
    title       varchar(255)                              not null,
    author      varchar(255)                              not null,
    description varchar(900) default ''                   not null,
    code        varchar(10)                               not null
);

create table comment
(
    createdAt  timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt  timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt  timestamp(6)                              null,
    id         varchar(36)                               not null
        primary key,
    name       varchar(255)                              null,
    mail       varchar(255)                              null,
    regionCode varchar(255)                              null,
    cellphone  varchar(255)                              null,
    affair     varchar(255)                              null,
    message    varchar(255)                              null
);

create table coupon
(
    createdAt          timestamp(6)      default CURRENT_TIMESTAMP(6) not null,
    updatedAt          timestamp(6)      default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt          timestamp(6)                                   null,
    id                 varchar(36)                                    not null
        primary key,
    name               varchar(255)                                   not null,
    dateStart          timestamp                                      null,
    dateEnd            timestamp                                      null,
    quantity           int               default 0                    not null,
    usedQuantity       int               default 0                    not null,
    unlimited          tinyint           default 0                    not null,
    discountPercentage double                                         not null,
    isEnable           tinyint                                        not null,
    isAllBooks         enum ('SI', 'NO') default 'NO'                 not null
);

create table coupon_book_personalized
(
    id       varchar(36) not null
        primary key,
    bookId   varchar(36) null,
    couponId varchar(36) null,
    constraint FK_bfcf7b212c3e31935f75a9b58d1
        foreign key (couponId) references coupon (id),
    constraint FK_e30d83b9ed2e5d028f09a8981e0
        foreign key (bookId) references book (id)
);

create table email_log
(
    createdAt      timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt      timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt      timestamp(6)                              null,
    id             int auto_increment
        primary key,
    recipientEmail varchar(255)                              not null,
    senderEmail    varchar(255)                              not null,
    emailType      varchar(100)                              not null,
    emailSubject   varchar(255)                              not null,
    sendDate       timestamp    default CURRENT_TIMESTAMP    not null,
    status         varchar(50)                               not null,
    errorMessage   text                                      not null,
    additionalInfo text                                      null
);

create table files
(
    createdAt    timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt    timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt    timestamp(6)                              null,
    id           varchar(36)                               not null
        primary key,
    filename     varchar(255)                              null,
    originalname varchar(255)                              null
);

create table attached_files
(
    createdAt    timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt    timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt    timestamp(6)                              null,
    id           int auto_increment
        primary key,
    nombre_tabla enum ('versiones')                        not null,
    id_registro  varchar(255)                              not null,
    file_id      varchar(255)                              not null,
    constraint FK_29bf753ad59eb964cb1d524cda1
        foreign key (file_id) references files (id)
);

create table migrations
(
    id        int auto_increment
        primary key,
    timestamp bigint       not null,
    name      varchar(255) not null
);

create table people
(
    createdAt           timestamp(6) default CURRENT_TIMESTAMP(6)      not null,
    updatedAt           timestamp(6) default CURRENT_TIMESTAMP(6)      not null on update CURRENT_TIMESTAMP(6),
    deletedAt           timestamp(6)                                   null,
    id                  varchar(36)                                    not null
        primary key,
    name                varchar(255) default ''                        null,
    lastName            varchar(255) default ''                        null,
    birthDate           timestamp                                      null,
    photoProfile        varchar(255) default '/images/defaultUser.png' not null,
    email               varchar(255)                                   not null,
    codeVerification    varchar(255)                                   null,
    emailVerified       tinyint      default 0                         not null,
    cellPhone           varchar(20)                                    null,
    regionCode          varchar(10)                                    null,
    academicLevel       varchar(255)                                   null,
    country             varchar(255)                                   null,
    city                varchar(255)                                   null,
    companyInstitution  varchar(255)                                   null,
    studentProfessional varchar(255)                                   null,
    constraint IDX_c77e8752faa45901af2b245dff
        unique (email)
);

create table credentials
(
    createdAt timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt timestamp(6)                              null,
    id        varchar(36)                               not null
        primary key,
    password  varchar(255)                              null,
    peopleId  varchar(36)                               null,
    constraint IDX_627771ae2398aa7973a048446b
        unique (peopleId),
    constraint REL_627771ae2398aa7973a048446b
        unique (peopleId),
    constraint FK_627771ae2398aa7973a048446b1
        foreign key (peopleId) references people (id)
);

create table roles
(
    createdAt timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt timestamp(6)                              null,
    id        int auto_increment
        primary key,
    nombre    varchar(255) default ''                   null,
    cod_rol   varchar(255) default ''                   null
);

create table admin
(
    createdAt timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt timestamp(6)                              null,
    id        varchar(36)                               not null
        primary key,
    rolId     int                                       null,
    peopleId  varchar(36)                               null,
    constraint IDX_ffdc55d46defbd53c907bde755
        unique (peopleId),
    constraint REL_ffdc55d46defbd53c907bde755
        unique (peopleId),
    constraint FK_c94378920677136eb6dcc03ef6d
        foreign key (rolId) references roles (id),
    constraint FK_ffdc55d46defbd53c907bde7553
        foreign key (peopleId) references people (id)
);

create table shopping
(
    createdAt                 timestamp(6)                                                                                    default CURRENT_TIMESTAMP(6) not null,
    updatedAt                 timestamp(6)                                                                                    default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt                 timestamp(6)                                                                                                                 null,
    id                        varchar(36)                                                                                                                  not null
        primary key,
    proofOfpayment            text                                                                                                                         null,
    amountPay                 decimal(19, 2)                                                                                                               not null,
    qrInfo                    text                                                                                                                         null,
    qrBody                    text                                                                                                                         null,
    qrPaymentBankNotification text                                                                                                                         null,
    paymentMade               tinyint                                                                                                                      null,
    paymentStatus             enum ('', 'QR_GENERADO', 'COMPROBANTE_EN_REVISIÓN', 'COMPROBANTE_RECHAZADO', 'PAGO_CONFIRMADO') default ''                   not null,
    paymentRejectionReason    text                                                                                                                         null,
    peopleId                  varchar(36)                                                                                                                  null,
    couponId                  varchar(36)                                                                                                                  null,
    constraint FK_7a7900215ef4cbb33a2b461d6bd
        foreign key (peopleId) references people (id),
    constraint FK_c0db8569f362470e033cb3117d3
        foreign key (couponId) references coupon (id)
);

create table subscription_plan
(
    createdAt     timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt     timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt     timestamp(6)                              null,
    id            varchar(36)                               not null
        primary key,
    name          varchar(255)                              not null,
    duration_days int          default 0                    not null
);

create table version
(
    createdAt             timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt             timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt             timestamp(6)                              null,
    id                    varchar(36)                               not null
        primary key,
    coverPath             varchar(255)                              not null,
    bookPath              varchar(255)                              not null,
    versionActive         tinyint                                   not null,
    is_books_pending_send tinyint      default 0                    not null,
    version               varchar(10)                               not null,
    bookId                varchar(255)                              not null,
    constraint FK_3800f23da5925c05532c0b17921
        foreign key (bookId) references book (id)
);

create table chapter
(
    createdAt     timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt     timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt     timestamp(6)                              null,
    id            varchar(36)                               not null
        primary key,
    name          varchar(255)                              null,
    chapterNumber int                                       null,
    open          tinyint      default 0                    not null,
    versionId     varchar(36)                               null,
    constraint FK_a6cb14bb8a66eb52c61b51cf284
        foreign key (versionId) references version (id)
);

create table simulator
(
    createdAt          timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt          timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt          timestamp(6)                              null,
    id                 varchar(36)                               not null
        primary key,
    name               varchar(255)                              not null,
    isPublic           tinyint                                   not null,
    coverSimulatorPath varchar(255)                              null,
    simulatorNumber    int                                       null,
    chapterId          varchar(36)                               null,
    constraint FK_d8818148f33aa681546db6e71d9
        foreign key (chapterId) references chapter (id)
);

create table formula
(
    createdAt   timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt   timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt   timestamp(6)                              null,
    id          varchar(36)                               not null
        primary key,
    position    json                                      not null,
    formula     text                                      not null,
    name        varchar(255)                              not null,
    simulatorId varchar(36)                               null,
    constraint FK_c8372f311db0fa91ad5f6eaf025
        foreign key (simulatorId) references simulator (id)
);

create table sheet
(
    createdAt   timestamp(6)                                                default CURRENT_TIMESTAMP(6) not null,
    updatedAt   timestamp(6)                                                default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt   timestamp(6)                                                                             null,
    id          varchar(36)                                                                              not null
        primary key,
    name        varchar(255)                                                                             not null,
    sheetNumber int                                                                                      not null,
    type        enum ('static', 'n_rows_input_data', 'n_rows_calculations') default 'static'             not null,
    data        json                                                                                     not null,
    `show`      tinyint                                                                                  not null,
    orderNumber int                                                                                      null,
    simulatorId varchar(36)                                                                              null,
    constraint FK_3567ec32eb4638268782b6e26bc
        foreign key (simulatorId) references simulator (id)
);

create table version_subscription_plans
(
    createdAt          timestamp(6)   default CURRENT_TIMESTAMP(6) not null,
    updatedAt          timestamp(6)   default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt          timestamp(6)                                null,
    id                 varchar(36)                                 not null
        primary key,
    price              decimal(10, 2) default 0.00                 not null,
    versionId          varchar(255)                                not null,
    subscriptionPlanId varchar(255)                                not null,
    constraint FK_c394c4d4c848f5fa86c4e41fe0e
        foreign key (versionId) references version (id),
    constraint FK_e4adb53a07bdbda2a5d4039aa8d
        foreign key (subscriptionPlanId) references subscription_plan (id)
);

create table products
(
    createdAt                   timestamp(6) default CURRENT_TIMESTAMP(6) not null,
    updatedAt                   timestamp(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
    deletedAt                   timestamp(6)                              null,
    id                          varchar(36)                               not null
        primary key,
    price                       decimal(19, 2)                            null,
    priceWithDiscount           decimal(19, 2)                            null,
    amount                      float                                     null,
    dateStart                   timestamp                                 null,
    dateEnd                     timestamp                                 null,
    shoppingId                  varchar(36)                               null,
    versionId                   varchar(36)                               null,
    versionSubscriptionsPlansId varchar(36)                               null,
    constraint FK_401e25a1fecf6197c2fe7f2a952
        foreign key (shoppingId) references shopping (id),
    constraint FK_52ed1b140015197cf4691e93825
        foreign key (versionId) references version (id),
    constraint FK_8078e2e667265087617c4905339
        foreign key (versionSubscriptionsPlansId) references version_subscription_plans (id)
);


