-- MySQL dump 10.13  Distrib 8.0.32, for Win64 (x86_64)
--
-- Host: localhost    Database: economixhubdb
-- ------------------------------------------------------
-- Server version	8.0.32

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin`
--

DROP TABLE IF EXISTS `admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin` (
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `peopleId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_ffdc55d46defbd53c907bde755` (`peopleId`),
  UNIQUE KEY `REL_ffdc55d46defbd53c907bde755` (`peopleId`),
  CONSTRAINT `FK_ffdc55d46defbd53c907bde7553` FOREIGN KEY (`peopleId`) REFERENCES `people` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `book`
--

DROP TABLE IF EXISTS `book`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `title` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `author` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon`
--

DROP TABLE IF EXISTS `coupon`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `coupon` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `dateStart` timestamp NULL DEFAULT NULL,
  `dateEnd` timestamp NULL DEFAULT NULL,
  `quantity` int NOT NULL DEFAULT '0',
  `discountPercentage` double NOT NULL,
  `isEnable` tinyint NOT NULL,
  `unlimited` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `credentials`
--

DROP TABLE IF EXISTS `credentials`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `credentials` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `password` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `peopleId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_627771ae2398aa7973a048446b` (`peopleId`),
  UNIQUE KEY `REL_627771ae2398aa7973a048446b` (`peopleId`),
  CONSTRAINT `FK_627771ae2398aa7973a048446b1` FOREIGN KEY (`peopleId`) REFERENCES `people` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `formula`
--

DROP TABLE IF EXISTS `formula`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `formula` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `position` json NOT NULL,
  `formula` text COLLATE utf8mb3_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `simulatorId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_c8372f311db0fa91ad5f6eaf025` (`simulatorId`),
  CONSTRAINT `FK_c8372f311db0fa91ad5f6eaf025` FOREIGN KEY (`simulatorId`) REFERENCES `simulator` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `timestamp` bigint NOT NULL,
  `name` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `people`
--

DROP TABLE IF EXISTS `people`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `people` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `lastName` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `birthDate` timestamp NULL DEFAULT NULL,
  `photoProfile` varchar(255) COLLATE utf8mb3_bin NOT NULL DEFAULT '/images/defaultUser.png',
  `email` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `codeVerification` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `emailVerified` tinyint NOT NULL DEFAULT '0',
  `cellPhone` varchar(20) COLLATE utf8mb3_bin DEFAULT NULL,
  `regionCode` varchar(10) COLLATE utf8mb3_bin DEFAULT NULL,
  `academicLevel` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `companyInstitution` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  `studentProfessional` varchar(255) COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_c77e8752faa45901af2b245dff` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `shoppingId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  `versionId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  `price` float DEFAULT NULL,
  `priceWithDiscount` float DEFAULT NULL,
  `amount` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_401e25a1fecf6197c2fe7f2a952` (`shoppingId`),
  KEY `FK_52ed1b140015197cf4691e93825` (`versionId`),
  CONSTRAINT `FK_401e25a1fecf6197c2fe7f2a952` FOREIGN KEY (`shoppingId`) REFERENCES `shopping` (`id`),
  CONSTRAINT `FK_52ed1b140015197cf4691e93825` FOREIGN KEY (`versionId`) REFERENCES `version` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sheet`
--

DROP TABLE IF EXISTS `sheet`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sheet` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `sheetNumber` int NOT NULL,
  `data` json NOT NULL,
  `show` tinyint NOT NULL,
  `orderNumber` int DEFAULT NULL,
  `simulatorId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_3567ec32eb4638268782b6e26bc` (`simulatorId`),
  CONSTRAINT `FK_3567ec32eb4638268782b6e26bc` FOREIGN KEY (`simulatorId`) REFERENCES `simulator` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shopping`
--

DROP TABLE IF EXISTS `shopping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shopping` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `proofOfpayment` double DEFAULT NULL,
  `amountPay` double NOT NULL,
  `peopleId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  `couponId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  `dateStart` timestamp NULL DEFAULT NULL,
  `dateEnd` timestamp NULL DEFAULT NULL,
  `qrInfo` json DEFAULT NULL,
  `paymentMade` tinyint DEFAULT NULL,
  `qrBody` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_7a7900215ef4cbb33a2b461d6bd` (`peopleId`),
  KEY `FK_c0db8569f362470e033cb3117d3` (`couponId`),
  CONSTRAINT `FK_7a7900215ef4cbb33a2b461d6bd` FOREIGN KEY (`peopleId`) REFERENCES `people` (`id`),
  CONSTRAINT `FK_c0db8569f362470e033cb3117d3` FOREIGN KEY (`couponId`) REFERENCES `coupon` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `simulator`
--

DROP TABLE IF EXISTS `simulator`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `simulator` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `isPublic` tinyint NOT NULL,
  `bookId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_9a378c349455eb29584a3fe76d` (`name`),
  KEY `FK_4cb0a58b3724971007f2755146e` (`bookId`),
  CONSTRAINT `FK_4cb0a58b3724971007f2755146e` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `version`
--

DROP TABLE IF EXISTS `version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `version` (
  `createdAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` timestamp(6) NULL DEFAULT NULL,
  `id` varchar(36) COLLATE utf8mb3_bin NOT NULL,
  `price` double NOT NULL,
  `coverPath` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `bookPath` varchar(255) COLLATE utf8mb3_bin NOT NULL,
  `versionActive` tinyint NOT NULL,
  `edition` int NOT NULL DEFAULT '1',
  `bookId` varchar(36) COLLATE utf8mb3_bin DEFAULT NULL,
  `daysSubscription` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_3800f23da5925c05532c0b17921` (`bookId`),
  CONSTRAINT `FK_3800f23da5925c05532c0b17921` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-10-02 16:02:02
